import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { ChatAccessComponent } from './components/chat-access/chat-access.component';
import { DoctorAvailabilityComponent } from './components/doctor-availability/doctor-availability.component';
import { NotificationBellComponent } from './components/notification-bell/notification-bell.component';
import { QuickChatWidgetComponent } from './components/quick-chat-widget/quick-chat-widget.component';
import { LanguageSelectorComponent } from './components/language-selector/language-selector.component';
import { InsuranceCoverageComponent } from './components/insurance-coverage/insurance-coverage.component';

@NgModule({
  declarations: [
    ChatAccessComponent,
    DoctorAvailabilityComponent,
    NotificationBellComponent,
    QuickChatWidgetComponent,
    LanguageSelectorComponent,
    InsuranceCoverageComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    HttpClientModule
  ],
  exports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    HttpClientModule,
    ChatAccessComponent,
    DoctorAvailabilityComponent,
    NotificationBellComponent,
    QuickChatWidgetComponent,
    LanguageSelectorComponent,
    InsuranceCoverageComponent
  ]
})
export class SharedModule { }
