.insurance-coverage {
  background: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

.insurance-coverage.compact {
  padding: 1rem;
  background: transparent;
}

.coverage-card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.coverage-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.coverage-card .card-body {
  padding: 1rem;
}

.coverage-percentage {
  font-weight: 600;
  font-size: 1.25rem;
}

.coverage-details {
  margin-top: 0.5rem;
}

.progress {
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.progress-bar {
  transition: width 0.6s ease;
}

.patient-info {
  background: white;
  border-radius: 0.375rem;
  padding: 1rem;
  border: 1px solid #dee2e6;
}

/* Coverage level indicators */
.text-excellent {
  color: #28a745 !important;
}

.text-good {
  color: #17a2b8 !important;
}

.text-fair {
  color: #ffc107 !important;
}

.text-poor {
  color: #dc3545 !important;
}

.text-none {
  color: #6c757d !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .insurance-coverage {
    padding: 1rem;
  }
  
  .coverage-card .card-body {
    padding: 0.75rem;
  }
  
  .coverage-percentage {
    font-size: 1.1rem;
  }
  
  .row.g-3 {
    --bs-gutter-x: 0.75rem;
    --bs-gutter-y: 0.75rem;
  }
}

/* Animation for loading */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

/* Compact mode adjustments */
.insurance-coverage.compact .coverage-card {
  margin-bottom: 0.5rem;
}

.insurance-coverage.compact .coverage-card .card-body {
  padding: 0.75rem;
}

.insurance-coverage.compact .coverage-percentage {
  font-size: 1rem;
}

.insurance-coverage.compact h6 {
  font-size: 0.875rem;
}

/* Icon styling */
.fas {
  width: 1.2em;
  text-align: center;
}

/* Alert styling */
.alert {
  border: none;
  border-radius: 0.375rem;
}

/* Button styling */
.btn-outline-secondary {
  border-color: #dee2e6;
}

.btn-outline-secondary:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
}

/* Progress bar animations */
@keyframes progressAnimation {
  0% {
    width: 0%;
  }
  100% {
    width: var(--progress-width);
  }
}

.progress-bar {
  animation: progressAnimation 1s ease-in-out;
}

/* Card hover effects */
.coverage-card {
  cursor: default;
  position: relative;
  overflow: hidden;
}

.coverage-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.coverage-card:hover::before {
  left: 100%;
}
