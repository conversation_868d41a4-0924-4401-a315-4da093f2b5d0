<div class="insurance-coverage" [class.compact]="compact">
  <!-- Title -->
  <div class="d-flex justify-content-between align-items-center mb-3" *ngIf="showTitle">
    <h5 class="mb-0">
      <i class="fas fa-shield-alt text-primary me-2"></i>
      {{ i18nService.translateSync('insurance') }} {{ i18nService.translateSync('coverage') }}
    </h5>
    <button 
      class="btn btn-sm btn-outline-secondary" 
      (click)="refresh()" 
      [disabled]="loading"
      title="Refresh coverage information">
      <i class="fas fa-sync-alt" [class.fa-spin]="loading"></i>
    </button>
  </div>

  <!-- Loading State -->
  <div class="text-center py-4" *ngIf="loading && !coverageSummary">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">{{ i18nService.translateSync('loading') }}</span>
    </div>
    <p class="mt-2 text-muted">{{ i18nService.translateSync('loading') }}</p>
  </div>

  <!-- Error State -->
  <div class="alert alert-warning" *ngIf="error && !coverageSummary">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{ error }}
    <button class="btn btn-sm btn-outline-warning ms-2" (click)="refresh()">
      {{ i18nService.translateSync('retry') }}
    </button>
  </div>

  <!-- Coverage Summary -->
  <div *ngIf="coverageSummary" class="coverage-summary">
    <!-- Patient Info -->
    <div class="patient-info mb-3" *ngIf="!compact">
      <div class="d-flex align-items-center">
        <i class="fas fa-user-circle text-secondary me-2"></i>
        <div>
          <strong>{{ coverageSummary.patientName }}</strong>
          <small class="text-muted d-block">
            {{ i18nService.translateSync('patient') }} ID: {{ coverageSummary.patientId }}
          </small>
        </div>
      </div>
    </div>

    <!-- Coverage Cards -->
    <div class="row g-3">
      <!-- Prescription Coverage -->
      <div class="col-md-4">
        <div class="coverage-card card h-100">
          <div class="card-body">
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-pills text-primary me-2"></i>
              <h6 class="mb-0">{{ getCoverageText('prescription') }}</h6>
            </div>
            
            <div class="coverage-details">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="coverage-percentage h5 mb-0" 
                      [class]="'text-' + getCoverageLevelColor(coverageSummary.prescriptionCoverage)">
                  {{ formatCoverage(coverageSummary.prescriptionCoverage) }}
                </span>
                <i [class]="getCoverageIcon(coverageSummary.prescriptionCoverage)"
                   [class]="'text-' + getCoverageLevelColor(coverageSummary.prescriptionCoverage)"></i>
              </div>
              
              <div class="progress mb-2" style="height: 6px;">
                <div class="progress-bar" 
                     [class]="getProgressBarClass(coverageSummary.prescriptionCoverage)"
                     [style.width.%]="getProgressBarWidth(coverageSummary.prescriptionCoverage)">
                </div>
              </div>
              
              <small class="text-muted" *ngIf="!compact">
                {{ getRecommendation(coverageSummary.prescriptionCoverage) }}
              </small>
            </div>
          </div>
        </div>
      </div>

      <!-- Consultation Coverage -->
      <div class="col-md-4">
        <div class="coverage-card card h-100">
          <div class="card-body">
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-video text-success me-2"></i>
              <h6 class="mb-0">{{ getCoverageText('consultation') }}</h6>
            </div>
            
            <div class="coverage-details">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="coverage-percentage h5 mb-0" 
                      [class]="'text-' + getCoverageLevelColor(coverageSummary.consultationCoverage)">
                  {{ formatCoverage(coverageSummary.consultationCoverage) }}
                </span>
                <i [class]="getCoverageIcon(coverageSummary.consultationCoverage)"
                   [class]="'text-' + getCoverageLevelColor(coverageSummary.consultationCoverage)"></i>
              </div>
              
              <div class="progress mb-2" style="height: 6px;">
                <div class="progress-bar" 
                     [class]="getProgressBarClass(coverageSummary.consultationCoverage)"
                     [style.width.%]="getProgressBarWidth(coverageSummary.consultationCoverage)">
                </div>
              </div>
              
              <small class="text-muted" *ngIf="!compact">
                {{ getRecommendation(coverageSummary.consultationCoverage) }}
              </small>
            </div>
          </div>
        </div>
      </div>

      <!-- Appointment Coverage -->
      <div class="col-md-4">
        <div class="coverage-card card h-100">
          <div class="card-body">
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-calendar-check text-info me-2"></i>
              <h6 class="mb-0">{{ getCoverageText('appointment') }}</h6>
            </div>
            
            <div class="coverage-details">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="coverage-percentage h5 mb-0" 
                      [class]="'text-' + getCoverageLevelColor(coverageSummary.appointmentCoverage)">
                  {{ formatCoverage(coverageSummary.appointmentCoverage) }}
                </span>
                <i [class]="getCoverageIcon(coverageSummary.appointmentCoverage)"
                   [class]="'text-' + getCoverageLevelColor(coverageSummary.appointmentCoverage)"></i>
              </div>
              
              <div class="progress mb-2" style="height: 6px;">
                <div class="progress-bar" 
                     [class]="getProgressBarClass(coverageSummary.appointmentCoverage)"
                     [style.width.%]="getProgressBarWidth(coverageSummary.appointmentCoverage)">
                </div>
              </div>
              
              <small class="text-muted" *ngIf="!compact">
                {{ getRecommendation(coverageSummary.appointmentCoverage) }}
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Last Updated -->
    <div class="text-center mt-3" *ngIf="!compact">
      <small class="text-muted">
        <i class="fas fa-clock me-1"></i>
        {{ i18nService.translateSync('last_updated') }}: 
        {{ coverageSummary.lastUpdated | date:'medium' }}
      </small>
    </div>
  </div>
</div>
