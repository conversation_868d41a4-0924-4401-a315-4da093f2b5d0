.language-selector {
  position: relative;
}

.language-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: all 0.15s ease-in-out;
}

.language-btn:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
}

.language-btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.flag {
  font-size: 1.1em;
  line-height: 1;
}

.language-name {
  font-weight: 500;
}

.language-code {
  font-size: 0.75rem;
  color: #6c757d;
}

.dropdown-menu {
  min-width: 200px;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  z-index: 1050;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  color: #212529;
  text-decoration: none;
  transition: background-color 0.15s ease-in-out;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #16181b;
}

.dropdown-item.active {
  background-color: #007bff;
  color: white;
}

.dropdown-item.active .language-code {
  color: rgba(255, 255, 255, 0.8);
}

.dropdown-item .language-name {
  flex: 1;
  font-weight: 500;
}

.dropdown-item .language-code {
  font-size: 0.75rem;
  color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .language-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
  }
  
  .dropdown-menu {
    min-width: 150px;
  }
  
  .dropdown-item {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
  }
}

/* Animation for dropdown */
.dropdown-menu {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.15s ease, transform 0.15s ease;
  pointer-events: none;
}

.dropdown-menu.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}
