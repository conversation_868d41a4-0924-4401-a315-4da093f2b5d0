/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ɵ$localize as $localize } from '@angular/localize';
export { $localize };
// Attach $localize to the global context, as a side-effect of this module.
globalThis.$localize = $localize;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9sb2NhbGl6ZS9pbml0L2luZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUNILE9BQU8sRUFBQyxVQUFVLElBQUksU0FBUyxFQUF5RCxNQUFNLG1CQUFtQixDQUFDO0FBRWxILE9BQU8sRUFBQyxTQUFTLEVBQTBCLENBQUM7QUFFNUMsMkVBQTJFO0FBQzFFLFVBQWtCLENBQUMsU0FBUyxHQUFHLFNBQVMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuaW1wb3J0IHvJtSRsb2NhbGl6ZSBhcyAkbG9jYWxpemUsIMm1TG9jYWxpemVGbiBhcyBMb2NhbGl6ZUZuLCDJtVRyYW5zbGF0ZUZuIGFzIFRyYW5zbGF0ZUZufSBmcm9tICdAYW5ndWxhci9sb2NhbGl6ZSc7XG5cbmV4cG9ydCB7JGxvY2FsaXplLCBMb2NhbGl6ZUZuLCBUcmFuc2xhdGVGbn07XG5cbi8vIEF0dGFjaCAkbG9jYWxpemUgdG8gdGhlIGdsb2JhbCBjb250ZXh0LCBhcyBhIHNpZGUtZWZmZWN0IG9mIHRoaXMgbW9kdWxlLlxuKGdsb2JhbFRoaXMgYXMgYW55KS4kbG9jYWxpemUgPSAkbG9jYWxpemU7XG4iXX0=