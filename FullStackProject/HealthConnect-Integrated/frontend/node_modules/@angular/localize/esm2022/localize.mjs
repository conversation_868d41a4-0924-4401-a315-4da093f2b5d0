/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// This file contains the public API of the `@angular/localize` entry-point
export { clearTranslations, loadTranslations } from './src/translate';
// Exports that are not part of the public API
export * from './private';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibG9jYWxpemUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9sb2NhbGl6ZS9sb2NhbGl6ZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCwyRUFBMkU7QUFFM0UsT0FBTyxFQUFDLGlCQUFpQixFQUFFLGdCQUFnQixFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFHcEUsOENBQThDO0FBQzlDLGNBQWMsV0FBVyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8vIFRoaXMgZmlsZSBjb250YWlucyB0aGUgcHVibGljIEFQSSBvZiB0aGUgYEBhbmd1bGFyL2xvY2FsaXplYCBlbnRyeS1wb2ludFxuXG5leHBvcnQge2NsZWFyVHJhbnNsYXRpb25zLCBsb2FkVHJhbnNsYXRpb25zfSBmcm9tICcuL3NyYy90cmFuc2xhdGUnO1xuZXhwb3J0IHtNZXNzYWdlSWQsIFRhcmdldE1lc3NhZ2V9IGZyb20gJy4vc3JjL3V0aWxzJztcblxuLy8gRXhwb3J0cyB0aGF0IGFyZSBub3QgcGFydCBvZiB0aGUgcHVibGljIEFQSVxuZXhwb3J0ICogZnJvbSAnLi9wcml2YXRlJztcbiJdfQ==