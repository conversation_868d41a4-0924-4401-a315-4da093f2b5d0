/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// This module specifier is intentionally a relative path to allow bundling the code directly
// into the package.
import { computeMsgId } from '../../../../compiler/src/i18n/digest';
import { BLOCK_MARKER, ID_SEPARATOR, LEGACY_ID_INDICATOR, MEANING_SEPARATOR } from './constants';
/**
 * Re-export this helper function so that users of `@angular/localize` don't need to actively import
 * from `@angular/compiler`.
 */
export { computeMsgId };
/**
 * Parse a `$localize` tagged string into a structure that can be used for translation or
 * extraction.
 *
 * See `ParsedMessage` for an example.
 */
export function parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {
    const substitutions = {};
    const substitutionLocations = {};
    const associatedMessageIds = {};
    const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);
    const cleanedMessageParts = [metadata.text];
    const placeholderNames = [];
    let messageString = metadata.text;
    for (let i = 1; i < messageParts.length; i++) {
        const { messagePart, placeholderName = computePlaceholderName(i), associatedMessageId } = parsePlaceholder(messageParts[i], messageParts.raw[i]);
        messageString += `{$${placeholderName}}${messagePart}`;
        if (expressions !== undefined) {
            substitutions[placeholderName] = expressions[i - 1];
            substitutionLocations[placeholderName] = expressionLocations[i - 1];
        }
        placeholderNames.push(placeholderName);
        if (associatedMessageId !== undefined) {
            associatedMessageIds[placeholderName] = associatedMessageId;
        }
        cleanedMessageParts.push(messagePart);
    }
    const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');
    const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter(id => id !== messageId) : [];
    return {
        id: messageId,
        legacyIds,
        substitutions,
        substitutionLocations,
        text: messageString,
        customId: metadata.customId,
        meaning: metadata.meaning || '',
        description: metadata.description || '',
        messageParts: cleanedMessageParts,
        messagePartLocations,
        placeholderNames,
        associatedMessageIds,
        location,
    };
}
/**
 * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.
 *
 * If the message part has a metadata block this function will extract the `meaning`,
 * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties
 * are serialized in the string delimited by `|`, `@@` and `␟` respectively.
 *
 * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)
 *
 * For example:
 *
 * ```ts
 * `:meaning|description@@custom-id:`
 * `:meaning|@@custom-id:`
 * `:meaning|description:`
 * `:description@@custom-id:`
 * `:meaning|:`
 * `:description:`
 * `:@@custom-id:`
 * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`
 * ```
 *
 * @param cooked The cooked version of the message part to parse.
 * @param raw The raw version of the message part to parse.
 * @returns A object containing any metadata that was parsed from the message part.
 */
export function parseMetadata(cooked, raw) {
    const { text: messageString, block } = splitBlock(cooked, raw);
    if (block === undefined) {
        return { text: messageString };
    }
    else {
        const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);
        const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);
        let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);
        if (description === undefined) {
            description = meaning;
            meaning = undefined;
        }
        if (description === '') {
            description = undefined;
        }
        return { text: messageString, meaning, description, customId, legacyIds };
    }
}
/**
 * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the
 * text.
 *
 * If the message part has a metadata block this function will extract the `placeholderName` and
 * `associatedMessageId` (if provided) from the block.
 *
 * These metadata properties are serialized in the string delimited by `@@`.
 *
 * For example:
 *
 * ```ts
 * `:placeholder-name@@associated-id:`
 * ```
 *
 * @param cooked The cooked version of the message part to parse.
 * @param raw The raw version of the message part to parse.
 * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the
 *     preceding placeholder, along with the static text that follows.
 */
export function parsePlaceholder(cooked, raw) {
    const { text: messagePart, block } = splitBlock(cooked, raw);
    if (block === undefined) {
        return { messagePart };
    }
    else {
        const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);
        return { messagePart, placeholderName, associatedMessageId };
    }
}
/**
 * Split a message part (`cooked` + `raw`) into an optional delimited "block" off the front and the
 * rest of the text of the message part.
 *
 * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the
 * start and end of the block.
 *
 * If the block is in the first message part then it will be metadata about the whole message:
 * meaning, description, id.  Otherwise it will be metadata about the immediately preceding
 * substitution: placeholder name.
 *
 * Since blocks are optional, it is possible that the content of a message block actually starts
 * with a block marker. In this case the marker must be escaped `\:`.
 *
 * @param cooked The cooked version of the message part to parse.
 * @param raw The raw version of the message part to parse.
 * @returns An object containing the `text` of the message part and the text of the `block`, if it
 * exists.
 * @throws an error if the `block` is unterminated
 */
export function splitBlock(cooked, raw) {
    if (raw.charAt(0) !== BLOCK_MARKER) {
        return { text: cooked };
    }
    else {
        const endOfBlock = findEndOfBlock(cooked, raw);
        return {
            block: cooked.substring(1, endOfBlock),
            text: cooked.substring(endOfBlock + 1),
        };
    }
}
function computePlaceholderName(index) {
    return index === 1 ? 'PH' : `PH_${index - 1}`;
}
/**
 * Find the end of a "marked block" indicated by the first non-escaped colon.
 *
 * @param cooked The cooked string (where escaped chars have been processed)
 * @param raw The raw string (where escape sequences are still in place)
 *
 * @returns the index of the end of block marker
 * @throws an error if the block is unterminated
 */
export function findEndOfBlock(cooked, raw) {
    for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {
        if (raw[rawIndex] === '\\') {
            rawIndex++;
        }
        else if (cooked[cookedIndex] === BLOCK_MARKER) {
            return cookedIndex;
        }
    }
    throw new Error(`Unterminated $localize metadata block in "${raw}".`);
}
//# sourceMappingURL=data:application/json;base64,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