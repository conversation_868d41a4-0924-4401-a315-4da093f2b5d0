import { parseTranslation, translate as _translate } from './utils';
/**
 * Load translations for use by `$localize`, if doing runtime translation.
 *
 * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible
 * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,
 * in the browser.
 *
 * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.
 *
 * Note that `$localize` messages are only processed once, when the tagged string is first
 * encountered, and does not provide dynamic language changing without refreshing the browser.
 * Loading new translations later in the application life-cycle will not change the translated text
 * of messages that have already been translated.
 *
 * The message IDs and translations are in the same format as that rendered to "simple JSON"
 * translation files when extracting messages. In particular, placeholders in messages are rendered
 * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:
 *
 * ```html
 * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>
 * ```
 *
 * would have the following form in the `translations` map:
 *
 * ```ts
 * {
 *   "2932901491976224757":
 *      "pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post"
 * }
 * ```
 *
 * @param translations A map from message ID to translated message.
 *
 * These messages are processed and added to a lookup based on their `MessageId`.
 *
 * @see {@link clearTranslations} for removing translations loaded using this function.
 * @see {@link $localize} for tagging messages as needing to be translated.
 * @publicApi
 */
export function loadTranslations(translations) {
    // Ensure the translate function exists
    if (!$localize.translate) {
        $localize.translate = translate;
    }
    if (!$localize.TRANSLATIONS) {
        $localize.TRANSLATIONS = {};
    }
    Object.keys(translations).forEach(key => {
        $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);
    });
}
/**
 * Remove all translations for `$localize`, if doing runtime translation.
 *
 * All translations that had been loading into memory using `loadTranslations()` will be removed.
 *
 * @see {@link loadTranslations} for loading translations at runtime.
 * @see {@link $localize} for tagging messages as needing to be translated.
 *
 * @publicApi
 */
export function clearTranslations() {
    $localize.translate = undefined;
    $localize.TRANSLATIONS = {};
}
/**
 * Translate the text of the given message, using the loaded translations.
 *
 * This function may reorder (or remove) substitutions as indicated in the matching translation.
 */
export function translate(messageParts, substitutions) {
    try {
        return _translate($localize.TRANSLATIONS, messageParts, substitutions);
    }
    catch (e) {
        console.warn(e.message);
        return [messageParts, substitutions];
    }
}
//# sourceMappingURL=data:application/json;base64,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