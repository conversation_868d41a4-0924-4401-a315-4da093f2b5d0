{"version": 3, "sources": ["../../../../../../../../../packages/localize/tools/src/migrate/cli.ts", "../../../../../../../../../packages/localize/tools/src/migrate/index.ts", "../../../../../../../../../packages/localize/tools/src/migrate/migrate.ts"], "mappings": ";;;;;;;AASA,SAAQ,eAAe,UAAU,kBAAkB,qBAAoB;AACvE,OAAO,UAAU;AACjB,OAAO,WAAW;;;ACHlB,SAAQ,qBAA4B;;;ACM9B,SAAU,YAAY,YAAoB,SAAyB;AACvE,QAAM,YAAY,OAAO,KAAK,OAAO;AAErC,aAAW,YAAY,WAAW;AAChC,UAAM,cAAc,QAAQ;AAC5B,UAAM,UAAU,IAAI,OAAO,aAAa,QAAQ,GAAG,GAAG;AACtD,iBAAa,WAAW,QAAQ,SAAS,WAAW;;AAGtD,SAAO;AACT;AAGA,SAAS,aAAa,KAAW;AAC/B,SAAO,IAAI,QAAQ,8BAA8B,MAAM;AACzD;;;ADAM,SAAU,aAAa,EAC3B,UAAAA,WACA,sBAAAC,uBACA,iBACA,QAAAC,QAAM,GACc;AACpB,QAAMC,MAAK,cAAa;AACxB,QAAM,sBAAsBA,IAAG,QAAQH,WAAU,eAAe;AAChE,QAAM,UAAU,KAAK,MAAMG,IAAG,SAAS,mBAAmB,CAAC;AAE3D,MAAI,OAAO,KAAK,OAAO,EAAE,WAAW,GAAG;AACrC,IAAAD,QAAO,KACH,mBAAmB,kIACoD;SACtE;AACL,IAAAD,sBAAqB,QAAQ,UAAO;AAClC,YAAM,eAAeE,IAAG,QAAQH,WAAU,IAAI;AAC9C,YAAM,aAAaG,IAAG,SAAS,YAAY;AAC3C,MAAAA,IAAG,UAAU,cAAc,YAAY,YAAY,OAAO,CAAC;IAC7D,CAAC;;AAEL;;;ADpCA,IAAM,OAAO,QAAQ,KAAK,MAAM,CAAC;AACjC,IAAM,UACF,MAAM,IAAI,EACL,OAAO,KAAK;EACX,OAAO;EACP,SAAS;EACT,UAAU;EAEV,MAAM;CACP,EACA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,UACI;EACJ,MAAM;CACP,EACA,OAAO,KAAK;EACX,OAAO;EACP,UAAU;EACV,UACI;EACJ,MAAM;CACP,EACA,OAAM,EACN,KAAI,EACJ,UAAS;AAElB,IAAM,KAAK,IAAI,iBAAgB;AAC/B,cAAc,EAAE;AAEhB,IAAM,WAAW,QAAQ;AACzB,IAAM,uBAAuB,KAAK,KAAK,QAAQ,GAAG,EAAC,KAAK,UAAU,WAAW,KAAI,CAAC;AAClF,IAAM,SAAS,IAAI,cAAc,SAAS,IAAI;AAE9C,aAAa,EAAC,UAAU,sBAAsB,iBAAiB,QAAQ,GAAG,OAAM,CAAC;AACjF,QAAQ,KAAK,CAAC;", "names": ["rootPath", "translationFilePaths", "logger", "fs"]}