{"version": 3, "sources": ["../../../../../../../packages/localize/tools/src/translate/source_files/es2015_translate_plugin.ts", "../../../../../../../packages/localize/tools/src/translate/source_files/es5_translate_plugin.ts", "../../../../../../../packages/localize/tools/src/translate/source_files/locale_plugin.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/translation_parsers/arb_translation_parser.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/translation_parsers/simple_json_translation_parser.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/translation_parsers/xliff1_translation_parser.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/base_visitor.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/message_serialization/message_serializer.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/translation_parsers/translation_parse_error.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/message_serialization/target_message_renderer.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/translation_parsers/xliff2_translation_parser.ts", "../../../../../../../packages/localize/tools/src/translate/translation_files/translation_parsers/xtb_translation_parser.ts"], "mappings": ";;;;;;;;;;;;;;;;;AAOA,SAAQ,qBAAsC;AAaxC,SAAU,0BACZ,aAA0B,cAC1B,EAAC,qBAAqB,SAAS,eAAe,YAAW,IAA4B,CAAA,GACrF,KAAuB,cAAa,GAAE;AACxC,SAAO;IACL,SAAS;MACP,yBAAyB,MAA0C;AACjE,YAAI;AACF,gBAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,cAAI,WAAW,KAAK,YAAY,GAAG;AACjC,kBAAM,CAAC,YAAY,IACf,sCAAsC,KAAK,IAAI,OAAO,EAAE,IAAI,QAAQ,GAAG,EAAE;AAC7E,kBAAM,aAAa,UACf,aAAa,cAAc,cAAc,KAAK,KAAK,MAAM,aACzD,kBAAkB;AACtB,iBAAK,YAAY,yBAAyB,WAAW,IAAI,WAAW,EAAE,CAAC;;iBAElE,GAAP;AACA,cAAI,kBAAkB,CAAC,GAAG;AAIxB,kBAAM,oBAAoB,IAAI,MAAM,CAAC;iBAChC;AACL,kBAAM;;;MAGZ;;;AAGN;;;AC3CA,SAAQ,iBAAAA,sBAAsC;AAaxC,SAAU,uBACZ,aAA0B,cAC1B,EAAC,qBAAqB,SAAS,eAAe,YAAW,IAA4B,CAAA,GACrF,KAAuBC,eAAa,GAAE;AACxC,SAAO;IACL,SAAS;MACP,eAAe,UAAoC;AACjD,YAAI;AACF,gBAAM,aAAa,SAAS,IAAI,QAAQ;AACxC,cAAI,WAAW,YAAY,YAAY,GAAG;AACxC,kBAAM,CAAC,YAAY,IAAI,mCAAmC,UAAU,EAAE;AACtE,kBAAM,CAAC,WAAW,IAAI,oCAAoC,UAAU,EAAE;AACtE,kBAAM,aACF,UAAU,aAAa,cAAc,cAAc,aAAa,kBAAkB;AACtF,qBAAS,YAAY,yBAAyB,WAAW,IAAI,WAAW,EAAE,CAAC;;iBAEtE,GAAP;AACA,cAAI,kBAAkB,CAAC,GAAG;AACxB,wBAAY,MAAM,oBAAoB,IAAI,UAAU,CAAC,CAAC;iBACjD;AACL,kBAAM;;;MAGZ;;;AAGN;;;ACvCA,SAA6B,SAAS,SAAQ;AAiBxC,SAAU,iBACZ,QAAgB,EAAC,eAAe,YAAW,IAA4B,CAAA,GAAE;AAC3E,SAAO;IACL,SAAS;MACP,iBAAiB,YAAwC;AACvD,cAAM,MAAM,WAAW,IAAI,QAAQ;AACnC,YAAI,CAAC,WAAW,KAAK,YAAY,GAAG;AAClC;;AAEF,cAAM,WAAW,WAAW,IAAI,UAAU;AAC1C,YAAI,CAAC,SAAS,aAAa,EAAC,MAAM,SAAQ,CAAC,GAAG;AAC5C;;AAEF,YAAI,WAAW,WAAW,uBAAsB,KAC5C,WAAW,WAAW,IAAI,MAAM,MAAM,YAAY;AACpD;;AAIF,cAAM,SAAS,WAAW;AAC1B,YAAI,OAAO,oBAAoB,EAAC,UAAU,KAAI,CAAC,KAAK,OAAO,IAAI,OAAO,MAAM,YAAY;AACtF,gBAAM,OAAO,OAAO,IAAI,MAAM;AAC9B,cAAI,gBAAgB,MAAM,YAAY,GAAG;AAGvC,mBAAO,YAAY,UAAU;qBAE3B,KAAK,oBAAoB,EAAC,UAAU,KAAI,CAAC,KACzC,gBAAgB,KAAK,IAAI,OAAO,GAAG,YAAY,GAAG;AAIpD,iBAAK,YAAY,KAAK,IAAI,MAAM,CAAC;;;AAIrC,mBAAW,YAAY,EAAE,cAAc,MAAM,CAAC;MAChD;;;AAGN;AAYA,SAAS,gBAAgB,YAAsB,cAAoB;AACjE,MAAI,CAAC,WAAW,mBAAkB,KAC9B,EAAE,WAAW,KAAK,aAAa,SAAS,WAAW,KAAK,aAAa,OAAO;AAC9E,WAAO;;AAET,QAAM,OAAO,WAAW,IAAI,MAAM;AAClC,QAAM,QAAQ,WAAW,IAAI,OAAO;AAEpC,SAAQ,KAAK,kBAAkB,EAAC,UAAU,SAAQ,CAAC,KAC3C,WAAW,KAAK,IAAI,UAAU,GAAG,YAAY,KAC7C,MAAM,gBAAgB,EAAC,OAAO,YAAW,CAAC,KAC7C,MAAM,kBAAkB,EAAC,UAAU,SAAQ,CAAC,KAC5C,WAAW,MAAM,IAAI,UAAU,GAAG,YAAY,KAC9C,KAAK,gBAAgB,EAAC,OAAO,YAAW,CAAC;AAChD;;;ACnFA,SAAmB,8BAAwC;AA+CrD,IAAO,uBAAP,MAA2B;EAC/B,QAAQ,WAAmB,UAAgB;AACzC,UAAM,cAAc,IAAI,YAAW;AACnC,QAAI,CAAC,SAAS,SAAS,YAAY,GAAG;AACpC,aAAO,EAAC,UAAU,OAAO,YAAW;;AAEtC,QAAI;AAEF,aAAO,EAAC,UAAU,MAAM,aAAa,MAAM,KAAK,kBAAkB,QAAQ,EAAC;YAC3E;AACA,kBAAY,KAAK,yBAAyB;AAC1C,aAAO,EAAC,UAAU,OAAO,YAAW;;EAExC;EAEA,MAAM,WAAmB,UAAkB,MAAqB,KAAK,kBAAkB,QAAQ,GAAC;AAE9F,UAAM,SAAkC;MACtC,QAAQ,IAAI;MACZ,cAAc,CAAA;MACd,aAAa,IAAI,YAAW;;AAG9B,eAAW,aAAa,OAAO,KAAK,GAAG,GAAG;AACxC,UAAI,UAAU,WAAW,GAAG,GAAG;AAE7B;;AAEF,YAAM,gBAAgB,IAAI;AAC1B,aAAO,aAAa,aAAa,uBAAkB,aAAa;;AAElE,WAAO;EACT;EAEQ,kBAAkB,UAAgB;AACxC,UAAM,OAAO,KAAK,MAAM,QAAQ;AAChC,QAAI,OAAO,KAAK,gBAAgB,UAAU;AACxC,YAAM,IAAI,MAAM,4BAA4B;;AAE9C,WAAO;EACT;;;;ACvFF,SAAuC,0BAAAC,+BAAwB;AAC/D,SAAQ,eAAc;AA2BhB,IAAO,8BAAP,MAAkC;EACtC,QAAQ,UAAkB,UAAgB;AACxC,UAAM,cAAc,IAAI,YAAW;AAGnC,QAAI,QAAQ,QAAQ,MAAM,WACtB,EAAE,SAAS,SAAS,UAAU,KAAK,SAAS,SAAS,gBAAgB,IAAI;AAC3E,kBAAY,KAAK,qCAAqC;AACtD,aAAO,EAAC,UAAU,OAAO,YAAW;;AAEtC,QAAI;AACF,YAAM,OAAO,KAAK,MAAM,QAAQ;AAChC,UAAI,KAAK,WAAW,QAAW;AAC7B,oBAAY,KAAK,qCAAqC;AACtD,eAAO,EAAC,UAAU,OAAO,YAAW;;AAEtC,UAAI,OAAO,KAAK,WAAW,UAAU;AACnC,oBAAY,KAAK,wCAAwC;AACzD,eAAO,EAAC,UAAU,OAAO,YAAW;;AAEtC,UAAI,KAAK,iBAAiB,QAAW;AACnC,oBAAY,KAAK,2CAA2C;AAC5D,eAAO,EAAC,UAAU,OAAO,YAAW;;AAEtC,UAAI,OAAO,KAAK,iBAAiB,UAAU;AACzC,oBAAY,KAAK,sCAAsC;AACvD,eAAO,EAAC,UAAU,OAAO,YAAW;;AAEtC,aAAO,EAAC,UAAU,MAAM,aAAa,MAAM,KAAI;aACxC,GAAP;AACA,kBAAY,KAAK,yBAAyB;AAC1C,aAAO,EAAC,UAAU,OAAO,YAAW;;EAExC;EAEA,MAAM,WAAmB,UAAkB,MAAqB;AAC9D,UAAM,EAAC,QAAQ,cAAc,aAAY,IAAI,QAAS,KAAK,MAAM,QAAQ;AACzE,UAAM,qBAA4D,CAAA;AAClE,eAAW,aAAa,cAAc;AACpC,YAAM,gBAAgB,aAAa;AACnC,yBAAmB,aAAaC,wBAAkB,aAAa;;AAEjE,WAAO,EAAC,QAAQ,cAAc,cAAc,oBAAoB,aAAa,IAAI,YAAW,EAAE;EAChG;;;;ACvEF,SAAiB,mBAAAC,kBAAiB,YAAAC,iBAAe;;;ACO3C,IAAO,cAAP,MAAkB;EACtB,aAAa,UAAmB,UAAa;EAAQ;EACrD,eAAe,YAAuB,UAAa;EAAQ;EAC3D,UAAU,OAAa,UAAa;EAAQ;EAC5C,aAAa,UAAmB,UAAa;EAAQ;EACrD,eAAe,YAAuB,UAAa;EAAQ;EAC3D,mBAAmB,gBAA+B,UAAa;EAAQ;EACvE,gBAAgB,QAAoB,UAAa;EAAG;EACpD,WAAW,QAAe,UAAa;EAAG;EAC1C,oBAAoB,YAA4B,UAAa;EAAG;;;;AChBlE,SAAQ,WAAAC,UAA+C,gBAAe;;;ACAtE,SAAQ,uBAAuC;AAKzC,IAAO,wBAAP,cAAqC,MAAK;EAC9C,YACW,MAA8B,KAC9B,QAAyB,gBAAgB,OAAK;AACvD,UAAM,kBAAkB,MAAM,KAAK,KAAK,CAAC;AAFhC,SAAA,OAAA;AAA8B,SAAA,MAAA;AAC9B,SAAA,QAAA;EAEX;;AAGF,SAAS,kBAAkB,MAAuB,KAAa,OAAsB;AACnF,QAAM,MAAM,KAAK,MAAM,WAAW,KAAK,CAAC;AACxC,SAAO;KAAQ,KAAK,QAAQ,KAAK,UAAU,KAAK,KAAK,YAAY;;AACjE,MAAI,KAAK;AACP,WAAO,MAAM,IAAI,UAAU,gBAAgB,aAAa,IAAI;;;AAE9D,SAAO;AACT;;;ACpBA,SAAQ,SAA2B,YAAY,mBAAAC,kBAAmD,iBAAgB;AAO5G,SAAU,eAAe,SAAkB,UAAgB;AAC/D,QAAM,YAAY,aAAa,SAAS,QAAQ;AAChD,MAAI,cAAc,QAAW;AAC3B,UAAM,IAAI,sBACN,QAAQ,YAAY,qBAAqB,sBAAsB;;AAErE,SAAO;AACT;AAEM,SAAU,aAAa,SAAkB,UAAgB;AAC7D,QAAM,OAAO,QAAQ,MAAM,KAAK,OAAK,EAAE,SAAS,QAAQ;AACxD,SAAO,SAAS,SAAY,KAAK,QAAQ;AAC3C;AAWM,SAAU,gBAAgB,SAAgB;AAC9C,QAAM,YAAY,IAAI,UAAS;AAC/B,QAAM,MAAM,UAAU,MAClB,QAAQ,WAAW,MAAM,KAAK,SAAS,QAAQ,WAAW,MAAM,KAAK,KACrE,EAAC,wBAAwB,MAAM,OAAO,cAAc,OAAO,EAAC,CAAC;AACjE,SAAO;AACT;AAMA,SAAS,cAAc,SAAgB;AACrC,QAAM,QAAQ,QAAQ,gBAAgB;AACtC,QAAM,MAAM,QAAQ,cAAe;AACnC,SAAO;IACL,UAAU,MAAM;IAChB,WAAW,MAAM;IACjB,UAAU,MAAM;IAChB,QAAQ,IAAI;;AAEhB;AAwBM,SAAU,YACZ,UAAkB,UAAkB,cACpC,YAAkC;AACpC,QAAM,cAAc,IAAI,YAAW;AACnC,QAAM,YAAY,IAAI,UAAS;AAC/B,QAAM,MAAM,UAAU,MAAM,UAAU,QAAQ;AAE9C,MAAI,IAAI,UAAU,WAAW,KACzB,IAAI,OAAO,KAAK,WAAS,MAAM,UAAUC,iBAAgB,KAAK,GAAG;AACnE,QAAI,OAAO,QAAQ,OAAK,cAAc,aAAa,CAAC,CAAC;AACrD,WAAO,EAAC,UAAU,OAAO,YAAW;;AAGtC,QAAM,eAAe,IAAI,UAAU,OAAO,eAAe,YAAY,CAAC;AACtE,QAAM,cAAc,aAAa;AACjC,MAAI,gBAAgB,QAAW;AAC7B,gBAAY,KAAK,oCAAoC,0BAA0B;AAC/E,WAAO,EAAC,UAAU,OAAO,YAAW;;AAGtC,aAAW,WAAW,OAAO,KAAK,UAAU,GAAG;AAC7C,UAAM,OAAO,YAAY,MAAM,KAAK,CAAAC,UAAQA,MAAK,SAAS,OAAO;AACjE,QAAI,SAAS,UAAa,KAAK,UAAU,WAAW,UAAU;AAC5D,yBACI,aAAa,YAAY,YACzB,QAAQ,4DAA4D,YAChE,WAAW,cACfD,iBAAgB,OAAO;AAC3B,aAAO,EAAC,UAAU,OAAO,YAAW;;;AAIxC,MAAI,aAAa,SAAS,GAAG;AAC3B,QAAI,OAAO,KAAK,IAAI,WAChB,IAAI,UAAU,GAAG,YACjB,sFACAA,iBAAgB,OAAO,CAAC;;AAG9B,SAAO,EAAC,UAAU,MAAM,aAAa,MAAM,EAAC,SAAS,aAAa,QAAQ,IAAI,OAAM,EAAC;AACvF;AAQM,SAAU,eAAe,MAAY;AACzC,WAAS,UAAU,MAAU;AAC3B,WAAO,gBAAgB,WAAW,KAAK,SAAS;EAClD;AACA,SAAO;AACT;AAKM,SAAU,mBACZ,aAA0B,YAA6B,SACvD,OAAsB;AACxB,gBAAc,aAAa,IAAI,WAAW,YAAY,SAAS,KAAK,CAAC;AACvE;AAMM,SAAU,cAAc,aAA0B,YAAsB;AAC5E,MAAI,WAAW,UAAUA,iBAAgB,OAAO;AAC9C,gBAAY,MAAM,WAAW,SAAQ,CAAE;SAClC;AACL,gBAAY,KAAK,WAAW,SAAQ,CAAE;;AAE1C;AAKM,SAAU,kBAAkB,QAAiC,QAAoB;AACrF,aAAW,SAAS,QAAQ;AAC1B,kBAAc,OAAO,aAAa,KAAK;;AAE3C;;;AF3IM,IAAO,oBAAP,cAAoC,YAAW;EACnD,YAAoB,UAAsC,QAA+B;AACvF,UAAK;AADa,SAAA,WAAA;AAAsC,SAAA,SAAA;EAE1D;EAEA,UAAU,OAAa;AACrB,SAAK,SAAS,YAAW;AACzB,aAAS,MAAM,KAAK;AACpB,SAAK,SAAS,UAAS;AACvB,WAAO,KAAK,SAAS;EACvB;EAES,aAAa,SAAgB;AACpC,QAAI,KAAK,OAAO,eAAe,QAAQ,SAAS,KAAK,OAAO,YAAY,aAAa;AACnF,YAAM,OAAO,eAAe,SAAS,KAAK,OAAO,YAAY,aAAa;AAC1E,YAAM,OAAO,KAAK,OAAO,YAAY,iBACjC,aAAa,SAAS,KAAK,OAAO,YAAY,aAAa;AAC/D,WAAK,iBAAiB,MAAM,IAAI;eAE9B,KAAK,OAAO,wBACZ,QAAQ,SAAS,KAAK,OAAO,qBAAqB,aAAa;AACjE,YAAM,QAAQ,eAAe,SAAS,KAAK,OAAO,qBAAqB,cAAc;AACrF,YAAM,MAAM,eAAe,SAAS,KAAK,OAAO,qBAAqB,YAAY;AACjF,WAAK,0BAA0B,OAAO,QAAQ,UAAU,GAAG;eAClD,KAAK,OAAO,eAAe,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAClE,eAAS,MAAM,QAAQ,QAAQ;WAC1B;AACL,YAAM,IAAI,sBAAsB,QAAQ,YAAY,mCAAmC;;EAE3F;EAES,UAAU,MAAU;AAC3B,SAAK,SAAS,KAAK,KAAK,KAAK;EAC/B;EAES,eAAe,WAAoB;AAC1C,SAAK,SAAS,SAAQ;AACtB,SAAK,SAAS,KAAK,GAAG,UAAU,gBAAgB,UAAU,OAAO;AACjE,aAAS,MAAM,UAAU,KAAK;AAC9B,SAAK,SAAS,OAAM;EACtB;EAES,mBAAmB,eAA4B;AACtD,SAAK,SAAS,KAAK,IAAI,cAAc,SAAS;AAC9C,SAAK,SAAS,eAAc;AAC5B,aAAS,MAAM,cAAc,UAAU;AACvC,SAAK,SAAS,eAAc;AAC5B,SAAK,SAAS,KAAK,GAAG;EACxB;EAEA,oBAAoB,OAAa;AAC/B,SAAK,SAAS,eAAc;AAC5B,aAAS,MAAM,KAAK;AACpB,SAAK,SAAS,eAAc;EAC9B;EAEA,iBAAiB,MAAc,MAAsB;AACnD,SAAK,SAAS,YAAY,MAAM,IAAI;EACtC;EAEA,0BAA0B,WAAmB,UAAkB,WAAiB;AAC9E,SAAK,SAAS,iBAAiB,SAAS;AACxC,SAAK,oBAAoB,QAAQ;AACjC,SAAK,SAAS,iBAAiB,SAAS;EAC1C;EAEQ,uBAAuB,MAAU;AACvC,WAAO,gBAAgBE,YAAW,KAAK,SAAS,KAAK,OAAO,qBAAsB;EACpF;;;;AGvFF,SAAQ,mCAAiD;AAOnD,IAAO,wBAAP,MAA4B;EAAlC,cAAA;AACU,SAAA,UAAuB,EAAC,cAAc,CAAA,GAAI,kBAAkB,CAAA,GAAI,MAAM,GAAE;AACxE,SAAA,WAAW;EAgDrB;EA9CE,IAAI,UAAO;AACT,UAAM,EAAC,cAAc,iBAAgB,IAAI,KAAK;AAC9C,WAAO,4BAAuB,cAAc,gBAAgB;EAC9D;EACA,cAAW;EAAU;EACrB,YAAS;AACP,SAAK,iBAAgB;EACvB;EACA,KAAK,MAAY;AACf,SAAK,QAAQ,QAAQ;EACvB;EACA,YAAY,MAAc,MAAsB;AAC9C,SAAK,kBAAkB,IAAI;EAC7B;EACA,iBAAiB,MAAY;AAC3B,SAAK,kBAAkB,IAAI;EAC7B;EACA,iBAAiB,MAAY;AAC3B,SAAK,kBAAkB,IAAI;EAC7B;EACA,iBAAc;EAAU;EACxB,iBAAc;EAAU;EACxB,WAAQ;AACN,SAAK;AACL,SAAK,KAAK,GAAG;EACf;EACA,SAAM;AACJ,SAAK;AACL,SAAK,KAAK,GAAG;EACf;EACQ,yBAAyB,MAAY;AAC3C,WAAO,KAAK,QAAQ,MAAM,GAAG;EAC/B;EACQ,kBAAkB,MAAY;AACpC,WAAO,KAAK,yBAAyB,IAAI;AACzC,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,KAAK,IAAI,OAAO;WAChB;AACL,WAAK,iBAAgB;AACrB,WAAK,QAAQ,iBAAiB,KAAK,IAAI;;EAE3C;EACQ,mBAAgB;AACtB,SAAK,QAAQ,aAAa,KAAK,KAAK,QAAQ,IAAI;AAChD,SAAK,QAAQ,OAAO;EACtB;;;;AC7CI,SAAU,4BAA4B,SAAkB,QAA+B;AAK3F,QAAM,EAAC,WAAW,QAAQ,YAAW,IAAI,gBAAgB,OAAO;AAChE,MAAI;AACF,UAAM,aAAa,IAAI,kBAAkB,IAAI,sBAAqB,GAAI,MAAM;AAC5E,UAAM,cAAc,WAAW,UAAU,SAAS;AAClD,WAAO,EAAC,aAAa,aAAa,iBAAiB,CAAA,EAAE;WAC9C,GAAP;AACA,WAAO,EAAC,aAAa,MAAM,aAAa,iBAAiB,CAAC,CAAe,EAAC;;AAE9E;;;ANNM,IAAO,0BAAP,MAA8B;EAClC,QAAQ,UAAkB,UAAgB;AACxC,WAAO,YAAY,UAAU,UAAU,SAAS,EAAC,SAAS,MAAK,CAAC;EAClE;EAEA,MAAM,UAAkB,UAAkB,MAA8B;AAEtE,WAAO,KAAK,cAAc,IAAI;EAChC;EAEQ,cAAc,EAAC,SAAS,OAAM,GAA2B;AAC/D,UAAM,cAAc,IAAI,YAAW;AACnC,WAAO,QAAQ,OAAK,cAAc,aAAa,CAAC,CAAC;AAEjD,QAAI,QAAQ,SAAS,WAAW,GAAG;AACjC,yBACI,aAAa,QAAQ,YAAY,mCACjCC,iBAAgB,OAAO;AAC3B,aAAO,EAAC,QAAQ,QAAW,cAAc,CAAA,GAAI,YAAW;;AAG1D,UAAM,QAAQ,QAAQ,SAAS,OAAO,eAAe,MAAM,CAAC;AAC5D,QAAI,MAAM,WAAW,GAAG;AACtB,yBACI,aAAa,QAAQ,YAAY,uCACjCA,iBAAgB,OAAO;eAClB,MAAM,SAAS,GAAG;AAC3B,yBACI,aAAa,MAAM,GAAG,YAAY,iDAClCA,iBAAgB,OAAO;;AAG7B,UAAM,SAAkC,EAAC,QAAQ,QAAW,cAAc,CAAA,GAAI,YAAW;AACzF,UAAM,qBAAqB,IAAI,wBAAuB;AACtD,UAAM,eAAe,oBAAI,IAAG;AAC5B,eAAW,QAAQ,OAAO;AACxB,YAAM,SAAS,aAAa,MAAM,iBAAiB;AACnD,UAAI,WAAW,QAAW;AACxB,qBAAa,IAAI,MAAM;AACvB,eAAO,SAAS;;AAElB,MAAAC,UAAS,oBAAoB,KAAK,UAAU,MAAM;;AAGpD,QAAI,aAAa,OAAO,GAAG;AACzB,yBACI,aAAa,QAAQ,YACrB,mDACI,KAAK,UAAU,MAAM,KAAK,YAAY,CAAC,aAAa,OAAO,WAC/DD,iBAAgB,OAAO;;AAG7B,WAAO;EACT;;AAGF,IAAM,0BAAN,cAAsC,YAAW;EACtC,aAAa,SAAkB,QAA+B;AACrE,QAAI,QAAQ,SAAS,cAAc;AACjC,WAAK,sBAAsB,SAAS,MAAM;WACrC;AACL,MAAAC,UAAS,MAAM,QAAQ,UAAU,MAAM;;EAE3C;EAEQ,sBAAsB,SAAkB,QAA+B;AAE7E,UAAM,KAAK,aAAa,SAAS,IAAI;AACrC,QAAI,OAAO,QAAW;AACpB,yBACI,OAAO,aAAa,QAAQ,YAC5B,4DAA4DD,iBAAgB,KAAK;AACrF;;AAIF,QAAI,OAAO,aAAa,QAAQ,QAAW;AACzC,yBACI,OAAO,aAAa,QAAQ,YAAY,wCAAwC,OAChFA,iBAAgB,KAAK;AACzB;;AAGF,QAAI,gBAAgB,QAAQ,SAAS,KAAK,eAAe,QAAQ,CAAC;AAClE,QAAI,kBAAkB,QAAW;AAE/B,yBACI,OAAO,aAAa,QAAQ,YAAY,4BACxCA,iBAAgB,OAAO;AAG3B,sBAAgB,QAAQ,SAAS,KAAK,eAAe,QAAQ,CAAC;AAC9D,UAAI,kBAAkB,QAAW;AAE/B,2BACI,OAAO,aAAa,QAAQ,YAC5B,qEACAA,iBAAgB,KAAK;AACzB;;;AAIJ,UAAM,EAAC,aAAa,aAAa,gBAAe,IAAI,4BAA4B,eAAe;MAC7F,gBAAgB,CAAC,KAAK,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,KAAK;MACjE,aAAa,EAAC,aAAa,KAAK,eAAe,KAAI;KACpD;AACD,QAAI,gBAAgB,MAAM;AACxB,aAAO,aAAa,MAAM;;AAE5B,sBAAkB,QAAQ,WAAW;AACrC,sBAAkB,QAAQ,eAAe;EAC3C;;;;AOjIF,SAAQ,WAAAE,UAAe,mBAAAC,kBAAiB,YAAAC,iBAAe;AAiBjD,IAAO,0BAAP,MAA8B;EAClC,QAAQ,UAAkB,UAAgB;AACxC,WAAO,YAAY,UAAU,UAAU,SAAS,EAAC,SAAS,MAAK,CAAC;EAClE;EAEA,MAAM,UAAkB,UAAkB,MAA8B;AAEtE,WAAO,KAAK,cAAc,IAAI;EAChC;EAEQ,cAAc,EAAC,SAAS,OAAM,GAA2B;AAC/D,UAAM,cAAc,IAAI,YAAW;AACnC,WAAO,QAAQ,OAAK,cAAc,aAAa,CAAC,CAAC;AAEjD,UAAM,SAAS,aAAa,SAAS,SAAS;AAC9C,UAAM,QAAQ,QAAQ,SAAS,OAAO,aAAa;AACnD,QAAI,MAAM,WAAW,GAAG;AACtB,yBACI,aAAa,QAAQ,YAAY,uCACjCC,iBAAgB,OAAO;eAClB,MAAM,SAAS,GAAG;AAC3B,yBACI,aAAa,MAAM,GAAG,YAAY,iDAClCA,iBAAgB,OAAO;;AAG7B,UAAM,SAAS,EAAC,QAAQ,cAAc,CAAA,GAAI,YAAW;AACrD,UAAM,qBAAqB,IAAI,yBAAwB;AACvD,eAAW,QAAQ,OAAO;AACxB,MAAAC,UAAS,oBAAoB,KAAK,UAAU,EAAC,OAAM,CAAC;;AAEtD,WAAO;EACT;;AASF,IAAM,2BAAN,cAAuC,YAAW;EACvC,aAAa,SAAkB,EAAC,QAAQ,KAAI,GAA4B;AAC/E,QAAI,QAAQ,SAAS,QAAQ;AAC3B,WAAK,iBAAiB,SAAS,MAAM;eAC5B,QAAQ,SAAS,WAAW;AACrC,WAAK,oBAAoB,SAAS,QAAQ,IAAI;WACzC;AACL,MAAAA,UAAS,MAAM,QAAQ,UAAU,EAAC,QAAQ,KAAI,CAAC;;EAEnD;EAEQ,iBAAiB,SAAkB,QAA+B;AAExE,UAAM,aAAa,aAAa,SAAS,IAAI;AAC7C,QAAI,eAAe,QAAW;AAC5B,yBACI,OAAO,aAAa,QAAQ,YAC5B,4DAA4DD,iBAAgB,KAAK;AACrF;;AAIF,QAAI,OAAO,aAAa,gBAAgB,QAAW;AACjD,yBACI,OAAO,aAAa,QAAQ,YAC5B,wCAAwC,eAAeA,iBAAgB,KAAK;AAChF;;AAGF,IAAAC,UAAS,MAAM,QAAQ,UAAU,EAAC,QAAQ,MAAM,WAAU,CAAC;EAC7D;EAEQ,oBACJ,SAAkB,QAAiC,MAAsB;AAE3E,QAAI,SAAS,QAAW;AACtB,yBACI,OAAO,aAAa,QAAQ,YAC5B,qEACAD,iBAAgB,KAAK;AACzB;;AAGF,QAAI,gBAAgB,QAAQ,SAAS,KAAK,eAAe,QAAQ,CAAC;AAClE,QAAI,kBAAkB,QAAW;AAE/B,yBACI,OAAO,aAAa,QAAQ,YAAY,4BACxCA,iBAAgB,OAAO;AAG3B,sBAAgB,QAAQ,SAAS,KAAK,eAAe,QAAQ,CAAC;AAC9D,UAAI,kBAAkB,QAAW;AAE/B,2BACI,OAAO,aAAa,QAAQ,YAC5B,qEACAA,iBAAgB,KAAK;AACzB;;;AAIJ,UAAM,EAAC,aAAa,aAAa,gBAAe,IAAI,4BAA4B,eAAe;MAC7F,gBAAgB,CAAC,MAAM,MAAM,MAAM,OAAO,MAAM,IAAI;MACpD,aAAa,EAAC,aAAa,MAAM,eAAe,SAAS,eAAe,OAAM;MAC9E,sBACI,EAAC,aAAa,MAAM,gBAAgB,cAAc,cAAc,WAAU;KAC/E;AACD,QAAI,gBAAgB,MAAM;AACxB,aAAO,aAAa,QAAQ;;AAE9B,sBAAkB,QAAQ,WAAW;AACrC,sBAAkB,QAAQ,eAAe;EAC3C;;AAGF,SAAS,cAAc,MAAU;AAC/B,SAAO,gBAAgBE,YAAW,KAAK,SAAS;AAClD;;;ACxIA,SAA6B,mBAAAC,kBAAiB,YAAAC,iBAAe;AAC7D,SAAQ,WAAAC,gBAAc;AAkBhB,IAAO,uBAAP,MAA2B;EAC/B,QAAQ,UAAkB,UAAgB;AACxC,UAAM,YAAYC,SAAQ,QAAQ;AAClC,QAAI,cAAc,UAAU,cAAc,QAAQ;AAChD,YAAM,cAAc,IAAI,YAAW;AACnC,kBAAY,KAAK,iCAAiC;AAClD,aAAO,EAAC,UAAU,OAAO,YAAW;;AAEtC,WAAO,YAAY,UAAU,UAAU,qBAAqB,CAAA,CAAE;EAChE;EAEA,MAAM,UAAkB,UAAkB,MAA8B;AAEtE,WAAO,KAAK,cAAc,IAAI;EAChC;EAEQ,cAAc,EAAC,SAAS,OAAM,GAA2B;AAC/D,UAAM,WAAW,QAAQ,MAAM,KAAK,CAAC,SAAS,KAAK,SAAS,MAAM;AAClE,UAAM,SAAkC;MACtC,QAAQ,YAAY,SAAS;MAC7B,cAAc,CAAA;MACd,aAAa,IAAI,YAAW;;AAE9B,WAAO,QAAQ,OAAK,cAAc,OAAO,aAAa,CAAC,CAAC;AAExD,UAAM,gBAAgB,IAAI,WAAU;AACpC,IAAAC,UAAS,eAAe,QAAQ,UAAU,MAAM;AAChD,WAAO;EACT;;AAGF,IAAM,aAAN,cAAyB,YAAW;EACzB,aAAa,SAAkB,QAA+B;AACrE,YAAQ,QAAQ,MAAM;MACpB,KAAK;AAEH,cAAM,KAAK,aAAa,SAAS,IAAI;AACrC,YAAI,OAAO,QAAW;AACpB,6BACI,OAAO,aAAa,QAAQ,YAC5B,6DAA6DC,iBAAgB,KAAK;AACtF;;AAIF,YAAI,OAAO,aAAa,QAAQ,QAAW;AACzC,6BACI,OAAO,aAAa,QAAQ,YAAY,wCAAwC,OAChFA,iBAAgB,KAAK;AACzB;;AAGF,cAAM,EAAC,aAAa,aAAa,gBAAe,IAAI,4BAChD,SAAS,EAAC,gBAAgB,CAAA,GAAI,aAAa,EAAC,aAAa,MAAM,eAAe,OAAM,EAAC,CAAC;AAC1F,YAAI,YAAY,QAAQ;AAGtB,iBAAO,YAAY,KAAK,oBAAoB,IAAI,WAAW,CAAC;mBACnD,gBAAgB,MAAM;AAE/B,iBAAO,aAAa,MAAM;;AAE5B,0BAAkB,QAAQ,eAAe;AACzC;MAEF;AACE,2BACI,OAAO,aAAa,QAAQ,YAAY,eAAe,QAAQ,cAC/DA,iBAAgB,KAAK;;EAE/B;;AAGF,SAAS,oBAAoB,IAAY,QAAoB;AAC3D,QAAM,MAAM,OAAO,IAAI,OAAK,EAAE,SAAQ,CAAE,EAAE,KAAK,IAAI;AACnD,SAAO,oCAAoC;IACvC;AACN;", "names": ["getFileSystem", "getFileSystem", "ɵparseTranslation", "ɵparseTranslation", "ParseErrorLevel", "visitAll", "Element", "ParseErrorLevel", "ParseErrorLevel", "attr", "Element", "ParseErrorLevel", "visitAll", "Element", "ParseErrorLevel", "visitAll", "ParseErrorLevel", "visitAll", "Element", "ParseErrorLevel", "visitAll", "extname", "extname", "visitAll", "ParseErrorLevel"]}