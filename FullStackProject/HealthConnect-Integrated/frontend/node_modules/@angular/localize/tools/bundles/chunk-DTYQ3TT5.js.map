{"version": 3, "sources": ["../../../../../../../packages/localize/tools/src/diagnostics.ts", "../../../../../../../packages/localize/tools/src/source_file_utils.ts"], "mappings": ";;;;;;AAmBM,IAAO,cAAP,MAAkB;EAAxB,cAAA;AACW,SAAA,WAAyD,CAAA;EA6BpE;EA5BE,IAAI,YAAS;AACX,WAAO,KAAK,SAAS,KAAK,OAAK,EAAE,SAAS,OAAO;EACnD;EACA,IAAI,MAAkC,SAAe;AACnD,QAAI,SAAS,UAAU;AACrB,WAAK,SAAS,KAAK,EAAC,MAAM,QAAO,CAAC;;EAEtC;EACA,KAAK,SAAe;AAClB,SAAK,SAAS,KAAK,EAAC,MAAM,WAAW,QAAO,CAAC;EAC/C;EACA,MAAM,SAAe;AACnB,SAAK,SAAS,KAAK,EAAC,MAAM,SAAS,QAAO,CAAC;EAC7C;EACA,MAAM,OAAkB;AACtB,SAAK,SAAS,KAAK,GAAG,MAAM,QAAQ;EACtC;EACA,kBAAkB,SAAe;AAC/B,UAAM,SAAS,KAAK,SAAS,OAAO,OAAK,EAAE,SAAS,OAAO,EAAE,IAAI,OAAK,QAAQ,EAAE,OAAO;AACvF,UAAM,WAAW,KAAK,SAAS,OAAO,OAAK,EAAE,SAAS,SAAS,EAAE,IAAI,OAAK,QAAQ,EAAE,OAAO;AAC3F,QAAI,OAAO,QAAQ;AACjB,iBAAW,gBAAgB,OAAO,KAAK,IAAI;;AAE7C,QAAI,SAAS,QAAQ;AACnB,iBAAW,kBAAkB,SAAS,KAAK,IAAI;;AAEjD,WAAO;EACT;;;;ACzCF,SAAwB,qBAAsC;AAC9D,SAAQ,iCAA4B,0BAA0D,uBAAiB;AAC/G,SAAQ,SAAS,SAAQ;AAWnB,SAAU,WACZ,YAAsB,cAAoB;AAC5C,SAAO,kBAAkB,YAAY,YAAY,KAAK,mBAAmB,UAAU;AACrF;AAQM,SAAU,kBACZ,YAAsB,MAAY;AACpC,SAAO,WAAW,aAAY,KAAM,WAAW,KAAK,SAAS;AAC/D;AAQM,SAAU,mBAAmB,YAAkC;AACnE,SAAO,CAAC,WAAW,SAAS,CAAC,WAAW,MAAM,WAAW,WAAW,KAAK,IAAI;AAC/E;AAQM,SAAU,yBACZ,cAAoC,eAAsC;AAC5E,MAAI,eAA6B,EAAE,cAAc,aAAa,EAAE;AAChE,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,mBACI,EAAE,iBAAiB,KAAK,cAAc,wBAAwB,cAAc,IAAI,EAAE,CAAC;AACvF,mBAAe,EAAE,iBAAiB,KAAK,cAAc,EAAE,cAAc,aAAa,EAAE,CAAC;;AAEvF,SAAO;AACT;AAaM,SAAU,mCACZ,MACA,KAAuB,cAAa,GAAE;AAExC,MAAI,SAAS,KAAK,IAAI,WAAW,EAAE;AAEnC,MAAI,WAAW,QAAW;AACxB,UAAM,IAAI,gBAAgB,KAAK,MAAM,2CAA2C;;AAElF,MAAI,CAAC,OAAO,aAAY,GAAI;AAC1B,UAAM,IAAI,gBACN,OAAO,MAAM,yDAAyD;;AAI5E,MAAI,MAAM;AAGV,MAAI,OAAO,oBAAmB,KAAM,OAAO,KAAK,aAAa,QACzD,OAAO,IAAI,MAAM,EAAE,aAAY,GAAI;AACrC,UAAM,QAAQ,OAAO,IAAI,OAAO;AAChC,QAAI,MAAM,uBAAsB,GAAI;AAClC,eAAS,MAAM,IAAI,OAAO;AAC1B,UAAI,CAAC,OAAO,aAAY,GAAI;AAC1B,cAAM,IAAI,gBACN,OAAO,MAAM,sEAAsE;;eAEhF,MAAM,qBAAoB,GAAI;AACvC,YAAM,cAAc,MAAM,IAAI,aAAa;AAC3C,UAAI,YAAY,SAAS,GAAG;AAG1B,cAAM,CAAC,OAAO,MAAM,IAAI;AACxB,YAAI,MAAM,uBAAsB,GAAI;AAClC,mBAAS,MAAM,IAAI,OAAO;AAC1B,cAAI,CAAC,OAAO,aAAY,GAAI;AAC1B,kBAAM,IAAI,gBACN,MAAM,MAAM,kDAAkD;;AAEpE,cAAI,OAAO,uBAAsB,GAAI;AACnC,kBAAM,OAAO,IAAI,OAAO;AACxB,gBAAI,CAAC,IAAI,aAAY,GAAI;AACvB,oBAAM,IAAI,gBACN,OAAO,MAAM,+CAA+C;;iBAE7D;AAGL,kBAAM;;;;;;AAQhB,MAAI,OAAO,iBAAgB,GAAI;AAC7B,QAAIA,QAAO;AACX,QAAIA,MAAK,IAAI,WAAW,EAAE,WAAW,GAAG;AAGtC,MAAAA,QAAO,yBAAyBA,KAAI;;AAGtC,aAASA,MAAK,IAAI,WAAW,EAAE;AAC/B,QAAI,CAAC,OAAO,aAAY,GAAI;AAC1B,YAAM,IAAI,gBACN,OAAO,MACP,+FAA+F;;AAErG,UAAM,OAAOA,MAAK,IAAI,WAAW,EAAE;AACnC,QAAI,QAAQ,CAAC,KAAK,aAAY,GAAI;AAChC,YAAM,IAAI,gBACN,KAAK,MACL,4FAA4F;;AAGlG,UAAM,SAAS,SAAY,OAAO;;AAGpC,QAAM,CAAC,aAAa,IAAI,yBAAyB,QAAQ,EAAE;AAC3D,QAAM,CAAC,YAAY,YAAY,IAAI,yBAAyB,KAAK,EAAE;AACnE,SAAO,CAAC,yBAAoB,eAAe,UAAU,GAAG,YAAY;AACtE;AAWM,SAAU,oCACZ,MACA,KAAuB,cAAa,GAAE;AACxC,QAAM,cAAc,KAAK,IAAI,WAAW,EAAE,OAAO,CAAC;AAClD,MAAI,CAAC,qBAAqB,WAAW,GAAG;AACtC,UAAM,gBAAgB,YAAY,KAAK,gBAAc,CAAC,WAAW,aAAY,CAAE;AAC/E,UAAM,IAAI,gBACN,cAAc,MACd,gGAAgG;;AAEtG,SAAO;IACL,YAAY,IAAI,UAAQ,KAAK,IAAI;IAAG,YAAY,IAAI,gBAAc,YAAY,IAAI,UAAU,CAAC;;AAEjG;AAUM,SAAU,sCACZ,UAAyC,KAAuB,cAAa,GAAE;AAEjF,QAAM,SAAS,SAAS,IAAI,OAAI;AAC9B,QAAI,EAAE,KAAK,MAAM,WAAW,QAAW;AACrC,YAAM,IAAI,gBACN,EAAE,MACF,yCAAyC,SAAS,IAAI,CAAAC,OAAKA,GAAE,KAAK,MAAM,MAAM,IAAI;;AAExF,WAAO,EAAE,KAAK,MAAM;EACtB,CAAC;AACD,QAAM,MAAM,SAAS,IAAI,OAAK,EAAE,KAAK,MAAM,GAAG;AAC9C,QAAM,YAAY,SAAS,IAAI,OAAK,YAAY,IAAI,CAAC,CAAC;AACtD,SAAO,CAAC,yBAAoB,QAAQ,GAAG,GAAG,SAAS;AACrD;AAUM,SAAU,qCACZ,OACA,KAAuB,cAAa,GAAE;AACxC,SAAO;IACL,MAAM,KAAK;IAA+B,MAAM,IAAI,aAAa,EAAE,IAAI,OAAK,YAAY,IAAI,CAAC,CAAC;;AAElG;AASM,SAAU,wBAAwB,YAAwB;AAC9D,MAAI,EAAE,mBAAmB,UAAU,GAAG;AACpC,WAAO,EAAE,wBAAwB,UAAU;SACtC;AACL,WAAO;;AAEX;AASM,SAAU,yBACZ,OACA,KAAuB,cAAa,GAAE;AACxC,MAAI,CAAC,qBAAqB,MAAM,IAAI,GAAG;AACrC,UAAM,IAAI,gBACN,MAAM,MAAM,yEAAyE;;AAE3F,QAAM,WAAW,MAAM,IAAI,UAAU;AACrC,SAAO,CAAC,SAAS,IAAI,SAAO,IAAI,KAAK,KAAK,GAAG,SAAS,IAAI,SAAO,YAAY,IAAI,GAAG,CAAC,CAAC;AACxF;AAkBM,SAAU,yBAAyB,MAAgC;AAEvE,QAAM,SAAS,KAAK,IAAI,QAAQ;AAChC,MAAI,CAAC,OAAO,aAAY,GAAI;AAC1B,UAAM,IAAI,gBACN,OAAO,MACP,qFAAqF;;AAE3F,QAAM,kBAAkB,KAAK,MAAM,WAAW,OAAO,KAAK,IAAI;AAC9D,MAAI,CAAC,iBAAiB;AACpB,UAAM,IAAI,gBAAgB,OAAO,MAAM,mDAAmD;;AAE5F,QAAM,aAAa,gBAAgB;AACnC,MAAI,CAAC,WAAW,sBAAqB,GAAI;AACvC,UAAM,IAAI,gBACN,WAAW,MAAM,wDAAwD;;AAE/E,QAAM,eAAe,sBAAsB,UAAU;AAErD,MAAI,aAAa,iBAAgB,GAAI;AACnC,WAAO;;AAGT,MAAI,aAAa,aAAY,GAAI;AAC/B,UAAM,iBAAiB,aAAa,KAAK;AACzC,UAAM,cAAc,aAAa,MAAM,WAAW,cAAc;AAChE,QAAI,gBAAgB,QAAW;AAC7B,YAAM,IAAI,gBACN,aAAa,MAAM,mDAAmD;;AAE5E,QAAI,CAAC,YAAY,KAAK,qBAAoB,GAAI;AAC5C,YAAM,IAAI,gBACN,YAAY,KAAK,MACjB,+EAA+E;;AAErF,UAAM,cAAc,YAAY,KAAK,IAAI,MAAM;AAC/C,QAAI,CAAC,YAAY,iBAAgB,GAAI;AACnC,YAAM,IAAI,gBACN,YAAY,KAAK,MACjB,mEAAmE;;AAIzE,QAAI,gBAAgB,eAAe,GAAG;AACpC,iBAAW,OAAM;;AAGnB,WAAO;;AAET,SAAO;AACT;AAEA,SAAS,sBAAsB,IAAmC;AAChE,QAAM,iBAAiB,GAAG,IAAI,MAAM,EAAE,IAAI,MAAM;AAChD,aAAW,aAAa,gBAAgB;AACtC,QAAI,UAAU,kBAAiB,GAAI;AACjC,YAAM,WAAW,UAAU,IAAI,UAAU;AACzC,UAAI,SAAS,qBAAoB,GAAI;AACnC,cAAM,cAAc,SAAS,IAAI,aAAa;AAC9C,eAAO,MAAM,QAAQ,WAAW,IAAI,YAAY,YAAY,SAAS,KAAK;iBACjE,SAAS,aAAY,GAAI;AAClC,eAAO;aACF;AACL,cAAM,IAAI,gBACN,UAAU,MAAM,sEAAsE;;;;AAIhG,QAAM,IAAI,gBAAgB,GAAG,MAAM,8CAA8C;AACnF;AAOM,SAAU,qBAAqB,MAAY;AAE/C,SAAO,EAAE,kBAAkB,IAAI,KAAK,KAAK,SAAS,MAAM,aAAW,EAAE,gBAAgB,OAAO,CAAC;AAC/F;AAMM,SAAU,qBAAqB,OAAyB;AAC5D,SAAO,MAAM,MAAM,aAAW,QAAQ,aAAY,CAAE;AACtD;AAcM,SAAU,UACZ,aAA0B,cAC1B,cAAoC,eACpC,oBAA8C;AAChD,MAAI;AACF,WAAO,gBAAW,cAAc,cAAc,aAAa;WACpD,GAAP;AACA,QAAI,gCAA2B,CAAC,GAAG;AACjC,kBAAY,IAAI,oBAAoB,EAAE,OAAO;AAE7C,aAAO;QACL,yBAAoB,EAAE,cAAc,cAAc,EAAE,cAAc,YAAY;QAC9E;;WAEG;AACL,kBAAY,MAAM,EAAE,OAAO;AAC3B,aAAO,CAAC,cAAc,aAAa;;;AAGzC;AAEM,IAAO,kBAAP,cAA+B,MAAK;EAExC,YAAmB,MAAc,SAAe;AAC9C,UAAM,OAAO;AADI,SAAA,OAAA;AADF,SAAA,OAAO;EAGxB;;AAGI,SAAU,kBAAkB,GAAM;AACtC,SAAO,EAAE,SAAS;AACpB;AAEM,SAAU,oBACZ,IAAsB,MAAgB,GAAkB;AAC1D,MAAI,WAAW,KAAK,IAAI,KAAK,KAAK;AAClC,MAAI,UAAU;AACZ,eAAW,GAAG,QAAQ,QAAQ;AAC9B,QAAI,MAAM,KAAK,IAAI,KAAK,KAAK;AAC7B,QAAI,KAAK;AACP,YAAM,GAAG,QAAQ,GAAG;AACpB,iBAAW,GAAG,SAAS,KAAK,QAAQ;;SAEjC;AACL,eAAW;;AAEb,QAAM,UAAU,KAAK,IAAI,KAAK,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAAE;AACrE,SAAO,GAAG,aAAa;AACzB;AAEM,SAAU,YACZ,IAAsB,WAAqB,SAAkB;AAC/D,QAAM,gBAAgB,UAAU,KAAK;AACrC,QAAM,OAAO,gBAAgB,IAAI,SAAS;AAC1C,MAAI,CAAC,iBAAiB,CAAC,MAAM;AAC3B,WAAO;;AAGT,QAAM,cACF,WAAW,gBAAgB,IAAI,OAAO,MAAM,QAAQ,QAAQ,KAAK,OAAO;AAE5E,SAAO;IACL,OAAO,iBAAiB,cAAc,KAAK;IAC3C,KAAK,iBAAiB,YAAY,GAAG;IACrC;IACA,MAAM,QAAQ,SAAS;;AAE3B;AAEM,SAAU,0BAA0B,UAAyB;AACjE,QAAM,gBAAgB,SAAS,QAAQ,UAAa,SAAS,IAAI,SAAS,SAAS,MAAM,OACrF,IAAI,SAAS,IAAI,OAAO,MACxB;AACJ,SAAO,GAAG,SAAS,MAAM,OAAO,IAAI;AACtC;AAEA,SAAS,gBAAgB,IAAsB,MAAwB;AAhcvE;AAicE,QAAM,OAAO,6BAAM,IAAI,KAAK;AAC5B,QAAM,WAAW,6BAAM;AACvB,MAAI,CAAC,YAAY,CAAC,KAAK,KAAK;AAC1B,WAAO;;AAET,QAAM,eAAe,GAAG,SAAS,KAAK,KAAK,QAAQ;AACnD,QAAM,QAAO,gBAAK,kBAAL,mBAAoB,eAApB,YAAkC,KAAK;AACpD,QAAM,UAAU,GAAG,QAAQ,MAAM,YAAY;AAC7C,SAAO;AACT;AAEA,SAAS,iBAAiB,KAAmC;AAE3D,SAAO,EAAC,MAAM,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAM;AAChD;AAEA,SAAS,QAAQ,MAAc;AAC7B,MAAI,KAAK,KAAK,SAAS,QAAQ,KAAK,KAAK,OAAO,MAAM;AACpD,WAAO;;AAET,SAAO,KAAK,IAAI,KAAK,KAAK,UAAU,KAAK,KAAK,OAAO,KAAK,KAAK,GAAG;AACpE;", "names": ["call", "q"]}