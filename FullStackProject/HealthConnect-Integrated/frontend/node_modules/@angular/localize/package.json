{"name": "@angular/localize", "version": "16.2.12", "description": "Angular - library for localizing messages", "bin": {"localize-translate": "./tools/bundles/src/translate/cli.js", "localize-extract": "./tools/bundles/src/extract/cli.js", "localize-migrate": "./tools/bundles/src/migrate/cli.js"}, "exports": {"./tools": {"types": "./tools/index.d.ts", "default": "./tools/bundles/index.js"}, "./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/index.mjs", "esm": "./esm2022/index.mjs", "default": "./fesm2022/localize.mjs"}, "./init": {"types": "./init/index.d.ts", "esm2022": "./esm2022/init/index.mjs", "esm": "./esm2022/init/index.mjs", "default": "./fesm2022/init.mjs"}}, "author": "angular", "license": "MIT", "engines": {"node": "^16.14.0 || >=18.10.0"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git"}, "schematics": "./schematics/collection.json", "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "ng-add": {"save": "devDependencies"}, "sideEffects": ["./esm2022/init/**", "./fesm2022/init.mjs"], "dependencies": {"@babel/core": "7.23.2", "fast-glob": "3.3.0", "yargs": "^17.2.1"}, "peerDependencies": {"@angular/compiler": "16.2.12", "@angular/compiler-cli": "16.2.12"}, "module": "./fesm2022/localize.mjs", "typings": "./index.d.ts", "type": "module"}