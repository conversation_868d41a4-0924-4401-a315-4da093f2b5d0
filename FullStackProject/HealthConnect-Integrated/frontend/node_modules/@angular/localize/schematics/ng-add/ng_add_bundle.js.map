{"version": 3, "sources": ["../../../../../../../packages/localize/schematics/ng-add/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n *\n * @fileoverview Schematics for `ng add @angular/localize` schematic.\n */\n\nimport {chain, noop, Rule, SchematicContext, SchematicsException, Tree,} from '@angular-devkit/schematics';\nimport {NodePackageInstallTask} from '@angular-devkit/schematics/tasks';\nimport {addPackageJsonDependency, NodeDependencyType, removePackageJsonDependency,} from '@schematics/angular/utility/dependencies';\nimport {JSONFile, JSONPath} from '@schematics/angular/utility/json-file';\nimport {getWorkspace} from '@schematics/angular/utility/workspace';\nimport {Builders} from '@schematics/angular/utility/workspace-models';\n\nimport {Schema} from './schema';\n\nconst localizeType = `@angular/localize`;\nconst localizeTripleSlashType = `/// <reference types=\"@angular/localize\" />`;\n\nfunction addTypeScriptConfigTypes(projectName: string): Rule {\n  return async (host: Tree) => {\n    const workspace = await getWorkspace(host);\n    const project = workspace.projects.get(projectName);\n    if (!project) {\n      throw new SchematicsException(`Invalid project name '${projectName}'.`);\n    }\n\n    // We add the root workspace tsconfig for better IDE support.\n    const tsConfigFiles = new Set<string>();\n    for (const target of project.targets.values()) {\n      switch (target.builder) {\n        case Builders.Karma:\n        case Builders.Server:\n        case Builders.Browser:\n          const value = target.options?.['tsConfig'];\n          if (typeof value === 'string') {\n            tsConfigFiles.add(value);\n          }\n\n          break;\n      }\n\n      if (target.builder === Builders.Browser) {\n        const value = target.options?.['main'];\n        if (typeof value === 'string') {\n          addTripleSlashType(host, value);\n        }\n      }\n    }\n\n    const typesJsonPath: JSONPath = ['compilerOptions', 'types'];\n    for (const path of tsConfigFiles) {\n      if (!host.exists(path)) {\n        continue;\n      }\n\n      const json = new JSONFile(host, path);\n      const types = json.get(typesJsonPath) ?? [];\n      if (!Array.isArray(types)) {\n        throw new SchematicsException(`TypeScript configuration file '${\n            path}' has an invalid 'types' property. It must be an array.`);\n      }\n\n      const hasLocalizeType =\n          types.some((t) => t === localizeType || t === '@angular/localize/init');\n      if (hasLocalizeType) {\n        // Skip has already localize type.\n        continue;\n      }\n\n      json.modify(typesJsonPath, [...types, localizeType]);\n    }\n  };\n}\n\nfunction addTripleSlashType(host: Tree, path: string): void {\n  const content = host.readText(path);\n  if (!content.includes(localizeTripleSlashType)) {\n    host.overwrite(path, localizeTripleSlashType + '\\n\\n' + content);\n  }\n}\n\nfunction moveToDependencies(host: Tree, context: SchematicContext): void {\n  if (!host.exists('package.json')) {\n    return;\n  }\n\n  // Remove the previous dependency and add in a new one under the desired type.\n  removePackageJsonDependency(host, '@angular/localize');\n  addPackageJsonDependency(host, {\n    name: '@angular/localize',\n    type: NodeDependencyType.Default,\n    version: `~16.2.12`,\n  });\n\n  // Add a task to run the package manager. This is necessary because we updated\n  // \"package.json\" and we want lock files to reflect this.\n  context.addTask(new NodePackageInstallTask());\n}\n\nexport default function(options: Schema): Rule {\n  return () => {\n    // We favor the name option because the project option has a\n    // smart default which can be populated even when unspecified by the user.\n    const projectName = options.name ?? options.project;\n\n    if (!projectName) {\n      throw new SchematicsException('Option \"project\" is required.');\n    }\n\n    return chain([\n      addTypeScriptConfigTypes(projectName),\n      // If `$localize` will be used at runtime then must install `@angular/localize`\n      // into `dependencies`, rather than the default of `devDependencies`.\n      options.useAtRuntime ? moveToDependencies : noop(),\n    ]);\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;AAUA,wBAA8E;AAC9E,mBAAqC;AACrC,0BAAyF;AACzF,uBAAiC;AACjC,uBAA2B;AAC3B,8BAAuB;AAIvB,IAAM,eAAe;AACrB,IAAM,0BAA0B;AAEhC,SAAS,yBAAyB,aAAmB;AACnD,SAAO,CAAO,SAAc;AAvB9B;AAwBI,UAAM,YAAY,UAAM,+BAAa,IAAI;AACzC,UAAM,UAAU,UAAU,SAAS,IAAI,WAAW;AAClD,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,sCAAoB,yBAAyB,eAAe;;AAIxE,UAAM,gBAAgB,oBAAI,IAAG;AAC7B,eAAW,UAAU,QAAQ,QAAQ,OAAM,GAAI;AAC7C,cAAQ,OAAO,SAAS;QACtB,KAAK,iCAAS;QACd,KAAK,iCAAS;QACd,KAAK,iCAAS;AACZ,gBAAM,SAAQ,YAAO,YAAP,mBAAiB;AAC/B,cAAI,OAAO,UAAU,UAAU;AAC7B,0BAAc,IAAI,KAAK;;AAGzB;;AAGJ,UAAI,OAAO,YAAY,iCAAS,SAAS;AACvC,cAAM,SAAQ,YAAO,YAAP,mBAAiB;AAC/B,YAAI,OAAO,UAAU,UAAU;AAC7B,6BAAmB,MAAM,KAAK;;;;AAKpC,UAAM,gBAA0B,CAAC,mBAAmB,OAAO;AAC3D,eAAW,QAAQ,eAAe;AAChC,UAAI,CAAC,KAAK,OAAO,IAAI,GAAG;AACtB;;AAGF,YAAM,OAAO,IAAI,0BAAS,MAAM,IAAI;AACpC,YAAM,SAAQ,UAAK,IAAI,aAAa,MAAtB,YAA2B,CAAA;AACzC,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,cAAM,IAAI,sCAAoB,kCAC1B,6DAA6D;;AAGnE,YAAM,kBACF,MAAM,KAAK,CAAC,MAAM,MAAM,gBAAgB,MAAM,wBAAwB;AAC1E,UAAI,iBAAiB;AAEnB;;AAGF,WAAK,OAAO,eAAe,CAAC,GAAG,OAAO,YAAY,CAAC;;EAEvD;AACF;AAEA,SAAS,mBAAmB,MAAY,MAAY;AAClD,QAAM,UAAU,KAAK,SAAS,IAAI;AAClC,MAAI,CAAC,QAAQ,SAAS,uBAAuB,GAAG;AAC9C,SAAK,UAAU,MAAM,0BAA0B,SAAS,OAAO;;AAEnE;AAEA,SAAS,mBAAmB,MAAY,SAAyB;AAC/D,MAAI,CAAC,KAAK,OAAO,cAAc,GAAG;AAChC;;AAIF,uDAA4B,MAAM,mBAAmB;AACrD,oDAAyB,MAAM;IAC7B,MAAM;IACN,MAAM,uCAAmB;IACzB,SAAS;GACV;AAID,UAAQ,QAAQ,IAAI,oCAAsB,CAAE;AAC9C;AAEc,SAAP,eAAiB,SAAe;AACrC,SAAO,MAAK;AAxGd;AA2GI,UAAM,eAAc,aAAQ,SAAR,YAAgB,QAAQ;AAE5C,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,sCAAoB,+BAA+B;;AAG/D,eAAO,yBAAM;MACX,yBAAyB,WAAW;MAGpC,QAAQ,eAAe,yBAAqB,wBAAI;KACjD;EACH;AACF;", "names": []}