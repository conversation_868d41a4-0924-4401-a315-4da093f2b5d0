{"version": 3, "file": "init.mjs", "sources": ["../../../../../../packages/localize/init/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ɵ$localize as $localize, ɵLocalizeFn as LocalizeFn, ɵTranslateFn as TranslateFn} from '@angular/localize';\n\nexport {$localize, LocalizeFn, TranslateFn};\n\n// Attach $localize to the global context, as a side-effect of this module.\n(globalThis as any).$localize = $localize;\n"], "names": ["$localize"], "mappings": ";;;;;;;;;AAWA;AACC,UAAkB,CAAC,SAAS,GAAGA,UAAS"}