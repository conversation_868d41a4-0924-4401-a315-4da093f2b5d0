{"version": 3, "file": "localize.mjs", "sources": ["../../../../../../packages/localize/src/utils/src/constants.ts", "../../../../../../packages/compiler/src/i18n/big_integer.ts", "../../../../../../packages/compiler/src/i18n/digest.ts", "../../../../../../packages/localize/src/utils/src/messages.ts", "../../../../../../packages/localize/src/utils/src/translations.ts", "../../../../../../packages/localize/src/translate.ts", "../../../../../../packages/localize/src/localize/src/localize.ts", "../../../../../../packages/localize/private.ts", "../../../../../../packages/localize/localize.ts", "../../../../../../packages/localize/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nexport const BLOCK_MARKER = ':';\n\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nexport const MEANING_SEPARATOR = '|';\n\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nexport const ID_SEPARATOR = '@@';\n\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nexport const LEGACY_ID_INDICATOR = '\\u241F';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Represents a big integer using a buffer of its individual digits, with the least significant\n * digit stored at the beginning of the array (little endian).\n *\n * For performance reasons, each instance is mutable. The addition operation can be done in-place\n * to reduce memory pressure of allocation for the digits array.\n */\nexport class BigInteger {\n  static zero(): BigInteger {\n    return new BigInteger([0]);\n  }\n\n  static one(): BigInteger {\n    return new BigInteger([1]);\n  }\n\n  /**\n   * Creates a big integer using its individual digits in little endian storage.\n   */\n  private constructor(private readonly digits: number[]) {}\n\n  /**\n   * Creates a clone of this instance.\n   */\n  clone(): BigInteger {\n    return new BigInteger(this.digits.slice());\n  }\n\n  /**\n   * Returns a new big integer with the sum of `this` and `other` as its value. This does not mutate\n   * `this` but instead returns a new instance, unlike `addToSelf`.\n   */\n  add(other: BigInteger): BigInteger {\n    const result = this.clone();\n    result.addToSelf(other);\n    return result;\n  }\n\n  /**\n   * Adds `other` to the instance itself, thereby mutating its value.\n   */\n  addToSelf(other: BigInteger): void {\n    const maxNrOfDigits = Math.max(this.digits.length, other.digits.length);\n    let carry = 0;\n    for (let i = 0; i < maxNrOfDigits; i++) {\n      let digitSum = carry;\n      if (i < this.digits.length) {\n        digitSum += this.digits[i];\n      }\n      if (i < other.digits.length) {\n        digitSum += other.digits[i];\n      }\n\n      if (digitSum >= 10) {\n        this.digits[i] = digitSum - 10;\n        carry = 1;\n      } else {\n        this.digits[i] = digitSum;\n        carry = 0;\n      }\n    }\n\n    // Apply a remaining carry if needed.\n    if (carry > 0) {\n      this.digits[maxNrOfDigits] = 1;\n    }\n  }\n\n  /**\n   * Builds the decimal string representation of the big integer. As this is stored in\n   * little endian, the digits are concatenated in reverse order.\n   */\n  toString(): string {\n    let res = '';\n    for (let i = this.digits.length - 1; i >= 0; i--) {\n      res += this.digits[i];\n    }\n    return res;\n  }\n}\n\n/**\n * Represents a big integer which is optimized for multiplication operations, as its power-of-twos\n * are memoized. See `multiplyBy()` for details on the multiplication algorithm.\n */\nexport class BigIntForMultiplication {\n  /**\n   * Stores all memoized power-of-twos, where each index represents `this.number * 2^index`.\n   */\n  private readonly powerOfTwos: BigInteger[];\n\n  constructor(value: BigInteger) {\n    this.powerOfTwos = [value];\n  }\n\n  /**\n   * Returns the big integer itself.\n   */\n  getValue(): BigInteger {\n    return this.powerOfTwos[0];\n  }\n\n  /**\n   * Computes the value for `num * b`, where `num` is a JS number and `b` is a big integer. The\n   * value for `b` is represented by a storage model that is optimized for this computation.\n   *\n   * This operation is implemented in N(log2(num)) by continuous halving of the number, where the\n   * least-significant bit (LSB) is tested in each iteration. If the bit is set, the bit's index is\n   * used as exponent into the power-of-two multiplication of `b`.\n   *\n   * As an example, consider the multiplication num=42, b=1337. In binary 42 is 0b00101010 and the\n   * algorithm unrolls into the following iterations:\n   *\n   *  Iteration | num        | LSB  | b * 2^iter | Add? | product\n   * -----------|------------|------|------------|------|--------\n   *  0         | 0b00101010 | 0    | 1337       | No   | 0\n   *  1         | 0b00010101 | 1    | 2674       | Yes  | 2674\n   *  2         | 0b00001010 | 0    | 5348       | No   | 2674\n   *  3         | 0b00000101 | 1    | 10696      | Yes  | 13370\n   *  4         | 0b00000010 | 0    | 21392      | No   | 13370\n   *  5         | 0b00000001 | 1    | 42784      | Yes  | 56154\n   *  6         | 0b00000000 | 0    | 85568      | No   | 56154\n   *\n   * The computed product of 56154 is indeed the correct result.\n   *\n   * The `BigIntForMultiplication` representation for a big integer provides memoized access to the\n   * power-of-two values to reduce the workload in computing those values.\n   */\n  multiplyBy(num: number): BigInteger {\n    const product = BigInteger.zero();\n    this.multiplyByAndAddTo(num, product);\n    return product;\n  }\n\n  /**\n   * See `multiplyBy()` for details. This function allows for the computed product to be added\n   * directly to the provided result big integer.\n   */\n  multiplyByAndAddTo(num: number, result: BigInteger): void {\n    for (let exponent = 0; num !== 0; num = num >>> 1, exponent++) {\n      if (num & 1) {\n        const value = this.getMultipliedByPowerOfTwo(exponent);\n        result.addToSelf(value);\n      }\n    }\n  }\n\n  /**\n   * Computes and memoizes the big integer value for `this.number * 2^exponent`.\n   */\n  private getMultipliedByPowerOfTwo(exponent: number): BigInteger {\n    // Compute the powers up until the requested exponent, where each value is computed from its\n    // predecessor. This is simple as `this.number * 2^(exponent - 1)` only has to be doubled (i.e.\n    // added to itself) to reach `this.number * 2^exponent`.\n    for (let i = this.powerOfTwos.length; i <= exponent; i++) {\n      const previousPower = this.powerOfTwos[i - 1];\n      this.powerOfTwos[i] = previousPower.add(previousPower);\n    }\n    return this.powerOfTwos[exponent];\n  }\n}\n\n/**\n * Represents an exponentiation operation for the provided base, of which exponents are computed and\n * memoized. The results are represented by a `BigIntForMultiplication` which is tailored for\n * multiplication operations by memoizing the power-of-twos. This effectively results in a matrix\n * representation that is lazily computed upon request.\n */\nexport class BigIntExponentiation {\n  private readonly exponents = [new BigIntForMultiplication(BigInteger.one())];\n\n  constructor(private readonly base: number) {}\n\n  /**\n   * Compute the value for `this.base^exponent`, resulting in a big integer that is optimized for\n   * further multiplication operations.\n   */\n  toThePowerOf(exponent: number): BigIntForMultiplication {\n    // Compute the results up until the requested exponent, where every value is computed from its\n    // predecessor. This is because `this.base^(exponent - 1)` only has to be multiplied by `base`\n    // to reach `this.base^exponent`.\n    for (let i = this.exponents.length; i <= exponent; i++) {\n      const value = this.exponents[i - 1].multiplyBy(this.base);\n      this.exponents[i] = new BigIntForMultiplication(value);\n    }\n    return this.exponents[exponent];\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Byte} from '../util';\n\nimport {BigIntExponentiation} from './big_integer';\nimport * as i18n from './i18n_ast';\n\n/**\n * A lazily created TextEncoder instance for converting strings into UTF-8 bytes\n */\nlet textEncoder: TextEncoder|undefined;\n\n/**\n * Return the message id or compute it using the XLIFF1 digest.\n */\nexport function digest(message: i18n.Message): string {\n  return message.id || computeDigest(message);\n}\n\n/**\n * Compute the message id using the XLIFF1 digest.\n */\nexport function computeDigest(message: i18n.Message): string {\n  return sha1(serializeNodes(message.nodes).join('') + `[${message.meaning}]`);\n}\n\n/**\n * Return the message id or compute it using the XLIFF2/XMB/$localize digest.\n */\nexport function decimalDigest(message: i18n.Message): string {\n  return message.id || computeDecimalDigest(message);\n}\n\n/**\n * Compute the message id using the XLIFF2/XMB/$localize digest.\n */\nexport function computeDecimalDigest(message: i18n.Message): string {\n  const visitor = new _SerializerIgnoreIcuExpVisitor();\n  const parts = message.nodes.map(a => a.visit(visitor, null));\n  return computeMsgId(parts.join(''), message.meaning);\n}\n\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * The visitor is also used in the i18n parser tests\n *\n * @internal\n */\nclass _SerializerVisitor implements i18n.Visitor {\n  visitText(text: i18n.Text, context: any): any {\n    return text.value;\n  }\n\n  visitContainer(container: i18n.Container, context: any): any {\n    return `[${container.children.map(child => child.visit(this)).join(', ')}]`;\n  }\n\n  visitIcu(icu: i18n.Icu, context: any): any {\n    const strCases =\n        Object.keys(icu.cases).map((k: string) => `${k} {${icu.cases[k].visit(this)}}`);\n    return `{${icu.expression}, ${icu.type}, ${strCases.join(', ')}}`;\n  }\n\n  visitTagPlaceholder(ph: i18n.TagPlaceholder, context: any): any {\n    return ph.isVoid ?\n        `<ph tag name=\"${ph.startName}\"/>` :\n        `<ph tag name=\"${ph.startName}\">${\n            ph.children.map(child => child.visit(this)).join(', ')}</ph name=\"${ph.closeName}\">`;\n  }\n\n  visitPlaceholder(ph: i18n.Placeholder, context: any): any {\n    return ph.value ? `<ph name=\"${ph.name}\">${ph.value}</ph>` : `<ph name=\"${ph.name}\"/>`;\n  }\n\n  visitIcuPlaceholder(ph: i18n.IcuPlaceholder, context?: any): any {\n    return `<ph icu name=\"${ph.name}\">${ph.value.visit(this)}</ph>`;\n  }\n}\n\nconst serializerVisitor = new _SerializerVisitor();\n\nexport function serializeNodes(nodes: i18n.Node[]): string[] {\n  return nodes.map(a => a.visit(serializerVisitor, null));\n}\n\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * Ignore the ICU expressions so that message IDs stays identical if only the expression changes.\n *\n * @internal\n */\nclass _SerializerIgnoreIcuExpVisitor extends _SerializerVisitor {\n  override visitIcu(icu: i18n.Icu, context: any): any {\n    let strCases = Object.keys(icu.cases).map((k: string) => `${k} {${icu.cases[k].visit(this)}}`);\n    // Do not take the expression into account\n    return `{${icu.type}, ${strCases.join(', ')}}`;\n  }\n}\n\n/**\n * Compute the SHA1 of the given string\n *\n * see https://csrc.nist.gov/publications/fips/fips180-4/fips-180-4.pdf\n *\n * WARNING: this function has not been designed not tested with security in mind.\n *          DO NOT USE IT IN A SECURITY SENSITIVE CONTEXT.\n */\nexport function sha1(str: string): string {\n  textEncoder ??= new TextEncoder();\n  const utf8 = [...textEncoder.encode(str)];\n  const words32 = bytesToWords32(utf8, Endian.Big);\n  const len = utf8.length * 8;\n\n  const w = new Uint32Array(80);\n  let a = 0x67452301, b = 0xefcdab89, c = 0x98badcfe, d = 0x10325476, e = 0xc3d2e1f0;\n\n  words32[len >> 5] |= 0x80 << (24 - len % 32);\n  words32[((len + 64 >> 9) << 4) + 15] = len;\n\n  for (let i = 0; i < words32.length; i += 16) {\n    const h0 = a, h1 = b, h2 = c, h3 = d, h4 = e;\n\n    for (let j = 0; j < 80; j++) {\n      if (j < 16) {\n        w[j] = words32[i + j];\n      } else {\n        w[j] = rol32(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n      }\n\n      const fkVal = fk(j, b, c, d);\n      const f = fkVal[0];\n      const k = fkVal[1];\n      const temp = [rol32(a, 5), f, e, k, w[j]].reduce(add32);\n      e = d;\n      d = c;\n      c = rol32(b, 30);\n      b = a;\n      a = temp;\n    }\n    a = add32(a, h0);\n    b = add32(b, h1);\n    c = add32(c, h2);\n    d = add32(d, h3);\n    e = add32(e, h4);\n  }\n\n  // Convert the output parts to a 160-bit hexadecimal string\n  return toHexU32(a) + toHexU32(b) + toHexU32(c) + toHexU32(d) + toHexU32(e);\n}\n\n/**\n * Convert and format a number as a string representing a 32-bit unsigned hexadecimal number.\n * @param value The value to format as a string.\n * @returns A hexadecimal string representing the value.\n */\nfunction toHexU32(value: number): string {\n  // unsigned right shift of zero ensures an unsigned 32-bit number\n  return (value >>> 0).toString(16).padStart(8, '0');\n}\n\nfunction fk(index: number, b: number, c: number, d: number): [number, number] {\n  if (index < 20) {\n    return [(b & c) | (~b & d), 0x5a827999];\n  }\n\n  if (index < 40) {\n    return [b ^ c ^ d, 0x6ed9eba1];\n  }\n\n  if (index < 60) {\n    return [(b & c) | (b & d) | (c & d), 0x8f1bbcdc];\n  }\n\n  return [b ^ c ^ d, 0xca62c1d6];\n}\n\n/**\n * Compute the fingerprint of the given string\n *\n * The output is 64 bit number encoded as a decimal string\n *\n * based on:\n * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java\n */\nexport function fingerprint(str: string): [number, number] {\n  textEncoder ??= new TextEncoder();\n  const utf8 = textEncoder.encode(str);\n  const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);\n\n  let hi = hash32(view, utf8.length, 0);\n  let lo = hash32(view, utf8.length, 102072);\n\n  if (hi == 0 && (lo == 0 || lo == 1)) {\n    hi = hi ^ 0x130f9bef;\n    lo = lo ^ -0x6b5f56d8;\n  }\n\n  return [hi, lo];\n}\n\nexport function computeMsgId(msg: string, meaning: string = ''): string {\n  let msgFingerprint = fingerprint(msg);\n\n  if (meaning) {\n    const meaningFingerprint = fingerprint(meaning);\n    msgFingerprint = add64(rol64(msgFingerprint, 1), meaningFingerprint);\n  }\n\n  const hi = msgFingerprint[0];\n  const lo = msgFingerprint[1];\n\n  return wordsToDecimalString(hi & 0x7fffffff, lo);\n}\n\nfunction hash32(view: DataView, length: number, c: number): number {\n  let a = 0x9e3779b9, b = 0x9e3779b9;\n  let index = 0;\n\n  const end = length - 12;\n  for (; index <= end; index += 12) {\n    a += view.getUint32(index, true);\n    b += view.getUint32(index + 4, true);\n    c += view.getUint32(index + 8, true);\n    const res = mix(a, b, c);\n    a = res[0], b = res[1], c = res[2];\n  }\n\n  const remainder = length - index;\n\n  // the first byte of c is reserved for the length\n  c += length;\n\n  if (remainder >= 4) {\n    a += view.getUint32(index, true);\n    index += 4;\n\n    if (remainder >= 8) {\n      b += view.getUint32(index, true);\n      index += 4;\n\n      // Partial 32-bit word for c\n      if (remainder >= 9) {\n        c += view.getUint8(index++) << 8;\n      }\n      if (remainder >= 10) {\n        c += view.getUint8(index++) << 16;\n      }\n      if (remainder === 11) {\n        c += view.getUint8(index++) << 24;\n      }\n    } else {\n      // Partial 32-bit word for b\n      if (remainder >= 5) {\n        b += view.getUint8(index++);\n      }\n      if (remainder >= 6) {\n        b += view.getUint8(index++) << 8;\n      }\n      if (remainder === 7) {\n        b += view.getUint8(index++) << 16;\n      }\n    }\n  } else {\n    // Partial 32-bit word for a\n    if (remainder >= 1) {\n      a += view.getUint8(index++);\n    }\n    if (remainder >= 2) {\n      a += view.getUint8(index++) << 8;\n    }\n    if (remainder === 3) {\n      a += view.getUint8(index++) << 16;\n    }\n  }\n\n  return mix(a, b, c)[2];\n}\n\n// clang-format off\nfunction mix(a: number, b: number, c: number): [number, number, number] {\n  a -= b; a -= c; a ^= c >>> 13;\n  b -= c; b -= a; b ^= a << 8;\n  c -= a; c -= b; c ^= b >>> 13;\n  a -= b; a -= c; a ^= c >>> 12;\n  b -= c; b -= a; b ^= a << 16;\n  c -= a; c -= b; c ^= b >>> 5;\n  a -= b; a -= c; a ^= c >>> 3;\n  b -= c; b -= a; b ^= a << 10;\n  c -= a; c -= b; c ^= b >>> 15;\n  return [a, b, c];\n}\n// clang-format on\n\n// Utils\n\nenum Endian {\n  Little,\n  Big,\n}\n\nfunction add32(a: number, b: number): number {\n  return add32to64(a, b)[1];\n}\n\nfunction add32to64(a: number, b: number): [number, number] {\n  const low = (a & 0xffff) + (b & 0xffff);\n  const high = (a >>> 16) + (b >>> 16) + (low >>> 16);\n  return [high >>> 16, (high << 16) | (low & 0xffff)];\n}\n\nfunction add64(a: [number, number], b: [number, number]): [number, number] {\n  const ah = a[0], al = a[1];\n  const bh = b[0], bl = b[1];\n  const result = add32to64(al, bl);\n  const carry = result[0];\n  const l = result[1];\n  const h = add32(add32(ah, bh), carry);\n  return [h, l];\n}\n\n// Rotate a 32b number left `count` position\nfunction rol32(a: number, count: number): number {\n  return (a << count) | (a >>> (32 - count));\n}\n\n// Rotate a 64b number left `count` position\nfunction rol64(num: [number, number], count: number): [number, number] {\n  const hi = num[0], lo = num[1];\n  const h = (hi << count) | (lo >>> (32 - count));\n  const l = (lo << count) | (hi >>> (32 - count));\n  return [h, l];\n}\n\nfunction bytesToWords32(bytes: Byte[], endian: Endian): number[] {\n  const size = (bytes.length + 3) >>> 2;\n  const words32 = [];\n\n  for (let i = 0; i < size; i++) {\n    words32[i] = wordAt(bytes, i * 4, endian);\n  }\n\n  return words32;\n}\n\nfunction byteAt(bytes: Byte[], index: number): Byte {\n  return index >= bytes.length ? 0 : bytes[index];\n}\n\nfunction wordAt(bytes: Byte[], index: number, endian: Endian): number {\n  let word = 0;\n  if (endian === Endian.Big) {\n    for (let i = 0; i < 4; i++) {\n      word += byteAt(bytes, index + i) << (24 - 8 * i);\n    }\n  } else {\n    for (let i = 0; i < 4; i++) {\n      word += byteAt(bytes, index + i) << 8 * i;\n    }\n  }\n  return word;\n}\n\n/**\n * Create a shared exponentiation pool for base-256 computations. This shared pool provides memoized\n * power-of-256 results with memoized power-of-two computations for efficient multiplication.\n *\n * For our purposes, this can be safely stored as a global without memory concerns. The reason is\n * that we encode two words, so only need the 0th (for the low word) and 4th (for the high word)\n * exponent.\n */\nconst base256 = new BigIntExponentiation(256);\n\n/**\n * Represents two 32-bit words as a single decimal number. This requires a big integer storage\n * model as JS numbers are not accurate enough to represent the 64-bit number.\n *\n * Based on https://www.danvk.org/hex2dec.html\n */\nfunction wordsToDecimalString(hi: number, lo: number): string {\n  // Encode the four bytes in lo in the lower digits of the decimal number.\n  // Note: the multiplication results in lo itself but represented by a big integer using its\n  // decimal digits.\n  const decimal = base256.toThePowerOf(0).multiplyBy(lo);\n\n  // Encode the four bytes in hi above the four lo bytes. lo is a maximum of (2^8)^4, which is why\n  // this multiplication factor is applied.\n  base256.toThePowerOf(4).multiplyByAndAddTo(hi, decimal);\n\n  return decimal.toString();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This module specifier is intentionally a relative path to allow bundling the code directly\n// into the package.\nimport {computeMsgId} from '../../../../compiler/src/i18n/digest';\n\nimport {BLOCK_MARKER, ID_SEPARATOR, LEGACY_ID_INDICATOR, MEANING_SEPARATOR} from './constants';\n\n/**\n * Re-export this helper function so that users of `@angular/localize` don't need to actively import\n * from `@angular/compiler`.\n */\nexport {computeMsgId};\n\n/**\n * A string containing a translation source message.\n *\n * I.E. the message that indicates what will be translated from.\n *\n * Uses `{$placeholder-name}` to indicate a placeholder.\n */\nexport type SourceMessage = string;\n\n/**\n * A string containing a translation target message.\n *\n * I.E. the message that indicates what will be translated to.\n *\n * Uses `{$placeholder-name}` to indicate a placeholder.\n */\nexport type TargetMessage = string;\n\n/**\n * A string that uniquely identifies a message, to be used for matching translations.\n */\nexport type MessageId = string;\n\n/**\n * Declares a copy of the `AbsoluteFsPath` branded type in `@angular/compiler-cli` to avoid an\n * import into `@angular/compiler-cli`. The compiler-cli's declaration files are not necessarily\n * compatible with web environments that use `@angular/localize`, and would inadvertently include\n * `typescript` declaration files in any compilation unit that uses `@angular/localize` (which\n * increases parsing time and memory usage during builds) using a default import that only\n * type-checks when `allowSyntheticDefaultImports` is enabled.\n *\n * @see https://github.com/angular/angular/issues/45179\n */\ntype AbsoluteFsPathLocalizeCopy = string&{_brand: 'AbsoluteFsPath'};\n\n/**\n * The location of the message in the source file.\n *\n * The `line` and `column` values for the `start` and `end` properties are zero-based.\n */\nexport interface SourceLocation {\n  start: {line: number, column: number};\n  end: {line: number, column: number};\n  file: AbsoluteFsPathLocalizeCopy;\n  text?: string;\n}\n\n/**\n * Additional information that can be associated with a message.\n */\nexport interface MessageMetadata {\n  /**\n   * A human readable rendering of the message\n   */\n  text: string;\n  /**\n   * Legacy message ids, if provided.\n   *\n   * In legacy message formats the message id can only be computed directly from the original\n   * template source.\n   *\n   * Since this information is not available in `$localize` calls, the legacy message ids may be\n   * attached by the compiler to the `$localize` metablock so it can be used if needed at the point\n   * of translation if the translations are encoded using the legacy message id.\n   */\n  legacyIds?: string[];\n  /**\n   * The id of the `message` if a custom one was specified explicitly.\n   *\n   * This id overrides any computed or legacy ids.\n   */\n  customId?: string;\n  /**\n   * The meaning of the `message`, used to distinguish identical `messageString`s.\n   */\n  meaning?: string;\n  /**\n   * The description of the `message`, used to aid translation.\n   */\n  description?: string;\n  /**\n   * The location of the message in the source.\n   */\n  location?: SourceLocation;\n}\n\n/**\n * Information parsed from a `$localize` tagged string that is used to translate it.\n *\n * For example:\n *\n * ```\n * const name = 'Jo Bloggs';\n * $localize`Hello ${name}:title@@ID:!`;\n * ```\n *\n * May be parsed into:\n *\n * ```\n * {\n *   id: '6998194507597730591',\n *   substitutions: { title: 'Jo Bloggs' },\n *   messageString: 'Hello {$title}!',\n *   placeholderNames: ['title'],\n *   associatedMessageIds: { title: 'ID' },\n * }\n * ```\n */\nexport interface ParsedMessage extends MessageMetadata {\n  /**\n   * The key used to look up the appropriate translation target.\n   */\n  id: MessageId;\n  /**\n   * A mapping of placeholder names to substitution values.\n   */\n  substitutions: Record<string, any>;\n  /**\n   * An optional mapping of placeholder names to associated MessageIds.\n   * This can be used to match ICU placeholders to the message that contains the ICU.\n   */\n  associatedMessageIds?: Record<string, MessageId>;\n  /**\n   * An optional mapping of placeholder names to source locations\n   */\n  substitutionLocations?: Record<string, SourceLocation|undefined>;\n  /**\n   * The static parts of the message.\n   */\n  messageParts: string[];\n  /**\n   * An optional mapping of message parts to source locations\n   */\n  messagePartLocations?: (SourceLocation|undefined)[];\n  /**\n   * The names of the placeholders that will be replaced with substitutions.\n   */\n  placeholderNames: string[];\n}\n\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nexport function parseMessage(\n    messageParts: TemplateStringsArray, expressions?: readonly any[], location?: SourceLocation,\n    messagePartLocations?: (SourceLocation|undefined)[],\n    expressionLocations: (SourceLocation|undefined)[] = []): ParsedMessage {\n  const substitutions: {[placeholderName: string]: any} = {};\n  const substitutionLocations: {[placeholderName: string]: SourceLocation|undefined} = {};\n  const associatedMessageIds: {[placeholderName: string]: MessageId} = {};\n  const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n  const cleanedMessageParts: string[] = [metadata.text];\n  const placeholderNames: string[] = [];\n  let messageString = metadata.text;\n  for (let i = 1; i < messageParts.length; i++) {\n    const {messagePart, placeholderName = computePlaceholderName(i), associatedMessageId} =\n        parsePlaceholder(messageParts[i], messageParts.raw[i]);\n    messageString += `{$${placeholderName}}${messagePart}`;\n    if (expressions !== undefined) {\n      substitutions[placeholderName] = expressions[i - 1];\n      substitutionLocations[placeholderName] = expressionLocations[i - 1];\n    }\n    placeholderNames.push(placeholderName);\n    if (associatedMessageId !== undefined) {\n      associatedMessageIds[placeholderName] = associatedMessageId;\n    }\n    cleanedMessageParts.push(messagePart);\n  }\n  const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n  const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter(id => id !== messageId) : [];\n  return {\n    id: messageId,\n    legacyIds,\n    substitutions,\n    substitutionLocations,\n    text: messageString,\n    customId: metadata.customId,\n    meaning: metadata.meaning || '',\n    description: metadata.description || '',\n    messageParts: cleanedMessageParts,\n    messagePartLocations,\n    placeholderNames,\n    associatedMessageIds,\n    location,\n  };\n}\n\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nexport function parseMetadata(cooked: string, raw: string): MessageMetadata {\n  const {text: messageString, block} = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {text: messageString};\n  } else {\n    const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n    const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n    let [meaning, description]: (string|undefined)[] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n    if (description === undefined) {\n      description = meaning;\n      meaning = undefined;\n    }\n    if (description === '') {\n      description = undefined;\n    }\n    return {text: messageString, meaning, description, customId, legacyIds};\n  }\n}\n\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nexport function parsePlaceholder(cooked: string, raw: string):\n    {messagePart: string; placeholderName?: string; associatedMessageId?: string;} {\n  const {text: messagePart, block} = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {messagePart};\n  } else {\n    const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n    return {messagePart, placeholderName, associatedMessageId};\n  }\n}\n\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nexport function splitBlock(cooked: string, raw: string): {text: string, block?: string} {\n  if (raw.charAt(0) !== BLOCK_MARKER) {\n    return {text: cooked};\n  } else {\n    const endOfBlock = findEndOfBlock(cooked, raw);\n    return {\n      block: cooked.substring(1, endOfBlock),\n      text: cooked.substring(endOfBlock + 1),\n    };\n  }\n}\n\n\nfunction computePlaceholderName(index: number) {\n  return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nexport function findEndOfBlock(cooked: string, raw: string): number {\n  for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n    if (raw[rawIndex] === '\\\\') {\n      rawIndex++;\n    } else if (cooked[cookedIndex] === BLOCK_MARKER) {\n      return cookedIndex;\n    }\n  }\n  throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {BLOCK_MARKER} from './constants';\nimport {MessageId, MessageMetadata, ParsedMessage, parseMessage, TargetMessage} from './messages';\n\n\n/**\n * A translation message that has been processed to extract the message parts and placeholders.\n */\nexport interface ParsedTranslation extends MessageMetadata {\n  messageParts: TemplateStringsArray;\n  placeholderNames: string[];\n}\n\n/**\n * The internal structure used by the runtime localization to translate messages.\n */\nexport type ParsedTranslations = Record<MessageId, ParsedTranslation>;\n\nexport class MissingTranslationError extends Error {\n  private readonly type = 'MissingTranslationError';\n  constructor(readonly parsedMessage: ParsedMessage) {\n    super(`No translation found for ${describeMessage(parsedMessage)}.`);\n  }\n}\n\nexport function isMissingTranslationError(e: any): e is MissingTranslationError {\n  return e.type === 'MissingTranslationError';\n}\n\n/**\n * Translate the text of the `$localize` tagged-string (i.e. `messageParts` and\n * `substitutions`) using the given `translations`.\n *\n * The tagged-string is parsed to extract its `messageId` which is used to find an appropriate\n * `ParsedTranslation`. If this doesn't match and there are legacy ids then try matching a\n * translation using those.\n *\n * If one is found then it is used to translate the message into a new set of `messageParts` and\n * `substitutions`.\n * The translation may reorder (or remove) substitutions as appropriate.\n *\n * If there is no translation with a matching message id then an error is thrown.\n * If a translation contains a placeholder that is not found in the message being translated then an\n * error is thrown.\n */\nexport function translate(\n    translations: Record<string, ParsedTranslation>, messageParts: TemplateStringsArray,\n    substitutions: readonly any[]): [TemplateStringsArray, readonly any[]] {\n  const message = parseMessage(messageParts, substitutions);\n  // Look up the translation using the messageId, and then the legacyId if available.\n  let translation = translations[message.id];\n  // If the messageId did not match a translation, try matching the legacy ids instead\n  if (message.legacyIds !== undefined) {\n    for (let i = 0; i < message.legacyIds.length && translation === undefined; i++) {\n      translation = translations[message.legacyIds[i]];\n    }\n  }\n  if (translation === undefined) {\n    throw new MissingTranslationError(message);\n  }\n  return [\n    translation.messageParts, translation.placeholderNames.map(placeholder => {\n      if (message.substitutions.hasOwnProperty(placeholder)) {\n        return message.substitutions[placeholder];\n      } else {\n        throw new Error(\n            `There is a placeholder name mismatch with the translation provided for the message ${\n                describeMessage(message)}.\\n` +\n            `The translation contains a placeholder with name ${\n                placeholder}, which does not exist in the message.`);\n      }\n    })\n  ];\n}\n\n/**\n * Parse the `messageParts` and `placeholderNames` out of a target `message`.\n *\n * Used by `loadTranslations()` to convert target message strings into a structure that is more\n * appropriate for doing translation.\n *\n * @param message the message to be parsed.\n */\nexport function parseTranslation(messageString: TargetMessage): ParsedTranslation {\n  const parts = messageString.split(/{\\$([^}]*)}/);\n  const messageParts = [parts[0]];\n  const placeholderNames: string[] = [];\n  for (let i = 1; i < parts.length - 1; i += 2) {\n    placeholderNames.push(parts[i]);\n    messageParts.push(`${parts[i + 1]}`);\n  }\n  const rawMessageParts =\n      messageParts.map(part => part.charAt(0) === BLOCK_MARKER ? '\\\\' + part : part);\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, rawMessageParts),\n    placeholderNames,\n  };\n}\n\n/**\n * Create a `ParsedTranslation` from a set of `messageParts` and `placeholderNames`.\n *\n * @param messageParts The message parts to appear in the ParsedTranslation.\n * @param placeholderNames The names of the placeholders to intersperse between the `messageParts`.\n */\nexport function makeParsedTranslation(\n    messageParts: string[], placeholderNames: string[] = []): ParsedTranslation {\n  let messageString = messageParts[0];\n  for (let i = 0; i < placeholderNames.length; i++) {\n    messageString += `{$${placeholderNames[i]}}${messageParts[i + 1]}`;\n  }\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, messageParts),\n    placeholderNames\n  };\n}\n\n/**\n * Create the specialized array that is passed to tagged-string tag functions.\n *\n * @param cooked The message parts with their escape codes processed.\n * @param raw The message parts with their escaped codes as-is.\n */\nexport function makeTemplateObject(cooked: string[], raw: string[]): TemplateStringsArray {\n  Object.defineProperty(cooked, 'raw', {value: raw});\n  return cooked as any;\n}\n\n\nfunction describeMessage(message: ParsedMessage): string {\n  const meaningString = message.meaning && ` - \"${message.meaning}\"`;\n  const legacy = message.legacyIds && message.legacyIds.length > 0 ?\n      ` [${message.legacyIds.map(l => `\"${l}\"`).join(', ')}]` :\n      '';\n  return `\"${message.id}\"${legacy} (\"${message.text}\"${meaningString})`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {LocalizeFn} from './localize';\nimport {MessageId, ParsedTranslation, parseTranslation, TargetMessage, translate as _translate} from './utils';\n\n/**\n * We augment the `$localize` object to also store the translations.\n *\n * Note that because the TRANSLATIONS are attached to a global object, they will be shared between\n * all applications that are running in a single page of the browser.\n */\ndeclare const $localize: LocalizeFn&{TRANSLATIONS: Record<MessageId, ParsedTranslation>};\n\n/**\n * Load translations for use by `$localize`, if doing runtime translation.\n *\n * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible\n * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,\n * in the browser.\n *\n * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.\n *\n * Note that `$localize` messages are only processed once, when the tagged string is first\n * encountered, and does not provide dynamic language changing without refreshing the browser.\n * Loading new translations later in the application life-cycle will not change the translated text\n * of messages that have already been translated.\n *\n * The message IDs and translations are in the same format as that rendered to \"simple JSON\"\n * translation files when extracting messages. In particular, placeholders in messages are rendered\n * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:\n *\n * ```html\n * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>\n * ```\n *\n * would have the following form in the `translations` map:\n *\n * ```ts\n * {\n *   \"2932901491976224757\":\n *      \"pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post\"\n * }\n * ```\n *\n * @param translations A map from message ID to translated message.\n *\n * These messages are processed and added to a lookup based on their `MessageId`.\n *\n * @see {@link clearTranslations} for removing translations loaded using this function.\n * @see {@link $localize} for tagging messages as needing to be translated.\n * @publicApi\n */\nexport function loadTranslations(translations: Record<MessageId, TargetMessage>) {\n  // Ensure the translate function exists\n  if (!$localize.translate) {\n    $localize.translate = translate;\n  }\n  if (!$localize.TRANSLATIONS) {\n    $localize.TRANSLATIONS = {};\n  }\n  Object.keys(translations).forEach(key => {\n    $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);\n  });\n}\n\n/**\n * Remove all translations for `$localize`, if doing runtime translation.\n *\n * All translations that had been loading into memory using `loadTranslations()` will be removed.\n *\n * @see {@link loadTranslations} for loading translations at runtime.\n * @see {@link $localize} for tagging messages as needing to be translated.\n *\n * @publicApi\n */\nexport function clearTranslations() {\n  $localize.translate = undefined;\n  $localize.TRANSLATIONS = {};\n}\n\n/**\n * Translate the text of the given message, using the loaded translations.\n *\n * This function may reorder (or remove) substitutions as indicated in the matching translation.\n */\nexport function translate(messageParts: TemplateStringsArray, substitutions: readonly any[]):\n    [TemplateStringsArray, readonly any[]] {\n  try {\n    return _translate($localize.TRANSLATIONS, messageParts, substitutions);\n  } catch (e) {\n    console.warn((e as Error).message);\n    return [messageParts, substitutions];\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {findEndOfBlock} from '../../utils';\n\n/** @nodoc */\nexport interface LocalizeFn {\n  (messageParts: TemplateStringsArray, ...expressions: readonly any[]): string;\n\n  /**\n   * A function that converts an input \"message with expressions\" into a translated \"message with\n   * expressions\".\n   *\n   * The conversion may be done in place, modifying the array passed to the function, so\n   * don't assume that this has no side-effects.\n   *\n   * The expressions must be passed in since it might be they need to be reordered for\n   * different translations.\n   */\n  translate?: TranslateFn;\n  /**\n   * The current locale of the translated messages.\n   *\n   * The compile-time translation inliner is able to replace the following code:\n   *\n   * ```\n   * typeof $localize !== \"undefined\" && $localize.locale\n   * ```\n   *\n   * with a string literal of the current locale. E.g.\n   *\n   * ```\n   * \"fr\"\n   * ```\n   */\n  locale?: string;\n}\n\n/** @nodoc */\nexport interface TranslateFn {\n  (messageParts: TemplateStringsArray,\n   expressions: readonly any[]): [TemplateStringsArray, readonly any[]];\n}\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n-common-prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @globalApi\n * @publicApi\n */\nexport const $localize: LocalizeFn = function(\n    messageParts: TemplateStringsArray, ...expressions: readonly any[]) {\n  if ($localize.translate) {\n    // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n    const translation = $localize.translate(messageParts, expressions);\n    messageParts = translation[0];\n    expressions = translation[1];\n  }\n  let message = stripBlock(messageParts[0], messageParts.raw[0]);\n  for (let i = 1; i < messageParts.length; i++) {\n    message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n  }\n  return message;\n};\n\nconst BLOCK_MARKER = ':';\n\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart: string, rawMessagePart: string) {\n  return rawMessagePart.charAt(0) === BLOCK_MARKER ?\n      messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1) :\n      messagePart;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file exports all the `utils` as private exports so that other parts of `@angular/localize`\n// can make use of them.\nexport {$localize as ɵ$localize, LocalizeFn as ɵLocalizeFn, TranslateFn as ɵTranslateFn} from './src/localize';\nexport {computeMsgId as ɵcomputeMsgId, findEndOfBlock as ɵfindEndOfBlock, isMissingTranslationError as ɵisMissingTranslationError, makeParsedTranslation as ɵmakeParsedTranslation, makeTemplateObject as ɵmakeTemplateObject, MissingTranslationError as ɵMissingTranslationError, ParsedMessage as ɵParsedMessage, ParsedTranslation as ɵParsedTranslation, ParsedTranslations as ɵParsedTranslations, parseMessage as ɵparseMessage, parseMetadata as ɵparseMetadata, parseTranslation as ɵparseTranslation, SourceLocation as ɵSourceLocation, SourceMessage as ɵSourceMessage, splitBlock as ɵsplitBlock, translate as ɵtranslate} from './src/utils';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file contains the public API of the `@angular/localize` entry-point\n\nexport {clearTranslations, loadTranslations} from './src/translate';\nexport {MessageId, TargetMessage} from './src/utils';\n\n// Exports that are not part of the public API\nexport * from './private';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// DO NOT ADD public exports to this file.\n// The public API exports are specified in the `./localize` module, which is checked by the\n// public_api_guard rules\n\nexport * from './localize';\n\n// The global declaration must be in the index.d.ts as otherwise it will not be picked up when used\n// with\n// /// <reference types=\"@angular/localize\" />\n\nimport {ɵLocalizeFn} from './localize';\n\n// `declare global` allows us to escape the current module and place types on the global namespace\ndeclare global {\n  /**\n   * Tag a template literal string for localization.\n   *\n   * For example:\n   *\n   * ```ts\n   * $localize `some string to localize`\n   * ```\n   *\n   * **Providing meaning, description and id**\n   *\n   * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n   * string by pre-pending it with a colon delimited block of the form:\n   *\n   * ```ts\n   * $localize`:meaning|description@@id:source message text`;\n   *\n   * $localize`:meaning|:source message text`;\n   * $localize`:description:source message text`;\n   * $localize`:@@id:source message text`;\n   * ```\n   *\n   * This format is the same as that used for `i18n` markers in Angular templates. See the\n   * [Angular i18n guide](guide/i18n-common-prepare#mark-text-in-component-template).\n   *\n   * **Naming placeholders**\n   *\n   * If the template literal string contains expressions, then the expressions will be automatically\n   * associated with placeholder names for you.\n   *\n   * For example:\n   *\n   * ```ts\n   * $localize `Hi ${name}! There are ${items.length} items.`;\n   * ```\n   *\n   * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n   *\n   * The recommended practice is to name the placeholder associated with each expression though.\n   *\n   * Do this by providing the placeholder name wrapped in `:` characters directly after the\n   * expression. These placeholder names are stripped out of the rendered localized string.\n   *\n   * For example, to name the `items.length` expression placeholder `itemCount` you write:\n   *\n   * ```ts\n   * $localize `There are ${items.length}:itemCount: items`;\n   * ```\n   *\n   * **Escaping colon markers**\n   *\n   * If you need to use a `:` character directly at the start of a tagged string that has no\n   * metadata block, or directly after a substitution expression that has no name you must escape\n   * the `:` by preceding it with a backslash:\n   *\n   * For example:\n   *\n   * ```ts\n   * // message has a metadata block so no need to escape colon\n   * $localize `:some description::this message starts with a colon (:)`;\n   * // no metadata block so the colon must be escaped\n   * $localize `\\:this message starts with a colon (:)`;\n   * ```\n   *\n   * ```ts\n   * // named substitution so no need to escape colon\n   * $localize `${label}:label:: ${}`\n   * // anonymous substitution so colon must be escaped\n   * $localize `${label}\\: ${}`\n   * ```\n   *\n   * **Processing localized strings:**\n   *\n   * There are three scenarios:\n   *\n   * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n   * transpiler, removing the tag and replacing the template literal string with a translated\n   * literal string from a collection of translations provided to the transpilation tool.\n   *\n   * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n   * reorders the parts (static strings and expressions) of the template literal string with strings\n   * from a collection of translations loaded at run-time.\n   *\n   * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n   * the original template literal string without applying any translations to the parts. This\n   * version is used during development or where there is no need to translate the localized\n   * template literals.\n   *\n   * @param messageParts a collection of the static parts of the template string.\n   * @param expressions a collection of the values of each placeholder in the template string.\n   * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n   */\n  const $localize: ɵLocalizeFn;\n}\n"], "names": ["BLOCK_MARKER", "translate", "_translate", "$localize"], "mappings": ";;;;;;AAQA;;;;;;;;;;;AAWG;AACI,MAAMA,cAAY,GAAG,GAAG,CAAC;AAEhC;;;;;;;;;AASG;AACI,MAAM,iBAAiB,GAAG,GAAG,CAAC;AAErC;;;;;;;;AAQG;AACI,MAAM,YAAY,GAAG,IAAI,CAAC;AAEjC;;;;;;;;;;;;;;AAcG;AACI,MAAM,mBAAmB,GAAG,QAAQ;;ACpD3C;;;;;;AAMG;MACU,UAAU,CAAA;AACrB,IAAA,OAAO,IAAI,GAAA;AACT,QAAA,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5B;AAED,IAAA,OAAO,GAAG,GAAA;AACR,QAAA,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5B;AAED;;AAEG;AACH,IAAA,WAAA,CAAqC,MAAgB,EAAA;QAAhB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAU;KAAI;AAEzD;;AAEG;IACH,KAAK,GAAA;QACH,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;KAC5C;AAED;;;AAGG;AACH,IAAA,GAAG,CAAC,KAAiB,EAAA;AACnB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;AAC5B,QAAA,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACxB,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;AAEG;AACH,IAAA,SAAS,CAAC,KAAiB,EAAA;AACzB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACxE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,QAAQ,GAAG,KAAK,CAAC;AACrB,YAAA,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAC1B,gBAAA,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,aAAA;AACD,YAAA,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE;AAC3B,gBAAA,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7B,aAAA;YAED,IAAI,QAAQ,IAAI,EAAE,EAAE;gBAClB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,EAAE,CAAC;gBAC/B,KAAK,GAAG,CAAC,CAAC;AACX,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;gBAC1B,KAAK,GAAG,CAAC,CAAC;AACX,aAAA;AACF,SAAA;;QAGD,IAAI,KAAK,GAAG,CAAC,EAAE;AACb,YAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAChC,SAAA;KACF;AAED;;;AAGG;IACH,QAAQ,GAAA;QACN,IAAI,GAAG,GAAG,EAAE,CAAC;AACb,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAChD,YAAA,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACvB,SAAA;AACD,QAAA,OAAO,GAAG,CAAC;KACZ;AACF,CAAA;AAED;;;AAGG;MACU,uBAAuB,CAAA;AAMlC,IAAA,WAAA,CAAY,KAAiB,EAAA;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED;;AAEG;IACH,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;KAC5B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACH,IAAA,UAAU,CAAC,GAAW,EAAA;AACpB,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACtC,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;AAGG;IACH,kBAAkB,CAAC,GAAW,EAAE,MAAkB,EAAA;AAChD,QAAA,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,EAAE;YAC7D,IAAI,GAAG,GAAG,CAAC,EAAE;gBACX,MAAM,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;AACvD,gBAAA,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACzB,aAAA;AACF,SAAA;KACF;AAED;;AAEG;AACK,IAAA,yBAAyB,CAAC,QAAgB,EAAA;;;;AAIhD,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,EAAE;YACxD,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9C,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACxD,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;KACnC;AACF,CAAA;AAED;;;;;AAKG;MACU,oBAAoB,CAAA;AAG/B,IAAA,WAAA,CAA6B,IAAY,EAAA;QAAZ,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAFxB,IAAS,CAAA,SAAA,GAAG,CAAC,IAAI,uBAAuB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;KAEhC;AAE7C;;;AAGG;AACH,IAAA,YAAY,CAAC,QAAgB,EAAA;;;;AAI3B,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,EAAE;AACtD,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,uBAAuB,CAAC,KAAK,CAAC,CAAC;AACxD,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;KACjC;AACF;;ACtLD;;AAEG;AACH,IAAI,WAAkC,CAAC;AAEvC;;AAEG;AACG,SAAU,MAAM,CAAC,OAAqB,EAAA;IAC1C,OAAO,OAAO,CAAC,EAAE,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;AAC9C,CAAC;AAED;;AAEG;AACG,SAAU,aAAa,CAAC,OAAqB,EAAA;IACjD,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAI,CAAA,EAAA,OAAO,CAAC,OAAO,CAAA,CAAA,CAAG,CAAC,CAAC;AAC/E,CAAC;AAED;;AAEG;AACG,SAAU,aAAa,CAAC,OAAqB,EAAA;IACjD,OAAO,OAAO,CAAC,EAAE,IAAI,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACrD,CAAC;AAED;;AAEG;AACG,SAAU,oBAAoB,CAAC,OAAqB,EAAA;AACxD,IAAA,MAAM,OAAO,GAAG,IAAI,8BAA8B,EAAE,CAAC;IACrD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AAC7D,IAAA,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AACvD,CAAC;AAED;;;;;;AAMG;AACH,MAAM,kBAAkB,CAAA;IACtB,SAAS,CAAC,IAAe,EAAE,OAAY,EAAA;QACrC,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IAED,cAAc,CAAC,SAAyB,EAAE,OAAY,EAAA;QACpD,OAAO,CAAA,CAAA,EAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG,CAAC;KAC7E;IAED,QAAQ,CAAC,GAAa,EAAE,OAAY,EAAA;AAClC,QAAA,MAAM,QAAQ,GACV,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAS,KAAK,CAAA,EAAG,CAAC,CAAA,EAAA,EAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG,CAAC,CAAC;AACpF,QAAA,OAAO,IAAI,GAAG,CAAC,UAAU,CAAA,EAAA,EAAK,GAAG,CAAC,IAAI,CAAK,EAAA,EAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;KACnE;IAED,mBAAmB,CAAC,EAAuB,EAAE,OAAY,EAAA;AACvD,QAAA,OAAO,EAAE,CAAC,MAAM;AACZ,YAAA,CAAA,cAAA,EAAiB,EAAE,CAAC,SAAS,CAAA,GAAA,CAAK;AAClC,YAAA,CAAA,cAAA,EAAiB,EAAE,CAAC,SAAS,CAAA,EAAA,EACzB,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAc,WAAA,EAAA,EAAE,CAAC,SAAS,IAAI,CAAC;KAC9F;IAED,gBAAgB,CAAC,EAAoB,EAAE,OAAY,EAAA;QACjD,OAAO,EAAE,CAAC,KAAK,GAAG,CAAA,UAAA,EAAa,EAAE,CAAC,IAAI,CAAA,EAAA,EAAK,EAAE,CAAC,KAAK,CAAO,KAAA,CAAA,GAAG,aAAa,EAAE,CAAC,IAAI,CAAA,GAAA,CAAK,CAAC;KACxF;IAED,mBAAmB,CAAC,EAAuB,EAAE,OAAa,EAAA;AACxD,QAAA,OAAO,CAAiB,cAAA,EAAA,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;KACjE;AACF,CAAA;AAED,MAAM,iBAAiB,GAAG,IAAI,kBAAkB,EAAE,CAAC;AAE7C,SAAU,cAAc,CAAC,KAAkB,EAAA;AAC/C,IAAA,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;AAMG;AACH,MAAM,8BAA+B,SAAQ,kBAAkB,CAAA;IACpD,QAAQ,CAAC,GAAa,EAAE,OAAY,EAAA;AAC3C,QAAA,IAAI,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAS,KAAK,CAAA,EAAG,CAAC,CAAA,EAAA,EAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG,CAAC,CAAC;;AAE/F,QAAA,OAAO,CAAI,CAAA,EAAA,GAAG,CAAC,IAAI,CAAK,EAAA,EAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG,CAAC;KAChD;AACF,CAAA;AAED;;;;;;;AAOG;AACG,SAAU,IAAI,CAAC,GAAW,EAAA;AAC9B,IAAA,WAAW,KAAK,IAAI,WAAW,EAAE,CAAC;IAClC,MAAM,IAAI,GAAG,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1C,MAAM,OAAO,GAAG,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;AACjD,IAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAE5B,IAAA,MAAM,CAAC,GAAG,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;AAC9B,IAAA,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,UAAU,CAAC;AAEnF,IAAA,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAC7C,IAAA,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC;AAE3C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;AAC3C,QAAA,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,GAAG,EAAE,EAAE;gBACV,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACvB,aAAA;AAAM,iBAAA;AACL,gBAAA,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9D,aAAA;AAED,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,YAAA,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACnB,YAAA,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACnB,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACxD,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;AACN,YAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACjB,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,IAAI,CAAC;AACV,SAAA;AACD,QAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACjB,QAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACjB,QAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACjB,QAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACjB,QAAA,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAClB,KAAA;;IAGD,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7E,CAAC;AAED;;;;AAIG;AACH,SAAS,QAAQ,CAAC,KAAa,EAAA;;AAE7B,IAAA,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,EAAE,CAAC,KAAa,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;IACxD,IAAI,KAAK,GAAG,EAAE,EAAE;AACd,QAAA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AACzC,KAAA;IAED,IAAI,KAAK,GAAG,EAAE,EAAE;QACd,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;AAChC,KAAA;IAED,IAAI,KAAK,GAAG,EAAE,EAAE;QACd,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAClD,KAAA;IAED,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;AACjC,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,WAAW,CAAC,GAAW,EAAA;AACrC,IAAA,WAAW,KAAK,IAAI,WAAW,EAAE,CAAC;IAClC,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACrC,IAAA,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAEzE,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACtC,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAE3C,IAAA,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;AACnC,QAAA,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;AACrB,QAAA,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC;AACvB,KAAA;AAED,IAAA,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAClB,CAAC;SAEe,YAAY,CAAC,GAAW,EAAE,UAAkB,EAAE,EAAA;AAC5D,IAAA,IAAI,cAAc,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAEtC,IAAA,IAAI,OAAO,EAAE;AACX,QAAA,MAAM,kBAAkB,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;AAChD,QAAA,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;AACtE,KAAA;AAED,IAAA,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAA,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IAE7B,OAAO,oBAAoB,CAAC,EAAE,GAAG,UAAU,EAAE,EAAE,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,MAAM,CAAC,IAAc,EAAE,MAAc,EAAE,CAAS,EAAA;AACvD,IAAA,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,UAAU,CAAC;IACnC,IAAI,KAAK,GAAG,CAAC,CAAC;AAEd,IAAA,MAAM,GAAG,GAAG,MAAM,GAAG,EAAE,CAAC;AACxB,IAAA,OAAO,KAAK,IAAI,GAAG,EAAE,KAAK,IAAI,EAAE,EAAE;QAChC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;QACrC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;QACrC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,QAAA,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACpC,KAAA;AAED,IAAA,MAAM,SAAS,GAAG,MAAM,GAAG,KAAK,CAAC;;IAGjC,CAAC,IAAI,MAAM,CAAC;IAEZ,IAAI,SAAS,IAAI,CAAC,EAAE;QAClB,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACjC,KAAK,IAAI,CAAC,CAAC;QAEX,IAAI,SAAS,IAAI,CAAC,EAAE;YAClB,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACjC,KAAK,IAAI,CAAC,CAAC;;YAGX,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;AAClC,aAAA;YACD,IAAI,SAAS,IAAI,EAAE,EAAE;gBACnB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AACnC,aAAA;YACD,IAAI,SAAS,KAAK,EAAE,EAAE;gBACpB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AACnC,aAAA;AACF,SAAA;AAAM,aAAA;;YAEL,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;AAC7B,aAAA;YACD,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;AAClC,aAAA;YACD,IAAI,SAAS,KAAK,CAAC,EAAE;gBACnB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AACnC,aAAA;AACF,SAAA;AACF,KAAA;AAAM,SAAA;;QAEL,IAAI,SAAS,IAAI,CAAC,EAAE;YAClB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;AAC7B,SAAA;QACD,IAAI,SAAS,IAAI,CAAC,EAAE;YAClB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;AAClC,SAAA;QACD,IAAI,SAAS,KAAK,CAAC,EAAE;YACnB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AACnC,SAAA;AACF,KAAA;IAED,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AAED;AACA,SAAS,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;IAC1C,CAAC,IAAI,CAAC,CAAC;IAAC,CAAC,IAAI,CAAC,CAAC;AAAC,IAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC,IAAI,CAAC,CAAC;IAAC,CAAC,IAAI,CAAC,CAAC;AAAC,IAAA,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,IAAI,CAAC,CAAC;IAAC,CAAC,IAAI,CAAC,CAAC;AAAC,IAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC,IAAI,CAAC,CAAC;IAAC,CAAC,IAAI,CAAC,CAAC;AAAC,IAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC,IAAI,CAAC,CAAC;IAAC,CAAC,IAAI,CAAC,CAAC;AAAC,IAAA,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAC7B,CAAC,IAAI,CAAC,CAAC;IAAC,CAAC,IAAI,CAAC,CAAC;AAAC,IAAA,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC,IAAI,CAAC,CAAC;IAAC,CAAC,IAAI,CAAC,CAAC;AAAC,IAAA,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC,IAAI,CAAC,CAAC;IAAC,CAAC,IAAI,CAAC,CAAC;AAAC,IAAA,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAC7B,CAAC,IAAI,CAAC,CAAC;IAAC,CAAC,IAAI,CAAC,CAAC;AAAC,IAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AAC9B,IAAA,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,CAAC;AACD;AAEA;AAEA,IAAK,MAGJ,CAAA;AAHD,CAAA,UAAK,MAAM,EAAA;AACT,IAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG,CAAA;AACL,CAAC,EAHI,MAAM,KAAN,MAAM,GAGV,EAAA,CAAA,CAAA,CAAA;AAED,SAAS,KAAK,CAAC,CAAS,EAAE,CAAS,EAAA;IACjC,OAAO,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,SAAS,CAAC,CAAS,EAAE,CAAS,EAAA;AACrC,IAAA,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;AACxC,IAAA,MAAM,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC;AACpD,IAAA,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC,IAAI,IAAI,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,SAAS,KAAK,CAAC,CAAmB,EAAE,CAAmB,EAAA;AACrD,IAAA,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAA,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACjC,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACpB,IAAA,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACtC,IAAA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChB,CAAC;AAED;AACA,SAAS,KAAK,CAAC,CAAS,EAAE,KAAa,EAAA;AACrC,IAAA,OAAO,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AAC7C,CAAC;AAED;AACA,SAAS,KAAK,CAAC,GAAqB,EAAE,KAAa,EAAA;AACjD,IAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAA,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AAChD,IAAA,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AAChD,IAAA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChB,CAAC;AAED,SAAS,cAAc,CAAC,KAAa,EAAE,MAAc,EAAA;IACnD,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;IACtC,MAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC7B,QAAA,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C,KAAA;AAED,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,MAAM,CAAC,KAAa,EAAE,KAAa,EAAA;AAC1C,IAAA,OAAO,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,MAAM,CAAC,KAAa,EAAE,KAAa,EAAE,MAAc,EAAA;IAC1D,IAAI,IAAI,GAAG,CAAC,CAAC;AACb,IAAA,IAAI,MAAM,KAAK,MAAM,CAAC,GAAG,EAAE;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B,YAAA,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAClD,SAAA;AACF,KAAA;AAAM,SAAA;QACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B,YAAA,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,SAAA;AACF,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;AAOG;AACH,MAAM,OAAO,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAE9C;;;;;AAKG;AACH,SAAS,oBAAoB,CAAC,EAAU,EAAE,EAAU,EAAA;;;;AAIlD,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;;;AAIvD,IAAA,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAExD,IAAA,OAAO,OAAO,CAAC,QAAQ,EAAE,CAAC;AAC5B;;ACtYA;AAwJA;;;;;AAKG;AACa,SAAA,YAAY,CACxB,YAAkC,EAAE,WAA4B,EAAE,QAAyB,EAC3F,oBAAmD,EACnD,mBAAA,GAAoD,EAAE,EAAA;IACxD,MAAM,aAAa,GAAqC,EAAE,CAAC;IAC3D,MAAM,qBAAqB,GAA0D,EAAE,CAAC;IACxF,MAAM,oBAAoB,GAA2C,EAAE,CAAC;AACxE,IAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,IAAA,MAAM,mBAAmB,GAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACtD,MAAM,gBAAgB,GAAa,EAAE,CAAC;AACtC,IAAA,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AAClC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC5C,MAAM,EAAC,WAAW,EAAE,eAAe,GAAG,sBAAsB,CAAC,CAAC,CAAC,EAAE,mBAAmB,EAAC,GACjF,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,QAAA,aAAa,IAAI,CAAK,EAAA,EAAA,eAAe,CAAI,CAAA,EAAA,WAAW,EAAE,CAAC;QACvD,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,aAAa,CAAC,eAAe,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACpD,qBAAqB,CAAC,eAAe,CAAC,GAAG,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACrE,SAAA;AACD,QAAA,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvC,IAAI,mBAAmB,KAAK,SAAS,EAAE;AACrC,YAAA,oBAAoB,CAAC,eAAe,CAAC,GAAG,mBAAmB,CAAC;AAC7D,SAAA;AACD,QAAA,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACvC,KAAA;AACD,IAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,QAAQ,IAAI,YAAY,CAAC,aAAa,EAAE,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;IAC3F,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,KAAK,SAAS,CAAC,GAAG,EAAE,CAAC;IAC9F,OAAO;AACL,QAAA,EAAE,EAAE,SAAS;QACb,SAAS;QACT,aAAa;QACb,qBAAqB;AACrB,QAAA,IAAI,EAAE,aAAa;QACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AAC3B,QAAA,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,EAAE;AAC/B,QAAA,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;AACvC,QAAA,YAAY,EAAE,mBAAmB;QACjC,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,QAAQ;KACT,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACa,SAAA,aAAa,CAAC,MAAc,EAAE,GAAW,EAAA;AACvD,IAAA,MAAM,EAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7D,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,OAAO,EAAC,IAAI,EAAE,aAAa,EAAC,CAAC;AAC9B,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,CAAC,gBAAgB,EAAE,GAAG,SAAS,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;AAC1E,QAAA,MAAM,CAAC,cAAc,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,GAAyB,cAAc,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QAC9F,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,WAAW,GAAG,OAAO,CAAC;YACtB,OAAO,GAAG,SAAS,CAAC;AACrB,SAAA;QACD,IAAI,WAAW,KAAK,EAAE,EAAE;YACtB,WAAW,GAAG,SAAS,CAAC;AACzB,SAAA;AACD,QAAA,OAAO,EAAC,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAC,CAAC;AACzE,KAAA;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;AAmBG;AACa,SAAA,gBAAgB,CAAC,MAAc,EAAE,GAAW,EAAA;AAE1D,IAAA,MAAM,EAAC,IAAI,EAAE,WAAW,EAAE,KAAK,EAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC3D,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,OAAO,EAAC,WAAW,EAAC,CAAC;AACtB,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,CAAC,eAAe,EAAE,mBAAmB,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACzE,QAAA,OAAO,EAAC,WAAW,EAAE,eAAe,EAAE,mBAAmB,EAAC,CAAC;AAC5D,KAAA;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;AAmBG;AACa,SAAA,UAAU,CAAC,MAAc,EAAE,GAAW,EAAA;IACpD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAKA,cAAY,EAAE;AAClC,QAAA,OAAO,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;AACvB,KAAA;AAAM,SAAA;QACL,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC/C,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC;YACtC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;SACvC,CAAC;AACH,KAAA;AACH,CAAC;AAGD,SAAS,sBAAsB,CAAC,KAAa,EAAA;AAC3C,IAAA,OAAO,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,CAAM,GAAA,EAAA,KAAK,GAAG,CAAC,EAAE,CAAC;AAChD,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,cAAc,CAAC,MAAc,EAAE,GAAW,EAAA;IACxD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE,QAAQ,EAAE,EAAE;AAC9F,QAAA,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;AAC1B,YAAA,QAAQ,EAAE,CAAC;AACZ,SAAA;AAAM,aAAA,IAAI,MAAM,CAAC,WAAW,CAAC,KAAKA,cAAY,EAAE;AAC/C,YAAA,OAAO,WAAW,CAAC;AACpB,SAAA;AACF,KAAA;AACD,IAAA,MAAM,IAAI,KAAK,CAAC,6CAA6C,GAAG,CAAA,EAAA,CAAI,CAAC,CAAC;AACxE;;AC5TM,MAAO,uBAAwB,SAAQ,KAAK,CAAA;AAEhD,IAAA,WAAA,CAAqB,aAA4B,EAAA;QAC/C,KAAK,CAAC,4BAA4B,eAAe,CAAC,aAAa,CAAC,CAAA,CAAA,CAAG,CAAC,CAAC;QADlD,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QADhC,IAAI,CAAA,IAAA,GAAG,yBAAyB,CAAC;KAGjD;AACF,CAAA;AAEK,SAAU,yBAAyB,CAAC,CAAM,EAAA;AAC9C,IAAA,OAAO,CAAC,CAAC,IAAI,KAAK,yBAAyB,CAAC;AAC9C,CAAC;AAED;;;;;;;;;;;;;;;AAeG;SACaC,WAAS,CACrB,YAA+C,EAAE,YAAkC,EACnF,aAA6B,EAAA;IAC/B,MAAM,OAAO,GAAG,YAAY,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;;IAE1D,IAAI,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;;AAE3C,IAAA,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;AACnC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC,EAAE,EAAE;YAC9E,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,SAAA;AACF,KAAA;IACD,IAAI,WAAW,KAAK,SAAS,EAAE;AAC7B,QAAA,MAAM,IAAI,uBAAuB,CAAC,OAAO,CAAC,CAAC;AAC5C,KAAA;IACD,OAAO;QACL,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,IAAG;YACvE,IAAI,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;AACrD,gBAAA,OAAO,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;AAC3C,aAAA;AAAM,iBAAA;gBACL,MAAM,IAAI,KAAK,CACX,CAAA,mFAAA,EACI,eAAe,CAAC,OAAO,CAAC,CAAK,GAAA,CAAA;oBACjC,CACI,iDAAA,EAAA,WAAW,CAAwC,sCAAA,CAAA,CAAC,CAAC;AAC9D,aAAA;AACH,SAAC,CAAC;KACH,CAAC;AACJ,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,gBAAgB,CAAC,aAA4B,EAAA;IAC3D,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACjD,MAAM,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,MAAM,gBAAgB,GAAa,EAAE,CAAC;AACtC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QAC5C,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,QAAA,YAAY,CAAC,IAAI,CAAC,CAAA,EAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAE,CAAA,CAAC,CAAC;AACtC,KAAA;AACD,IAAA,MAAM,eAAe,GACjB,YAAY,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAKD,cAAY,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACnF,OAAO;AACL,QAAA,IAAI,EAAE,aAAa;AACnB,QAAA,YAAY,EAAE,kBAAkB,CAAC,YAAY,EAAE,eAAe,CAAC;QAC/D,gBAAgB;KACjB,CAAC;AACJ,CAAC;AAED;;;;;AAKG;SACa,qBAAqB,CACjC,YAAsB,EAAE,mBAA6B,EAAE,EAAA;AACzD,IAAA,IAAI,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACpC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAChD,QAAA,aAAa,IAAI,CAAA,EAAA,EAAK,gBAAgB,CAAC,CAAC,CAAC,CAAA,CAAA,EAAI,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;AACpE,KAAA;IACD,OAAO;AACL,QAAA,IAAI,EAAE,aAAa;AACnB,QAAA,YAAY,EAAE,kBAAkB,CAAC,YAAY,EAAE,YAAY,CAAC;QAC5D,gBAAgB;KACjB,CAAC;AACJ,CAAC;AAED;;;;;AAKG;AACa,SAAA,kBAAkB,CAAC,MAAgB,EAAE,GAAa,EAAA;AAChE,IAAA,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAC,KAAK,EAAE,GAAG,EAAC,CAAC,CAAC;AACnD,IAAA,OAAO,MAAa,CAAC;AACvB,CAAC;AAGD,SAAS,eAAe,CAAC,OAAsB,EAAA;IAC7C,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,IAAI,CAAA,IAAA,EAAO,OAAO,CAAC,OAAO,CAAA,CAAA,CAAG,CAAC;AACnE,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;QAC5D,CAAK,EAAA,EAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAA,CAAA,CAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAG,CAAA,CAAA;AACvD,QAAA,EAAE,CAAC;AACP,IAAA,OAAO,CAAI,CAAA,EAAA,OAAO,CAAC,EAAE,CAAI,CAAA,EAAA,MAAM,CAAM,GAAA,EAAA,OAAO,CAAC,IAAI,CAAI,CAAA,EAAA,aAAa,GAAG,CAAC;AACxE;;AC7HA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCG;AACG,SAAU,gBAAgB,CAAC,YAA8C,EAAA;;AAE7E,IAAA,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AACxB,QAAA,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC;AACjC,KAAA;AACD,IAAA,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;AAC3B,QAAA,SAAS,CAAC,YAAY,GAAG,EAAE,CAAC;AAC7B,KAAA;IACD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,GAAG,IAAG;AACtC,QAAA,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;AACpE,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;AASG;SACa,iBAAiB,GAAA;AAC/B,IAAA,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC;AAChC,IAAA,SAAS,CAAC,YAAY,GAAG,EAAE,CAAC;AAC9B,CAAC;AAED;;;;AAIG;AACa,SAAA,SAAS,CAAC,YAAkC,EAAE,aAA6B,EAAA;IAEzF,IAAI;QACF,OAAOE,WAAU,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;AACxE,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,CAAC,IAAI,CAAE,CAAW,CAAC,OAAO,CAAC,CAAC;AACnC,QAAA,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;AACtC,KAAA;AACH;;ACjDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8FG;MACUC,WAAS,GAAe,UACjC,YAAkC,EAAE,GAAG,WAA2B,EAAA;IACpE,IAAIA,WAAS,CAAC,SAAS,EAAE;;QAEvB,MAAM,WAAW,GAAGA,WAAS,CAAC,SAAS,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AACnE,QAAA,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAC9B,QAAA,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAC9B,KAAA;AACD,IAAA,IAAI,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC5C,OAAO,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAClF,KAAA;AACD,IAAA,OAAO,OAAO,CAAC;AACjB,EAAE;AAEF,MAAM,YAAY,GAAG,GAAG,CAAC;AAEzB;;;;;;;;;;;;AAYG;AACH,SAAS,UAAU,CAAC,WAAmB,EAAE,cAAsB,EAAA;IAC7D,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,YAAY;AAC5C,QAAA,WAAW,CAAC,SAAS,CAAC,cAAc,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;AACtE,QAAA,WAAW,CAAC;AAClB;;AC1KA;;ACAA;;ACAA;;;;"}