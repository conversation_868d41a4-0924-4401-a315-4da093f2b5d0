{"version": 3, "names": ["helpers", "data", "require", "_traverse", "_codeFrame", "_t", "_helperModuleTransforms", "_semver", "cloneNode", "interpreterDirective", "errorVisitor", "enter", "path", "state", "loc", "node", "stop", "File", "constructor", "options", "code", "ast", "inputMap", "_map", "Map", "opts", "declarations", "scope", "metadata", "hub", "file", "getCode", "getScope", "addHelper", "bind", "buildError", "buildCodeFrameError", "NodePath", "get", "parentPath", "parent", "container", "key", "setContext", "shebang", "interpreter", "value", "replaceWith", "remove", "set", "val", "Error", "has", "getModuleName", "addImport", "availableHelper", "name", "versionRange", "minVersion", "err", "semver", "valid", "intersects", "declar", "generator", "res", "ensure", "uid", "generateUidIdentifier", "dependencies", "dep", "getDependencies", "nodes", "globals", "Object", "keys", "getAllBindings", "for<PERSON>ach", "hasBinding", "rename", "_compact", "unshiftContainer", "indexOf", "isVariableDeclaration", "registerDeclaration", "addTemplateObject", "msg", "_Error", "SyntaxError", "_loc", "traverse", "txt", "highlightCode", "codeFrameColumns", "start", "line", "column", "end", "undefined", "exports", "default"], "sources": ["../../../src/transformation/file/file.ts"], "sourcesContent": ["import * as helpers from \"@babel/helpers\";\nimport { NodePath } from \"@babel/traverse\";\nimport type { HubInterface, Visitor, Scope } from \"@babel/traverse\";\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport traverse from \"@babel/traverse\";\nimport { cloneNode, interpreterDirective } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { getModuleName } from \"@babel/helper-module-transforms\";\nimport semver from \"semver\";\n\nimport type { NormalizedFile } from \"../normalize-file.ts\";\n\nconst errorVisitor: Visitor<{ loc: NodeLocation[\"loc\"] | null }> = {\n  enter(path, state) {\n    const loc = path.node.loc;\n    if (loc) {\n      state.loc = loc;\n      path.stop();\n    }\n  },\n};\n\nexport type NodeLocation = {\n  loc?: {\n    end?: {\n      line: number;\n      column: number;\n    };\n    start: {\n      line: number;\n      column: number;\n    };\n  };\n  _loc?: {\n    end?: {\n      line: number;\n      column: number;\n    };\n    start: {\n      line: number;\n      column: number;\n    };\n  };\n};\n\nexport default class File {\n  _map: Map<unknown, unknown> = new Map();\n  opts: { [key: string]: any };\n  declarations: { [key: string]: t.Identifier } = {};\n  path: NodePath<t.Program>;\n  ast: t.File;\n  scope: Scope;\n  metadata: { [key: string]: any } = {};\n  code: string = \"\";\n  inputMap: any;\n\n  hub: HubInterface & { file: File } = {\n    // keep it for the usage in babel-core, ex: path.hub.file.opts.filename\n    file: this,\n    getCode: () => this.code,\n    getScope: () => this.scope,\n    addHelper: this.addHelper.bind(this),\n    buildError: this.buildCodeFrameError.bind(this),\n  };\n\n  constructor(options: {}, { code, ast, inputMap }: NormalizedFile) {\n    this.opts = options;\n    this.code = code;\n    this.ast = ast;\n    this.inputMap = inputMap;\n\n    this.path = NodePath.get({\n      hub: this.hub,\n      parentPath: null,\n      parent: this.ast,\n      container: this.ast,\n      key: \"program\",\n    }).setContext() as NodePath<t.Program>;\n    this.scope = this.path.scope;\n  }\n\n  /**\n   * Provide backward-compatible access to the interpreter directive handling\n   * in Babel 6.x. If you are writing a plugin for Babel 7.x, it would be\n   * best to use 'program.interpreter' directly.\n   */\n  get shebang(): string {\n    const { interpreter } = this.path.node;\n    return interpreter ? interpreter.value : \"\";\n  }\n  set shebang(value: string) {\n    if (value) {\n      this.path.get(\"interpreter\").replaceWith(interpreterDirective(value));\n    } else {\n      this.path.get(\"interpreter\").remove();\n    }\n  }\n\n  set(key: unknown, val: unknown) {\n    if (key === \"helpersNamespace\") {\n      throw new Error(\n        \"Babel 7.0.0-beta.56 has dropped support for the 'helpersNamespace' utility.\" +\n          \"If you are using @babel/plugin-external-helpers you will need to use a newer \" +\n          \"version than the one you currently have installed. \" +\n          \"If you have your own implementation, you'll want to explore using 'helperGenerator' \" +\n          \"alongside 'file.availableHelper()'.\",\n      );\n    }\n\n    this._map.set(key, val);\n  }\n\n  get(key: unknown): any {\n    return this._map.get(key);\n  }\n\n  has(key: unknown): boolean {\n    return this._map.has(key);\n  }\n\n  getModuleName(): string | undefined | null {\n    return getModuleName(this.opts, this.opts);\n  }\n\n  addImport() {\n    throw new Error(\n      \"This API has been removed. If you're looking for this \" +\n        \"functionality in Babel 7, you should import the \" +\n        \"'@babel/helper-module-imports' module and use the functions exposed \" +\n        \" from that module, such as 'addNamed' or 'addDefault'.\",\n    );\n  }\n\n  /**\n   * Check if a given helper is available in @babel/core's helper list.\n   *\n   * This _also_ allows you to pass a Babel version specifically. If the\n   * helper exists, but was not available for the full given range, it will be\n   * considered unavailable.\n   */\n  availableHelper(name: string, versionRange?: string | null): boolean {\n    let minVersion;\n    try {\n      minVersion = helpers.minVersion(name);\n    } catch (err) {\n      if (err.code !== \"BABEL_HELPER_UNKNOWN\") throw err;\n\n      return false;\n    }\n\n    if (typeof versionRange !== \"string\") return true;\n\n    // semver.intersects() has some surprising behavior with comparing ranges\n    // with pre-release versions. We add '^' to ensure that we are always\n    // comparing ranges with ranges, which sidesteps this logic.\n    // For example:\n    //\n    //   semver.intersects(`<7.0.1`, \"7.0.0-beta.0\") // false - surprising\n    //   semver.intersects(`<7.0.1`, \"^7.0.0-beta.0\") // true - expected\n    //\n    // This is because the first falls back to\n    //\n    //   semver.satisfies(\"7.0.0-beta.0\", `<7.0.1`) // false - surprising\n    //\n    // and this fails because a prerelease version can only satisfy a range\n    // if it is a prerelease within the same major/minor/patch range.\n    //\n    // Note: If this is found to have issues, please also revisit the logic in\n    // transform-runtime's definitions.js file.\n    if (semver.valid(versionRange)) versionRange = `^${versionRange}`;\n\n    return (\n      !semver.intersects(`<${minVersion}`, versionRange) &&\n      !semver.intersects(`>=8.0.0`, versionRange)\n    );\n  }\n\n  addHelper(name: string): t.Identifier {\n    const declar = this.declarations[name];\n    if (declar) return cloneNode(declar);\n\n    const generator = this.get(\"helperGenerator\");\n    if (generator) {\n      const res = generator(name);\n      if (res) return res;\n    }\n\n    // make sure that the helper exists\n    helpers.ensure(name, File);\n\n    const uid = (this.declarations[name] =\n      this.scope.generateUidIdentifier(name));\n\n    const dependencies: { [key: string]: t.Identifier } = {};\n    for (const dep of helpers.getDependencies(name)) {\n      dependencies[dep] = this.addHelper(dep);\n    }\n\n    const { nodes, globals } = helpers.get(\n      name,\n      dep => dependencies[dep],\n      uid,\n      Object.keys(this.scope.getAllBindings()),\n    );\n\n    globals.forEach(name => {\n      if (this.path.scope.hasBinding(name, true /* noGlobals */)) {\n        this.path.scope.rename(name);\n      }\n    });\n\n    nodes.forEach(node => {\n      // @ts-expect-error Fixme: document _compact node property\n      node._compact = true;\n    });\n\n    this.path.unshiftContainer(\"body\", nodes);\n    // TODO: NodePath#unshiftContainer should automatically register new\n    // bindings.\n    this.path.get(\"body\").forEach(path => {\n      if (nodes.indexOf(path.node) === -1) return;\n      if (path.isVariableDeclaration()) this.scope.registerDeclaration(path);\n    });\n\n    return uid;\n  }\n\n  addTemplateObject() {\n    throw new Error(\n      \"This function has been moved into the template literal transform itself.\",\n    );\n  }\n\n  buildCodeFrameError(\n    node: NodeLocation | undefined | null,\n    msg: string,\n    _Error: typeof Error = SyntaxError,\n  ): Error {\n    let loc = node && (node.loc || node._loc);\n\n    if (!loc && node) {\n      const state: { loc?: NodeLocation[\"loc\"] | null } = {\n        loc: null,\n      };\n      traverse(node as t.Node, errorVisitor, this.scope, state);\n      loc = state.loc;\n\n      let txt =\n        \"This is an error on an internal node. Probably an internal error.\";\n      if (loc) txt += \" Location has been estimated.\";\n\n      msg += ` (${txt})`;\n    }\n\n    if (loc) {\n      const { highlightCode = true } = this.opts;\n\n      msg +=\n        \"\\n\" +\n        codeFrameColumns(\n          this.code,\n          {\n            start: {\n              line: loc.start.line,\n              column: loc.start.column + 1,\n            },\n            end:\n              loc.end && loc.start.line === loc.end.line\n                ? {\n                    line: loc.end.line,\n                    column: loc.end.column + 1,\n                  }\n                : undefined,\n          },\n          { highlightCode },\n        );\n    }\n\n    return new _Error(msg);\n  }\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,WAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,GAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,EAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,wBAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,uBAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,QAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B;EAHnBO,SAAS;EAAEC;AAAoB,IAAAJ,EAAA;AAOxC,MAAMK,YAA0D,GAAG;EACjEC,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACjB,MAAMC,GAAG,GAAGF,IAAI,CAACG,IAAI,CAACD,GAAG;IACzB,IAAIA,GAAG,EAAE;MACPD,KAAK,CAACC,GAAG,GAAGA,GAAG;MACfF,IAAI,CAACI,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAyBc,MAAMC,IAAI,CAAC;EAoBxBC,WAAWA,CAACC,OAAW,EAAE;IAAEC,IAAI;IAAEC,GAAG;IAAEC;EAAyB,CAAC,EAAE;IAAA,KAnBlEC,IAAI,GAA0B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACvCC,IAAI;IAAA,KACJC,YAAY,GAAoC,CAAC,CAAC;IAAA,KAClDd,IAAI;IAAA,KACJS,GAAG;IAAA,KACHM,KAAK;IAAA,KACLC,QAAQ,GAA2B,CAAC,CAAC;IAAA,KACrCR,IAAI,GAAW,EAAE;IAAA,KACjBE,QAAQ;IAAA,KAERO,GAAG,GAAkC;MAEnCC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACX,IAAI;MACxBY,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACL,KAAK;MAC1BM,SAAS,EAAE,IAAI,CAACA,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;MACpCC,UAAU,EAAE,IAAI,CAACC,mBAAmB,CAACF,IAAI,CAAC,IAAI;IAChD,CAAC;IAGC,IAAI,CAACT,IAAI,GAAGN,OAAO;IACnB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IAExB,IAAI,CAACV,IAAI,GAAGyB,oBAAQ,CAACC,GAAG,CAAC;MACvBT,GAAG,EAAE,IAAI,CAACA,GAAG;MACbU,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI,CAACnB,GAAG;MAChBoB,SAAS,EAAE,IAAI,CAACpB,GAAG;MACnBqB,GAAG,EAAE;IACP,CAAC,CAAC,CAACC,UAAU,CAAC,CAAwB;IACtC,IAAI,CAAChB,KAAK,GAAG,IAAI,CAACf,IAAI,CAACe,KAAK;EAC9B;EAOA,IAAIiB,OAAOA,CAAA,EAAW;IACpB,MAAM;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACjC,IAAI,CAACG,IAAI;IACtC,OAAO8B,WAAW,GAAGA,WAAW,CAACC,KAAK,GAAG,EAAE;EAC7C;EACA,IAAIF,OAAOA,CAACE,KAAa,EAAE;IACzB,IAAIA,KAAK,EAAE;MACT,IAAI,CAAClC,IAAI,CAAC0B,GAAG,CAAC,aAAa,CAAC,CAACS,WAAW,CAACtC,oBAAoB,CAACqC,KAAK,CAAC,CAAC;IACvE,CAAC,MAAM;MACL,IAAI,CAAClC,IAAI,CAAC0B,GAAG,CAAC,aAAa,CAAC,CAACU,MAAM,CAAC,CAAC;IACvC;EACF;EAEAC,GAAGA,CAACP,GAAY,EAAEQ,GAAY,EAAE;IAC9B,IAAIR,GAAG,KAAK,kBAAkB,EAAE;MAC9B,MAAM,IAAIS,KAAK,CACb,6EAA6E,GAC3E,+EAA+E,GAC/E,qDAAqD,GACrD,sFAAsF,GACtF,qCACJ,CAAC;IACH;IAEA,IAAI,CAAC5B,IAAI,CAAC0B,GAAG,CAACP,GAAG,EAAEQ,GAAG,CAAC;EACzB;EAEAZ,GAAGA,CAACI,GAAY,EAAO;IACrB,OAAO,IAAI,CAACnB,IAAI,CAACe,GAAG,CAACI,GAAG,CAAC;EAC3B;EAEAU,GAAGA,CAACV,GAAY,EAAW;IACzB,OAAO,IAAI,CAACnB,IAAI,CAAC6B,GAAG,CAACV,GAAG,CAAC;EAC3B;EAEAW,aAAaA,CAAA,EAA8B;IACzC,OAAO,IAAAA,uCAAa,EAAC,IAAI,CAAC5B,IAAI,EAAE,IAAI,CAACA,IAAI,CAAC;EAC5C;EAEA6B,SAASA,CAAA,EAAG;IACV,MAAM,IAAIH,KAAK,CACb,wDAAwD,GACtD,kDAAkD,GAClD,sEAAsE,GACtE,wDACJ,CAAC;EACH;EASAI,eAAeA,CAACC,IAAY,EAAEC,YAA4B,EAAW;IACnE,IAAIC,UAAU;IACd,IAAI;MACFA,UAAU,GAAG1D,OAAO,CAAD,CAAC,CAAC0D,UAAU,CAACF,IAAI,CAAC;IACvC,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZ,IAAIA,GAAG,CAACvC,IAAI,KAAK,sBAAsB,EAAE,MAAMuC,GAAG;MAElD,OAAO,KAAK;IACd;IAEA,IAAI,OAAOF,YAAY,KAAK,QAAQ,EAAE,OAAO,IAAI;IAmBjD,IAAIG,QAAKA,CAAC,CAACC,KAAK,CAACJ,YAAY,CAAC,EAAEA,YAAY,GAAI,IAAGA,YAAa,EAAC;IAEjE,OACE,CAACG,QAAKA,CAAC,CAACE,UAAU,CAAE,IAAGJ,UAAW,EAAC,EAAED,YAAY,CAAC,IAClD,CAACG,QAAKA,CAAC,CAACE,UAAU,CAAE,SAAQ,EAAEL,YAAY,CAAC;EAE/C;EAEAxB,SAASA,CAACuB,IAAY,EAAgB;IACpC,MAAMO,MAAM,GAAG,IAAI,CAACrC,YAAY,CAAC8B,IAAI,CAAC;IACtC,IAAIO,MAAM,EAAE,OAAOvD,SAAS,CAACuD,MAAM,CAAC;IAEpC,MAAMC,SAAS,GAAG,IAAI,CAAC1B,GAAG,CAAC,iBAAiB,CAAC;IAC7C,IAAI0B,SAAS,EAAE;MACb,MAAMC,GAAG,GAAGD,SAAS,CAACR,IAAI,CAAC;MAC3B,IAAIS,GAAG,EAAE,OAAOA,GAAG;IACrB;IAGAjE,OAAO,CAAD,CAAC,CAACkE,MAAM,CAACV,IAAI,EAAEvC,IAAI,CAAC;IAE1B,MAAMkD,GAAG,GAAI,IAAI,CAACzC,YAAY,CAAC8B,IAAI,CAAC,GAClC,IAAI,CAAC7B,KAAK,CAACyC,qBAAqB,CAACZ,IAAI,CAAE;IAEzC,MAAMa,YAA6C,GAAG,CAAC,CAAC;IACxD,KAAK,MAAMC,GAAG,IAAItE,OAAO,CAAD,CAAC,CAACuE,eAAe,CAACf,IAAI,CAAC,EAAE;MAC/Ca,YAAY,CAACC,GAAG,CAAC,GAAG,IAAI,CAACrC,SAAS,CAACqC,GAAG,CAAC;IACzC;IAEA,MAAM;MAAEE,KAAK;MAAEC;IAAQ,CAAC,GAAGzE,OAAO,CAAD,CAAC,CAACsC,GAAG,CACpCkB,IAAI,EACJc,GAAG,IAAID,YAAY,CAACC,GAAG,CAAC,EACxBH,GAAG,EACHO,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChD,KAAK,CAACiD,cAAc,CAAC,CAAC,CACzC,CAAC;IAEDH,OAAO,CAACI,OAAO,CAACrB,IAAI,IAAI;MACtB,IAAI,IAAI,CAAC5C,IAAI,CAACe,KAAK,CAACmD,UAAU,CAACtB,IAAI,EAAE,IAAoB,CAAC,EAAE;QAC1D,IAAI,CAAC5C,IAAI,CAACe,KAAK,CAACoD,MAAM,CAACvB,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC;IAEFgB,KAAK,CAACK,OAAO,CAAC9D,IAAI,IAAI;MAEpBA,IAAI,CAACiE,QAAQ,GAAG,IAAI;IACtB,CAAC,CAAC;IAEF,IAAI,CAACpE,IAAI,CAACqE,gBAAgB,CAAC,MAAM,EAAET,KAAK,CAAC;IAGzC,IAAI,CAAC5D,IAAI,CAAC0B,GAAG,CAAC,MAAM,CAAC,CAACuC,OAAO,CAACjE,IAAI,IAAI;MACpC,IAAI4D,KAAK,CAACU,OAAO,CAACtE,IAAI,CAACG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MACrC,IAAIH,IAAI,CAACuE,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACxD,KAAK,CAACyD,mBAAmB,CAACxE,IAAI,CAAC;IACxE,CAAC,CAAC;IAEF,OAAOuD,GAAG;EACZ;EAEAkB,iBAAiBA,CAAA,EAAG;IAClB,MAAM,IAAIlC,KAAK,CACb,0EACF,CAAC;EACH;EAEAf,mBAAmBA,CACjBrB,IAAqC,EACrCuE,GAAW,EACXC,MAAoB,GAAGC,WAAW,EAC3B;IACP,IAAI1E,GAAG,GAAGC,IAAI,KAAKA,IAAI,CAACD,GAAG,IAAIC,IAAI,CAAC0E,IAAI,CAAC;IAEzC,IAAI,CAAC3E,GAAG,IAAIC,IAAI,EAAE;MAChB,MAAMF,KAA2C,GAAG;QAClDC,GAAG,EAAE;MACP,CAAC;MACD,IAAA4E,mBAAQ,EAAC3E,IAAI,EAAYL,YAAY,EAAE,IAAI,CAACiB,KAAK,EAAEd,KAAK,CAAC;MACzDC,GAAG,GAAGD,KAAK,CAACC,GAAG;MAEf,IAAI6E,GAAG,GACL,mEAAmE;MACrE,IAAI7E,GAAG,EAAE6E,GAAG,IAAI,+BAA+B;MAE/CL,GAAG,IAAK,KAAIK,GAAI,GAAE;IACpB;IAEA,IAAI7E,GAAG,EAAE;MACP,MAAM;QAAE8E,aAAa,GAAG;MAAK,CAAC,GAAG,IAAI,CAACnE,IAAI;MAE1C6D,GAAG,IACD,IAAI,GACJ,IAAAO,6BAAgB,EACd,IAAI,CAACzE,IAAI,EACT;QACE0E,KAAK,EAAE;UACLC,IAAI,EAAEjF,GAAG,CAACgF,KAAK,CAACC,IAAI;UACpBC,MAAM,EAAElF,GAAG,CAACgF,KAAK,CAACE,MAAM,GAAG;QAC7B,CAAC;QACDC,GAAG,EACDnF,GAAG,CAACmF,GAAG,IAAInF,GAAG,CAACgF,KAAK,CAACC,IAAI,KAAKjF,GAAG,CAACmF,GAAG,CAACF,IAAI,GACtC;UACEA,IAAI,EAAEjF,GAAG,CAACmF,GAAG,CAACF,IAAI;UAClBC,MAAM,EAAElF,GAAG,CAACmF,GAAG,CAACD,MAAM,GAAG;QAC3B,CAAC,GACDE;MACR,CAAC,EACD;QAAEN;MAAc,CAClB,CAAC;IACL;IAEA,OAAO,IAAIL,MAAM,CAACD,GAAG,CAAC;EACxB;AACF;AAACa,OAAA,CAAAC,OAAA,GAAAnF,IAAA;AAAA"}