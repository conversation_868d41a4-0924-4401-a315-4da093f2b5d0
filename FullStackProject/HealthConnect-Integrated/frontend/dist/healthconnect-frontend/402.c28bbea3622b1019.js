"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[402],{5402:(oe,k,m)=>{m.r(k),m.d(k,{AppointmentsModule:()=>ne});var p=m(177),s=m(4341),d=m(2434),C=m(4978),c=m(7337),t=m(540),_=m(9545),v=m(8010),F=m(3285);function y(n,r){1&n&&(t.j41(0,"button",23),t.nrm(1,"i",24),t.EFF(2," Book Appointment "),t.k0s())}function D(n,r){if(1&n&&(t.j41(0,"option",25),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e.value),t.R7$(1),t.SpI(" ",e.label," ")}}function E(n,r){if(1&n&&(t.j41(0,"option",25),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e.value),t.R7$(1),t.SpI(" ",e.label," ")}}function I(n,r){if(1&n&&(t.j41(0,"div",26),t.nrm(1,"i",27),t.EFF(2),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function x(n,r){1&n&&(t.j41(0,"div",28)(1,"div",29)(2,"span",30),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",31),t.EFF(5,"Loading appointments..."),t.k0s()())}function T(n,r){1&n&&t.nrm(0,"i",53)}function O(n,r){1&n&&t.nrm(0,"i",54)}function M(n,r){if(1&n&&(t.j41(0,"p",55),t.nrm(1,"i",56),t.EFF(2),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(2),t.SpI(" ",e.reasonForVisit," ")}}function P(n,r){if(1&n&&(t.j41(0,"div",55)(1,"a",57),t.nrm(2,"i",58),t.EFF(3," Join Video Call "),t.k0s()()),2&n){const e=t.XpG().$implicit;t.R7$(1),t.Y8G("href",e.meetingLink,t.B4B)}}const S=function(n,r,e){return{appointmentId:n,doctorId:r,patientId:e,chatType:"PRE_APPOINTMENT",buttonText:"Chat",buttonClass:"btn-info",size:"sm",showIcon:!1}};function A(n,r){if(1&n&&t.nrm(0,"app-chat-access",59),2&n){const e=t.XpG().$implicit;t.Y8G("config",t.sMw(1,S,e.id,e.doctor.id,e.patient.id))}}const j=function(n,r,e){return{appointmentId:n,doctorId:r,patientId:e,chatType:"POST_APPOINTMENT",buttonText:"Follow-up",buttonClass:"btn-success",size:"sm",showIcon:!1}};function $(n,r){if(1&n&&t.nrm(0,"app-chat-access",59),2&n){const e=t.XpG().$implicit;t.Y8G("config",t.sMw(1,j,e.id,e.doctor.id,e.patient.id))}}function R(n,r){if(1&n){const e=t.RV6();t.j41(0,"button",60),t.bIt("click",function(){t.eBV(e);const i=t.XpG().$implicit,a=t.XpG(2);return t.Njj(a.cancelAppointment(i))}),t.nrm(1,"i",61),t.EFF(2," Cancel "),t.k0s()}}function G(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",34)(1,"div",35)(2,"div",8)(3,"div",36)(4,"div")(5,"h6",37),t.EFF(6),t.k0s(),t.j41(7,"small",38),t.EFF(8),t.k0s()(),t.j41(9,"span",39),t.EFF(10),t.k0s()(),t.j41(11,"div",40)(12,"p",41),t.nrm(13,"i",42),t.EFF(14),t.k0s(),t.j41(15,"p",41),t.nrm(16,"i",43),t.EFF(17),t.k0s(),t.j41(18,"p",41),t.DNE(19,T,1,0,"i",44),t.DNE(20,O,1,0,"i",45),t.EFF(21),t.k0s(),t.DNE(22,M,3,1,"p",46),t.k0s(),t.DNE(23,P,4,1,"div",46),t.j41(24,"div",47)(25,"div",48)(26,"button",49),t.bIt("click",function(){const a=t.eBV(e).$implicit,l=t.XpG(2);return t.Njj(l.viewAppointment(a))}),t.nrm(27,"i",50),t.EFF(28," View "),t.k0s(),t.DNE(29,A,1,5,"app-chat-access",51),t.DNE(30,$,1,5,"app-chat-access",51),t.k0s(),t.j41(31,"div"),t.DNE(32,R,3,0,"button",52),t.k0s()()()()()}if(2&n){const e=r.$implicit,o=t.XpG(2);t.R7$(6),t.SpI(" ",o.getOtherParty(e)," "),t.R7$(2),t.JRh(o.getOtherPartyRole(e)),t.R7$(1),t.Y8G("ngClass",o.getStatusBadgeClass(e.status)),t.R7$(1),t.SpI(" ",o.getStatusDisplayName(e.status)," "),t.R7$(4),t.SpI(" ",o.formatDate(e.date)," "),t.R7$(3),t.Lme(" ",o.formatTime(e.startTime)," - ",o.formatTime(e.endTime)," "),t.R7$(2),t.Y8G("ngIf","VIDEO_CALL"===e.type),t.R7$(1),t.Y8G("ngIf","IN_PERSON"===e.type),t.R7$(1),t.SpI(" ",o.getTypeDisplayName(e.type)," "),t.R7$(1),t.Y8G("ngIf",e.reasonForVisit),t.R7$(1),t.Y8G("ngIf",e.meetingLink&&"VIDEO_CALL"===e.type),t.R7$(6),t.Y8G("ngIf",o.isBeforeAppointment(e)),t.R7$(1),t.Y8G("ngIf",o.isAfterAppointment(e)),t.R7$(2),t.Y8G("ngIf",o.canCancelAppointment(e))}}function N(n,r){if(1&n&&(t.j41(0,"div",32)(1,"div",1),t.DNE(2,G,33,15,"div",33),t.k0s()()),2&n){const e=t.XpG();t.R7$(2),t.Y8G("ngForOf",e.filteredAppointments)}}function Y(n,r){1&n&&(t.j41(0,"span"),t.EFF(1,"You haven't booked any appointments yet."),t.k0s())}function w(n,r){1&n&&(t.j41(0,"span"),t.EFF(1,"You don't have any appointments scheduled."),t.k0s())}function L(n,r){1&n&&(t.j41(0,"button",23),t.nrm(1,"i",24),t.EFF(2," Book Your First Appointment "),t.k0s())}function V(n,r){if(1&n&&(t.j41(0,"div",62),t.nrm(1,"i",63),t.j41(2,"h5",38),t.EFF(3,"No appointments found"),t.k0s(),t.j41(4,"p",64),t.DNE(5,Y,2,0,"span",65),t.DNE(6,w,2,0,"span",65),t.k0s(),t.DNE(7,L,3,0,"button",7),t.k0s()),2&n){const e=t.XpG();t.R7$(5),t.Y8G("ngIf",e.isPatient()),t.R7$(1),t.Y8G("ngIf",e.isDoctor()),t.R7$(1),t.Y8G("ngIf",e.isPatient())}}let X=(()=>{class n{constructor(e,o,i){this.appointmentService=e,this.authService=o,this.router=i,this.appointments=[],this.filteredAppointments=[],this.currentUser=null,this.loading=!1,this.error=null,this.statusFilter="",this.typeFilter="",this.statusOptions=[{value:"",label:"All Statuses"},{value:c.y.PENDING,label:"Pending"},{value:c.y.SCHEDULED,label:"Scheduled"},{value:c.y.CONFIRMED,label:"Confirmed"},{value:c.y.COMPLETED,label:"Completed"},{value:c.y.CANCELLED,label:"Cancelled"}],this.typeOptions=[{value:"",label:"All Types"},{value:c.Y.IN_PERSON,label:"In Person"},{value:c.Y.VIDEO_CALL,label:"Video Call"}]}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.loadAppointments()}loadAppointments(){this.loading=!0,this.error=null,this.appointmentService.getAppointments().subscribe({next:e=>{this.appointments=e,this.applyFilters(),this.loading=!1},error:e=>{this.error="Failed to load appointments. Please try again.",this.loading=!1,console.error("Error loading appointments:",e)}})}applyFilters(){this.filteredAppointments=this.appointments.filter(e=>!(this.statusFilter&&e.status!==this.statusFilter||this.typeFilter&&e.type!==this.typeFilter))}onStatusFilterChange(){this.applyFilters()}onTypeFilterChange(){this.applyFilters()}viewAppointment(e){this.router.navigate(["/appointments",e.id])}editAppointment(e){this.router.navigate(["/appointments",e.id])}cancelAppointment(e){confirm("Are you sure you want to cancel this appointment?")&&this.appointmentService.cancelAppointment(e.id).subscribe({next:()=>{this.loadAppointments()},error:o=>{this.error="Failed to cancel appointment. Please try again.",console.error("Error canceling appointment:",o)}})}getStatusDisplayName(e){return this.appointmentService.getStatusDisplayName(e)}getTypeDisplayName(e){return this.appointmentService.getTypeDisplayName(e)}isBeforeAppointment(e){return new Date(`${e.date}T${e.startTime}`)>new Date}isAfterAppointment(e){return new Date(`${e.date}T${e.endTime}`)<new Date}getStatusBadgeClass(e){return this.appointmentService.getStatusBadgeClass(e)}formatDate(e){return new Date(e).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"})}formatTime(e){const[o,i]=e.split(":"),a=parseInt(o);return`${a%12||12}:${i} ${a>=12?"PM":"AM"}`}isDoctor(){return"DOCTOR"===this.currentUser?.role}isPatient(){return"PATIENT"===this.currentUser?.role}canCancelAppointment(e){return e.status===c.y.PENDING||e.status===c.y.SCHEDULED||e.status===c.y.CONFIRMED}getOtherParty(e){return this.isDoctor()?e.patient.fullName:e.doctor.fullName}getOtherPartyRole(e){return this.isDoctor()?"Patient":"Doctor"}static{this.\u0275fac=function(o){return new(o||n)(t.rXU(_.h),t.rXU(v.u),t.rXU(d.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-appointment-list"]],decls:29,vars:9,consts:[[1,"container-fluid","py-4"],[1,"row"],[1,"col-12"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"card-title","mb-0"],[1,"fas","fa-calendar-alt","me-2"],["class","btn btn-primary","routerLink","/appointments/book",4,"ngIf"],[1,"card-body"],[1,"row","mb-4"],[1,"col-md-4"],["for","statusFilter",1,"form-label"],["id","statusFilter",1,"form-select",3,"ngModel","ngModelChange","change"],[3,"value",4,"ngFor","ngForOf"],["for","typeFilter",1,"form-label"],["id","typeFilter",1,"form-select",3,"ngModel","ngModelChange","change"],[1,"col-md-4","d-flex","align-items-end"],[1,"btn","btn-outline-secondary",3,"click"],[1,"fas","fa-sync-alt","me-2"],["class","alert alert-danger","role","alert",4,"ngIf"],["class","text-center py-4",4,"ngIf"],["class","appointments-list",4,"ngIf"],["class","text-center py-5",4,"ngIf"],["routerLink","/appointments/book",1,"btn","btn-primary"],[1,"fas","fa-plus","me-2"],[3,"value"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2","text-muted"],[1,"appointments-list"],["class","col-lg-6 mb-4",4,"ngFor","ngForOf"],[1,"col-lg-6","mb-4"],[1,"card","appointment-card","h-100"],[1,"d-flex","justify-content-between","align-items-start","mb-3"],[1,"card-title","mb-1"],[1,"text-muted"],[1,"badge",3,"ngClass"],[1,"appointment-details"],[1,"mb-2"],[1,"fas","fa-calendar","me-2","text-muted"],[1,"fas","fa-clock","me-2","text-muted"],["class","fas fa-video me-2 text-muted",4,"ngIf"],["class","fas fa-user-friends me-2 text-muted",4,"ngIf"],["class","mb-3",4,"ngIf"],[1,"d-flex","justify-content-between","align-items-center"],["role","group",1,"btn-group"],[1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"fas","fa-eye","me-1"],[3,"config",4,"ngIf"],["class","btn btn-sm btn-outline-danger",3,"click",4,"ngIf"],[1,"fas","fa-video","me-2","text-muted"],[1,"fas","fa-user-friends","me-2","text-muted"],[1,"mb-3"],[1,"fas","fa-notes-medical","me-2","text-muted"],["target","_blank",1,"btn","btn-sm","btn-outline-primary",3,"href"],[1,"fas","fa-video","me-2"],[3,"config"],[1,"btn","btn-sm","btn-outline-danger",3,"click"],[1,"fas","fa-times","me-1"],[1,"text-center","py-5"],[1,"fas","fa-calendar-times","fa-3x","text-muted","mb-3"],[1,"text-muted","mb-4"],[4,"ngIf"]],template:function(o,i){1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"h4",5),t.nrm(6,"i",6),t.EFF(7,"My Appointments "),t.k0s(),t.DNE(8,y,3,0,"button",7),t.k0s(),t.j41(9,"div",8)(10,"div",9)(11,"div",10)(12,"label",11),t.EFF(13,"Filter by Status"),t.k0s(),t.j41(14,"select",12),t.bIt("ngModelChange",function(l){return i.statusFilter=l})("change",function(){return i.onStatusFilterChange()}),t.DNE(15,D,2,2,"option",13),t.k0s()(),t.j41(16,"div",10)(17,"label",14),t.EFF(18,"Filter by Type"),t.k0s(),t.j41(19,"select",15),t.bIt("ngModelChange",function(l){return i.typeFilter=l})("change",function(){return i.onTypeFilterChange()}),t.DNE(20,E,2,2,"option",13),t.k0s()(),t.j41(21,"div",16)(22,"button",17),t.bIt("click",function(){return i.loadAppointments()}),t.nrm(23,"i",18),t.EFF(24," Refresh "),t.k0s()()(),t.DNE(25,I,3,1,"div",19),t.DNE(26,x,6,0,"div",20),t.DNE(27,N,3,1,"div",21),t.DNE(28,V,8,3,"div",22),t.k0s()()()()()),2&o&&(t.R7$(8),t.Y8G("ngIf",i.isPatient()),t.R7$(6),t.Y8G("ngModel",i.statusFilter),t.R7$(1),t.Y8G("ngForOf",i.statusOptions),t.R7$(4),t.Y8G("ngModel",i.typeFilter),t.R7$(1),t.Y8G("ngForOf",i.typeOptions),t.R7$(5),t.Y8G("ngIf",i.error),t.R7$(1),t.Y8G("ngIf",i.loading),t.R7$(1),t.Y8G("ngIf",!i.loading&&i.filteredAppointments.length>0),t.R7$(1),t.Y8G("ngIf",!i.loading&&0===i.filteredAppointments.length))},dependencies:[p.YU,p.Sq,p.bT,s.xH,s.y7,s.wz,s.BC,s.vS,d.Wk,F.Q],styles:[".appointment-card[_ngcontent-%COMP%]{transition:transform .2s ease-in-out,box-shadow .2s ease-in-out;border:1px solid #e3e6f0}.appointment-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 .5rem 1rem #00000026}.appointment-details[_ngcontent-%COMP%]{font-size:.9rem}.appointment-details[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:16px;text-align:center}.badge-warning[_ngcontent-%COMP%]{background-color:#f6c23e;color:#1a1a1a}.badge-info[_ngcontent-%COMP%]{background-color:#36b9cc}.badge-primary[_ngcontent-%COMP%]{background-color:#4e73df}.badge-success[_ngcontent-%COMP%]{background-color:#1cc88a}.badge-danger[_ngcontent-%COMP%]{background-color:#e74a3b}.badge-secondary[_ngcontent-%COMP%]{background-color:#858796}.form-select[_ngcontent-%COMP%]:focus{border-color:#667eea;box-shadow:0 0 0 .2rem #667eea40}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border:none;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#5a6fd8 0%,#6a4190 100%);transform:translateY(-1px)}.btn-outline-primary[_ngcontent-%COMP%]{border-color:#667eea;color:#667eea}.btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#667eea;border-color:#667eea}.btn-outline-danger[_ngcontent-%COMP%]{border-color:#e74a3b;color:#e74a3b}.btn-outline-danger[_ngcontent-%COMP%]:hover{background-color:#e74a3b;border-color:#e74a3b}.btn-outline-secondary[_ngcontent-%COMP%]{border-color:#858796;color:#858796}.btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#858796;border-color:#858796}.card[_ngcontent-%COMP%]{border:1px solid #e3e6f0;box-shadow:0 .15rem 1.75rem #3a3b4526}.card-title[_ngcontent-%COMP%]{color:#5a5c69;font-weight:600}.alert-danger[_ngcontent-%COMP%]{border-left:4px solid #e74a3b}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}"]})}}return n})();function B(n,r){if(1&n&&(t.j41(0,"div",11),t.nrm(1,"i",12),t.EFF(2),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.success," ")}}function U(n,r){if(1&n&&(t.j41(0,"div",13),t.nrm(1,"i",14),t.EFF(2),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function z(n,r){if(1&n&&(t.j41(0,"option",45),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e.id),t.R7$(1),t.Lme(" ",e.fullName," - ",e.specialization||"General Practice"," ")}}function J(n,r){1&n&&(t.j41(0,"div",34),t.EFF(1," Please select a doctor. "),t.k0s())}function H(n,r){if(1&n&&(t.j41(0,"p",53),t.EFF(1),t.k0s()),2&n){const e=t.XpG(3);t.R7$(1),t.SpI(" ",e.selectedDoctor.affiliation," ")}}function q(n,r){if(1&n&&(t.j41(0,"div",46)(1,"div",7)(2,"div",47)(3,"div",48),t.nrm(4,"i",49),t.k0s(),t.j41(5,"div")(6,"h6",50),t.EFF(7),t.k0s(),t.j41(8,"p",51),t.EFF(9),t.k0s(),t.DNE(10,H,2,1,"p",52),t.k0s()()()()),2&n){const e=t.XpG(2);t.R7$(7),t.JRh(e.selectedDoctor.fullName),t.R7$(2),t.JRh(e.selectedDoctor.specialization||"General Practice"),t.R7$(1),t.Y8G("ngIf",e.selectedDoctor.affiliation)}}function W(n,r){1&n&&(t.j41(0,"div",34),t.EFF(1," Please select a date. "),t.k0s())}function Q(n,r){if(1&n&&(t.j41(0,"option",45),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e.value),t.R7$(1),t.SpI(" ",e.label," ")}}function K(n,r){1&n&&(t.j41(0,"div",34),t.EFF(1," Please select an appointment type. "),t.k0s())}function Z(n,r){1&n&&(t.j41(0,"div",54)(1,"div",55)(2,"span",56),t.EFF(3,"Loading..."),t.k0s()(),t.EFF(4," Loading available time slots... "),t.k0s())}function tt(n,r){if(1&n&&(t.j41(0,"div",59)(1,"label",60),t.nrm(2,"input",61),t.j41(3,"span",62),t.EFF(4),t.k0s()()()),2&n){const e=r.$implicit,o=t.XpG(3);t.R7$(2),t.Y8G("value",o.getTimeSlotValue(e)),t.R7$(2),t.SpI(" ",o.getTimeSlotDisplay(e)," ")}}function et(n,r){if(1&n&&(t.j41(0,"div",57)(1,"div",23),t.DNE(2,tt,5,2,"div",58),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(2),t.Y8G("ngForOf",e.availableSlots)}}function nt(n,r){1&n&&(t.j41(0,"div",63),t.nrm(1,"i",64),t.EFF(2," No available time slots for the selected date. Please choose a different date. "),t.k0s())}function ot(n,r){1&n&&(t.j41(0,"div",34),t.EFF(1," Please provide a reason for the visit. "),t.k0s())}function it(n,r){1&n&&(t.j41(0,"span",65)(1,"span",56),t.EFF(2,"Loading..."),t.k0s()())}function rt(n,r){1&n&&t.nrm(0,"i",66)}function at(n,r){if(1&n){const e=t.RV6();t.j41(0,"form",15),t.bIt("ngSubmit",function(){t.eBV(e);const i=t.XpG();return t.Njj(i.onSubmit())}),t.j41(1,"div",16)(2,"label",17),t.EFF(3,"Select Doctor *"),t.k0s(),t.j41(4,"select",18)(5,"option",19),t.EFF(6,"Choose a doctor..."),t.k0s(),t.DNE(7,z,2,3,"option",20),t.k0s(),t.DNE(8,J,2,0,"div",21),t.k0s(),t.DNE(9,q,11,3,"div",22),t.j41(10,"div",23)(11,"div",24)(12,"label",25),t.EFF(13,"Appointment Date *"),t.k0s(),t.nrm(14,"input",26),t.DNE(15,W,2,0,"div",21),t.k0s(),t.j41(16,"div",24)(17,"label",27),t.EFF(18,"Appointment Type *"),t.k0s(),t.j41(19,"select",28),t.DNE(20,Q,2,2,"option",20),t.k0s(),t.DNE(21,K,2,0,"div",21),t.k0s()(),t.j41(22,"div",29)(23,"label",30),t.EFF(24,"Available Time Slots *"),t.k0s(),t.DNE(25,Z,5,0,"div",31),t.DNE(26,et,3,1,"div",32),t.DNE(27,nt,3,0,"div",33),t.j41(28,"div",34),t.EFF(29," Please select a time slot. "),t.k0s()(),t.j41(30,"div",29)(31,"label",35),t.EFF(32,"Reason for Visit *"),t.k0s(),t.nrm(33,"input",36),t.DNE(34,ot,2,0,"div",21),t.k0s(),t.j41(35,"div",16)(36,"label",37),t.EFF(37,"Additional Notes (Optional)"),t.k0s(),t.nrm(38,"textarea",38),t.k0s(),t.j41(39,"div",39)(40,"button",40),t.nrm(41,"i",41),t.EFF(42," Back to Doctors "),t.k0s(),t.j41(43,"button",42),t.DNE(44,it,3,0,"span",43),t.DNE(45,rt,1,0,"i",44),t.EFF(46),t.k0s()()()}if(2&n){const e=t.XpG();let o,i,a,l,u,g,f,h,b;t.Y8G("formGroup",e.bookingForm),t.R7$(4),t.AVh("is-invalid",(null==(o=e.bookingForm.get("doctorId"))?null:o.invalid)&&(null==(o=e.bookingForm.get("doctorId"))?null:o.touched)),t.R7$(3),t.Y8G("ngForOf",e.doctors),t.R7$(1),t.Y8G("ngIf",(null==(i=e.bookingForm.get("doctorId"))?null:i.invalid)&&(null==(i=e.bookingForm.get("doctorId"))?null:i.touched)),t.R7$(1),t.Y8G("ngIf",e.selectedDoctor),t.R7$(5),t.AVh("is-invalid",(null==(a=e.bookingForm.get("date"))?null:a.invalid)&&(null==(a=e.bookingForm.get("date"))?null:a.touched)),t.Y8G("min",e.getTodayDate()),t.R7$(1),t.Y8G("ngIf",(null==(l=e.bookingForm.get("date"))?null:l.invalid)&&(null==(l=e.bookingForm.get("date"))?null:l.touched)),t.R7$(4),t.AVh("is-invalid",(null==(u=e.bookingForm.get("type"))?null:u.invalid)&&(null==(u=e.bookingForm.get("type"))?null:u.touched)),t.R7$(1),t.Y8G("ngForOf",e.appointmentTypes),t.R7$(1),t.Y8G("ngIf",(null==(g=e.bookingForm.get("type"))?null:g.invalid)&&(null==(g=e.bookingForm.get("type"))?null:g.touched)),t.R7$(4),t.Y8G("ngIf",e.loading),t.R7$(1),t.Y8G("ngIf",!e.loading&&e.availableSlots.length>0),t.R7$(1),t.Y8G("ngIf",!e.loading&&e.selectedDoctor&&0===e.availableSlots.length),t.R7$(1),t.xc7("display",null!=(f=e.bookingForm.get("timeSlot"))&&f.invalid&&null!=(f=e.bookingForm.get("timeSlot"))&&f.touched?"block":"none"),t.R7$(5),t.AVh("is-invalid",(null==(h=e.bookingForm.get("reasonForVisit"))?null:h.invalid)&&(null==(h=e.bookingForm.get("reasonForVisit"))?null:h.touched)),t.R7$(1),t.Y8G("ngIf",(null==(b=e.bookingForm.get("reasonForVisit"))?null:b.invalid)&&(null==(b=e.bookingForm.get("reasonForVisit"))?null:b.touched)),t.R7$(9),t.Y8G("disabled",e.bookingForm.invalid||e.submitting),t.R7$(1),t.Y8G("ngIf",e.submitting),t.R7$(1),t.Y8G("ngIf",!e.submitting),t.R7$(1),t.SpI(" ",e.submitting?"Booking...":"Book Appointment"," ")}}let st=(()=>{class n{constructor(e,o,i,a){this.fb=e,this.appointmentService=o,this.route=i,this.router=a,this.doctors=[],this.selectedDoctor=null,this.timeSlots=[],this.availableSlots=[],this.loading=!1,this.submitting=!1,this.error=null,this.success=null,this.appointmentTypes=[{value:c.Y.IN_PERSON,label:"In Person"},{value:c.Y.VIDEO_CALL,label:"Video Call"}],this.initializeForm()}initializeForm(){this.bookingForm=this.fb.group({doctorId:["",s.k0.required],date:["",s.k0.required],timeSlot:["",s.k0.required],type:[c.Y.IN_PERSON,s.k0.required],reasonForVisit:["",s.k0.required],notes:[""]})}ngOnInit(){this.loadDoctors(),this.route.queryParams.subscribe(i=>{i.doctorId&&(this.bookingForm.patchValue({doctorId:i.doctorId}),this.onDoctorChange())});const e=new Date;e.setDate(e.getDate()+1);const o=e.toISOString().split("T")[0];this.bookingForm.get("date")?.setValue(o),this.bookingForm.get("doctorId")?.valueChanges.subscribe(()=>this.onDoctorChange()),this.bookingForm.get("date")?.valueChanges.subscribe(()=>this.onDateChange())}loadDoctors(){this.appointmentService.getDoctors().subscribe({next:e=>{this.doctors=e},error:e=>{this.error="Failed to load doctors. Please try again.",console.error("Error loading doctors:",e)}})}onDoctorChange(){const e=this.bookingForm.get("doctorId")?.value;e?(this.selectedDoctor=this.doctors.find(o=>o.id==e)||null,this.loadTimeSlots()):(this.selectedDoctor=null,this.timeSlots=[],this.availableSlots=[])}onDateChange(){this.selectedDoctor&&this.loadTimeSlots()}loadTimeSlots(){const e=this.bookingForm.get("doctorId")?.value,o=this.bookingForm.get("date")?.value;!e||!o||(this.loading=!0,this.appointmentService.getAvailableTimeSlots(e,o).subscribe({next:i=>{this.timeSlots=i,this.availableSlots=i.filter(l=>l.available),this.loading=!1;const a=this.bookingForm.get("timeSlot")?.value;a&&!this.availableSlots.find(l=>l.startTime===a.split("-")[0]&&l.endTime===a.split("-")[1])&&this.bookingForm.patchValue({timeSlot:""})},error:i=>{this.error="Failed to load available time slots. Please try again.",this.loading=!1,console.error("Error loading time slots:",i)}}))}onSubmit(){if(console.log("Form submission started"),console.log("Form valid:",this.bookingForm.valid),console.log("Form errors:",this.bookingForm.errors),console.log("Form value:",this.bookingForm.value),this.bookingForm.valid){this.submitting=!0,this.error=null,this.success=null;const e=this.bookingForm.value;if(!e.timeSlot||!e.timeSlot.includes("-"))return this.error="Please select a valid time slot.",this.submitting=!1,void console.error("Invalid time slot format:",e.timeSlot);const[o,i]=e.timeSlot.split("-"),a={doctorId:parseInt(e.doctorId),date:e.date,startTime:o,endTime:i,type:e.type,reasonForVisit:e.reasonForVisit,notes:e.notes||void 0};console.log("Submitting appointment request:",a),console.log("Form value:",e),this.appointmentService.createAppointment(a).subscribe({next:l=>{this.success="Appointment booked successfully!",this.submitting=!1,setTimeout(()=>{this.router.navigate(["/appointments",l.id])},2e3)},error:l=>{this.error="Failed to book appointment. Please try again.",this.submitting=!1,console.error("Error booking appointment:",l),console.error("Error details:",l.error),console.error("Request data:",a)}})}else console.log("Form is invalid"),console.log("Form errors:",this.getFormValidationErrors()),this.error="Please fill in all required fields correctly.",this.markFormGroupTouched()}getFormValidationErrors(){const e={};return Object.keys(this.bookingForm.controls).forEach(o=>{const i=this.bookingForm.get(o)?.errors;i&&(e[o]=i)}),e}markFormGroupTouched(){Object.keys(this.bookingForm.controls).forEach(e=>{this.bookingForm.get(e)?.markAsTouched()})}getTimeSlotDisplay(e){return`${this.formatTime(e.startTime)} - ${this.formatTime(e.endTime)}`}getTimeSlotValue(e){return`${e.startTime}-${e.endTime}`}formatTime(e){const[o,i]=e.split(":"),a=parseInt(o);return`${a%12||12}:${i} ${a>=12?"PM":"AM"}`}getTodayDate(){const e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]}static{this.\u0275fac=function(o){return new(o||n)(t.rXU(s.ok),t.rXU(_.h),t.rXU(d.nX),t.rXU(d.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-appointment-booking"]],decls:12,vars:3,consts:[[1,"container-fluid","py-4"],[1,"row","justify-content-center"],[1,"col-lg-8"],[1,"card"],[1,"card-header"],[1,"card-title","mb-0"],[1,"fas","fa-calendar-plus","me-2"],[1,"card-body"],["class","alert alert-success","role","alert",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],[3,"formGroup","ngSubmit",4,"ngIf"],["role","alert",1,"alert","alert-success"],[1,"fas","fa-check-circle","me-2"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[3,"formGroup","ngSubmit"],[1,"mb-4"],["for","doctorId",1,"form-label"],["id","doctorId","formControlName","doctorId",1,"form-select"],["value",""],[3,"value",4,"ngFor","ngForOf"],["class","invalid-feedback",4,"ngIf"],["class","card bg-light mb-4",4,"ngIf"],[1,"row"],[1,"col-md-6","mb-3"],["for","date",1,"form-label"],["type","date","id","date","formControlName","date",1,"form-control",3,"min"],["for","type",1,"form-label"],["id","type","formControlName","type",1,"form-select"],[1,"mb-3"],["for","timeSlot",1,"form-label"],["class","text-center py-3",4,"ngIf"],["class","time-slots-grid",4,"ngIf"],["class","alert alert-info",4,"ngIf"],[1,"invalid-feedback"],["for","reasonForVisit",1,"form-label"],["type","text","id","reasonForVisit","formControlName","reasonForVisit","placeholder","e.g., Regular checkup, Follow-up, Consultation",1,"form-control"],["for","notes",1,"form-label"],["id","notes","formControlName","notes","rows","3","placeholder","Any additional information or special requests...",1,"form-control"],[1,"d-flex","justify-content-between"],["type","button","routerLink","/appointments/doctors",1,"btn","btn-outline-secondary"],[1,"fas","fa-arrow-left","me-2"],["type","submit",1,"btn","btn-primary",3,"disabled"],["class","spinner-border spinner-border-sm me-2","role","status",4,"ngIf"],["class","fas fa-calendar-check me-2",4,"ngIf"],[3,"value"],[1,"card","bg-light","mb-4"],[1,"d-flex","align-items-center"],[1,"avatar-circle","me-3"],[1,"fas","fa-user-md"],[1,"mb-1"],[1,"text-muted","mb-1"],["class","text-muted mb-0",4,"ngIf"],[1,"text-muted","mb-0"],[1,"text-center","py-3"],["role","status",1,"spinner-border","spinner-border-sm","text-primary","me-2"],[1,"visually-hidden"],[1,"time-slots-grid"],["class","col-md-4 col-sm-6 mb-2",4,"ngFor","ngForOf"],[1,"col-md-4","col-sm-6","mb-2"],[1,"time-slot-option"],["type","radio","name","timeSlot","formControlName","timeSlot",3,"value"],[1,"time-slot-label"],[1,"alert","alert-info"],[1,"fas","fa-info-circle","me-2"],["role","status",1,"spinner-border","spinner-border-sm","me-2"],[1,"fas","fa-calendar-check","me-2"]],template:function(o,i){1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"h4",5),t.nrm(6,"i",6),t.EFF(7,"Book an Appointment "),t.k0s()(),t.j41(8,"div",7),t.DNE(9,B,3,1,"div",8),t.DNE(10,U,3,1,"div",9),t.DNE(11,at,47,26,"form",10),t.k0s()()()()()),2&o&&(t.R7$(9),t.Y8G("ngIf",i.success),t.R7$(1),t.Y8G("ngIf",i.error),t.R7$(1),t.Y8G("ngIf",i.bookingForm))},dependencies:[p.Sq,p.bT,s.qT,s.xH,s.y7,s.me,s.wz,s.Fm,s.BC,s.cb,s.j4,s.JD,d.Wk],styles:[".avatar-circle[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:50%;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.2rem;flex-shrink:0}.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]{display:block;cursor:pointer;margin:0}.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{display:none}.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   .time-slot-label[_ngcontent-%COMP%]{display:block;padding:.75rem 1rem;border:2px solid #e3e6f0;border-radius:.5rem;text-align:center;transition:all .3s ease;background:white;font-weight:500}.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   .time-slot-label[_ngcontent-%COMP%]:hover{border-color:#667eea;background:#f8f9fc}.time-slots-grid[_ngcontent-%COMP%]   .time-slot-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + .time-slot-label[_ngcontent-%COMP%]{border-color:#667eea;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff}.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus{border-color:#667eea;box-shadow:0 0 0 .2rem #667eea40}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border:none;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#5a6fd8 0%,#6a4190 100%);transform:translateY(-1px)}.btn-primary[_ngcontent-%COMP%]:disabled{background:#6c757d;opacity:.65}.alert-success[_ngcontent-%COMP%]{border-left:4px solid #28a745}.alert-danger[_ngcontent-%COMP%]{border-left:4px solid #e74a3b}.alert-info[_ngcontent-%COMP%]{border-left:4px solid #17a2b8}.card[_ngcontent-%COMP%]{border:1px solid #e3e6f0;box-shadow:0 .15rem 1.75rem #3a3b4526}.bg-light[_ngcontent-%COMP%]{background-color:#f8f9fc!important}"]})}}return n})();var lt=m(4719);function ct(n,r){if(1&n&&(t.j41(0,"option",23),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e),t.R7$(1),t.SpI(" ",e," ")}}function pt(n,r){if(1&n&&(t.j41(0,"div",24),t.nrm(1,"i",25),t.EFF(2),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function dt(n,r){1&n&&(t.j41(0,"div",26)(1,"div",27)(2,"span",28),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",29),t.EFF(5,"Searching for doctors..."),t.k0s()())}function mt(n,r){if(1&n&&(t.j41(0,"p",40),t.nrm(1,"i",49),t.EFF(2),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(2),t.SpI(" ",e.affiliation," ")}}const ut=function(n){return{doctorId:n,chatType:"GENERAL",buttonText:"Start Chat",buttonClass:"btn-outline-info",size:"sm"}};function gt(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",31)(1,"div",32)(2,"div",7)(3,"div",33)(4,"div",34),t.nrm(5,"i",35),t.k0s(),t.j41(6,"div")(7,"h5",36),t.EFF(8),t.k0s(),t.j41(9,"p",37),t.EFF(10),t.k0s()()(),t.j41(11,"div",38),t.DNE(12,mt,3,1,"p",39),t.j41(13,"p",40),t.nrm(14,"i",41),t.EFF(15),t.k0s(),t.j41(16,"p",42),t.nrm(17,"i",43),t.EFF(18),t.k0s()(),t.j41(19,"div",44)(20,"button",45),t.bIt("click",function(){const a=t.eBV(e).$implicit,l=t.XpG(2);return t.Njj(l.bookAppointment(a))}),t.nrm(21,"i",46),t.EFF(22," Book Appointment "),t.k0s(),t.nrm(23,"app-chat-access",47)(24,"app-doctor-availability",48),t.k0s()()()()}if(2&n){const e=r.$implicit,o=t.XpG(2);t.R7$(8),t.JRh(e.fullName),t.R7$(2),t.JRh(e.specialization||"General Practice"),t.R7$(2),t.Y8G("ngIf",e.affiliation),t.R7$(3),t.SpI(" ",o.getExperienceText(e.yearsOfExperience)," "),t.R7$(3),t.SpI(" ",e.email," "),t.R7$(5),t.Y8G("config",t.eq3(8,ut,e.id)),t.R7$(1),t.Y8G("doctorId",e.id)("showDetails",!0)}}function ft(n,r){if(1&n&&(t.j41(0,"div",1),t.DNE(1,gt,25,10,"div",30),t.k0s()),2&n){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.doctors)}}function _t(n,r){1&n&&(t.j41(0,"div",50),t.nrm(1,"i",51),t.j41(2,"h5",52),t.EFF(3,"No doctors found"),t.k0s(),t.j41(4,"p",52),t.EFF(5,"Try adjusting your search criteria or check back later."),t.k0s()())}let ht=(()=>{class n{constructor(e,o,i){this.fb=e,this.appointmentService=o,this.router=i,this.doctors=[],this.specializations=[],this.loading=!1,this.error=null,this.initializeForm()}initializeForm(){this.searchForm=this.fb.group({specialization:[""]})}ngOnInit(){this.loadSpecializations(),this.loadDoctors()}loadSpecializations(){this.appointmentService.getSpecializations().subscribe({next:e=>{this.specializations=e},error:e=>{console.error("Error loading specializations:",e)}})}loadDoctors(){this.loading=!0,this.error=null;const e=this.searchForm.get("specialization")?.value;this.appointmentService.getDoctors(e||void 0).subscribe({next:o=>{this.doctors=o,this.loading=!1},error:o=>{this.error="Failed to load doctors. Please try again.",this.loading=!1,console.error("Error loading doctors:",o)}})}onSearch(){this.loadDoctors()}onClearSearch(){this.searchForm.reset(),this.loadDoctors()}bookAppointment(e){this.router.navigate(["/appointments/book"],{queryParams:{doctorId:e.id}})}getExperienceText(e){return e?1===e?"1 year experience":`${e} years experience`:"Experience not specified"}static{this.\u0275fac=function(o){return new(o||n)(t.rXU(s.ok),t.rXU(_.h),t.rXU(d.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-doctor-search"]],decls:29,vars:8,consts:[[1,"container-fluid","py-4"],[1,"row"],[1,"col-12"],[1,"card"],[1,"card-header"],[1,"card-title","mb-0"],[1,"fas","fa-user-md","me-2"],[1,"card-body"],[1,"mb-4",3,"formGroup","ngSubmit"],[1,"col-md-6"],["for","specialization",1,"form-label"],["id","specialization","formControlName","specialization",1,"form-select"],["value",""],[3,"value",4,"ngFor","ngForOf"],[1,"col-md-6","d-flex","align-items-end"],["type","submit",1,"btn","btn-primary","me-2",3,"disabled"],[1,"fas","fa-search","me-1"],["type","button",1,"btn","btn-outline-secondary",3,"click"],[1,"fas","fa-times","me-1"],["class","alert alert-danger","role","alert",4,"ngIf"],["class","text-center py-4",4,"ngIf"],["class","row",4,"ngIf"],["class","text-center py-5",4,"ngIf"],[3,"value"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2","text-muted"],["class","col-md-6 col-lg-4 mb-4",4,"ngFor","ngForOf"],[1,"col-md-6","col-lg-4","mb-4"],[1,"card","h-100","doctor-card"],[1,"d-flex","align-items-center","mb-3"],[1,"avatar-circle","me-3"],[1,"fas","fa-user-md"],[1,"card-title","mb-1"],[1,"text-muted","mb-0"],[1,"doctor-info"],["class","mb-2",4,"ngIf"],[1,"mb-2"],[1,"fas","fa-clock","me-2","text-muted"],[1,"mb-3"],[1,"fas","fa-envelope","me-2","text-muted"],[1,"d-grid","gap-2"],[1,"btn","btn-primary",3,"click"],[1,"fas","fa-calendar-plus","me-2"],[3,"config"],["size","sm",3,"doctorId","showDetails"],[1,"fas","fa-hospital","me-2","text-muted"],[1,"text-center","py-5"],[1,"fas","fa-user-md","fa-3x","text-muted","mb-3"],[1,"text-muted"]],template:function(o,i){1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"h4",5),t.nrm(6,"i",6),t.EFF(7,"Find a Doctor "),t.k0s()(),t.j41(8,"div",7)(9,"form",8),t.bIt("ngSubmit",function(){return i.onSearch()}),t.j41(10,"div",1)(11,"div",9)(12,"label",10),t.EFF(13,"Specialization"),t.k0s(),t.j41(14,"select",11)(15,"option",12),t.EFF(16,"All Specializations"),t.k0s(),t.DNE(17,ct,2,2,"option",13),t.k0s()(),t.j41(18,"div",14)(19,"button",15),t.nrm(20,"i",16),t.EFF(21),t.k0s(),t.j41(22,"button",17),t.bIt("click",function(){return i.onClearSearch()}),t.nrm(23,"i",18),t.EFF(24," Clear "),t.k0s()()()(),t.DNE(25,pt,3,1,"div",19),t.DNE(26,dt,6,0,"div",20),t.DNE(27,ft,2,1,"div",21),t.DNE(28,_t,6,0,"div",22),t.k0s()()()()()),2&o&&(t.R7$(9),t.Y8G("formGroup",i.searchForm),t.R7$(8),t.Y8G("ngForOf",i.specializations),t.R7$(2),t.Y8G("disabled",i.loading),t.R7$(2),t.SpI(" ",i.loading?"Searching...":"Search"," "),t.R7$(4),t.Y8G("ngIf",i.error),t.R7$(1),t.Y8G("ngIf",i.loading),t.R7$(1),t.Y8G("ngIf",!i.loading&&i.doctors.length>0),t.R7$(1),t.Y8G("ngIf",!i.loading&&0===i.doctors.length))},dependencies:[p.Sq,p.bT,s.qT,s.xH,s.y7,s.wz,s.BC,s.cb,s.j4,s.JD,F.Q,lt.U],styles:[".doctor-card[_ngcontent-%COMP%]{transition:transform .2s ease-in-out,box-shadow .2s ease-in-out;border:1px solid #e3e6f0}.doctor-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 .5rem 1rem #00000026}.avatar-circle[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.5rem;flex-shrink:0}.doctor-info[_ngcontent-%COMP%]{font-size:.9rem}.doctor-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:16px;text-align:center}.card-title[_ngcontent-%COMP%]{color:#5a5c69;font-weight:600}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border:none;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#5a6fd8 0%,#6a4190 100%);transform:translateY(-1px)}.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus{border-color:#667eea;box-shadow:0 0 0 .2rem #667eea40}.alert-danger[_ngcontent-%COMP%]{border-left:4px solid #e74a3b}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}"]})}}return n})();function bt(n,r){1&n&&(t.j41(0,"div",5)(1,"div",6)(2,"span",7),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",8),t.EFF(5,"Loading appointment details..."),t.k0s()())}function vt(n,r){if(1&n&&(t.j41(0,"div",30),t.nrm(1,"i",31),t.EFF(2),t.k0s()),2&n){const e=t.XpG(2);t.R7$(2),t.SpI(" ",e.success," ")}}function Ft(n,r){if(1&n&&(t.j41(0,"div",32),t.nrm(1,"i",33),t.EFF(2),t.k0s()),2&n){const e=t.XpG(2);t.R7$(2),t.SpI(" ",e.error," ")}}function kt(n,r){if(1&n&&(t.j41(0,"option",51),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e.value),t.R7$(1),t.SpI(" ",e.label," ")}}function Ct(n,r){if(1&n&&(t.j41(0,"option",51),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.Y8G("value",e.value),t.R7$(1),t.SpI(" ",e.label," ")}}function yt(n,r){1&n&&(t.j41(0,"div",41)(1,"label",52),t.EFF(2,"Meeting Link"),t.k0s(),t.nrm(3,"input",53),t.k0s())}function Dt(n,r){1&n&&(t.j41(0,"span",54)(1,"span",7),t.EFF(2,"Loading..."),t.k0s()())}function Et(n,r){if(1&n){const e=t.RV6();t.j41(0,"form",34),t.bIt("ngSubmit",function(){t.eBV(e);const i=t.XpG(2);return t.Njj(i.onUpdate())}),t.j41(1,"div",35)(2,"div",18)(3,"label",36),t.EFF(4,"Status"),t.k0s(),t.j41(5,"select",37),t.DNE(6,kt,2,2,"option",38),t.k0s()(),t.j41(7,"div",18)(8,"label",39),t.EFF(9,"Type"),t.k0s(),t.j41(10,"select",40),t.DNE(11,Ct,2,2,"option",38),t.k0s()()(),t.j41(12,"div",41)(13,"label",42),t.EFF(14,"Reason for Visit"),t.k0s(),t.nrm(15,"input",43),t.k0s(),t.j41(16,"div",41)(17,"label",44),t.EFF(18,"Notes"),t.k0s(),t.nrm(19,"textarea",45),t.k0s(),t.DNE(20,yt,4,0,"div",46),t.j41(21,"div",47)(22,"button",48),t.bIt("click",function(){t.eBV(e);const i=t.XpG(2);return t.Njj(i.toggleEdit())}),t.EFF(23," Cancel "),t.k0s(),t.j41(24,"button",49),t.DNE(25,Dt,3,0,"span",50),t.EFF(26),t.k0s()()()}if(2&n){const e=t.XpG(2);let o;t.Y8G("formGroup",e.editForm),t.R7$(6),t.Y8G("ngForOf",e.statusOptions),t.R7$(5),t.Y8G("ngForOf",e.typeOptions),t.R7$(9),t.Y8G("ngIf","VIDEO_CALL"===(null==(o=e.editForm.get("type"))?null:o.value)),t.R7$(4),t.Y8G("disabled",e.editForm.invalid||e.updating),t.R7$(1),t.Y8G("ngIf",e.updating),t.R7$(1),t.SpI(" ",e.updating?"Updating...":"Update Appointment"," ")}}function It(n,r){1&n&&t.nrm(0,"i",62)}function xt(n,r){1&n&&t.nrm(0,"i",63)}function Tt(n,r){if(1&n&&(t.j41(0,"div",64)(1,"div",19)(2,"h6",20),t.nrm(3,"i",65),t.EFF(4," Notes "),t.k0s(),t.j41(5,"p",22),t.EFF(6),t.k0s()()()),2&n){const e=t.XpG(3);t.R7$(6),t.JRh(e.appointment.notes)}}function Ot(n,r){if(1&n&&(t.j41(0,"div",64)(1,"div",19)(2,"h6",20),t.nrm(3,"i",62),t.EFF(4," Video Call "),t.k0s(),t.j41(5,"a",66),t.nrm(6,"i",67),t.EFF(7," Join Meeting "),t.k0s()()()),2&n){const e=t.XpG(3);t.R7$(5),t.Y8G("href",e.appointment.meetingLink,t.B4B)}}function Mt(n,r){if(1&n&&(t.j41(0,"div")(1,"div",17)(2,"div",18)(3,"div",19)(4,"h6",20),t.DNE(5,It,1,0,"i",55),t.DNE(6,xt,1,0,"i",56),t.EFF(7," Appointment Type "),t.k0s(),t.j41(8,"p",22),t.EFF(9),t.k0s()()(),t.j41(10,"div",18)(11,"div",19)(12,"h6",20),t.nrm(13,"i",57),t.EFF(14," Reason for Visit "),t.k0s(),t.j41(15,"p",22),t.EFF(16),t.k0s()()()(),t.DNE(17,Tt,7,1,"div",58),t.DNE(18,Ot,8,1,"div",58),t.j41(19,"div",59)(20,"div",18)(21,"div",19)(22,"h6",20),t.nrm(23,"i",60),t.EFF(24," Created "),t.k0s(),t.j41(25,"p",22),t.EFF(26),t.nI1(27,"date"),t.k0s()()(),t.j41(28,"div",18)(29,"div",19)(30,"h6",20),t.nrm(31,"i",61),t.EFF(32," Last Updated "),t.k0s(),t.j41(33,"p",22),t.EFF(34),t.nI1(35,"date"),t.k0s()()()()()),2&n){const e=t.XpG(2);t.R7$(5),t.Y8G("ngIf","VIDEO_CALL"===e.appointment.type),t.R7$(1),t.Y8G("ngIf","IN_PERSON"===e.appointment.type),t.R7$(3),t.JRh(e.getTypeDisplayName(e.appointment.type)),t.R7$(7),t.JRh(e.appointment.reasonForVisit||"Not specified"),t.R7$(1),t.Y8G("ngIf",e.appointment.notes),t.R7$(1),t.Y8G("ngIf",e.appointment.meetingLink&&"VIDEO_CALL"===e.appointment.type),t.R7$(8),t.JRh(t.i5U(27,8,e.appointment.createdAt,"medium")),t.R7$(8),t.JRh(t.i5U(35,11,e.appointment.updatedAt,"medium"))}}const Pt=function(n,r,e,o){return{appointmentId:n,doctorId:r,patientId:e,chatType:"PRE_APPOINTMENT",subject:o,buttonText:"Chat Before Appointment",buttonClass:"btn-info",size:"sm"}};function St(n,r){if(1&n&&t.nrm(0,"app-chat-access",70),2&n){const e=t.XpG(3);t.Y8G("config",t.ziG(1,Pt,e.appointment.id,e.appointment.doctor.id,e.appointment.patient.id,"Pre-appointment discussion for "+e.appointment.date))}}const At=function(n,r,e,o){return{appointmentId:n,doctorId:r,patientId:e,chatType:"POST_APPOINTMENT",subject:o,buttonText:"Follow-up Chat",buttonClass:"btn-success",size:"sm"}};function jt(n,r){if(1&n&&t.nrm(0,"app-chat-access",70),2&n){const e=t.XpG(3);t.Y8G("config",t.ziG(1,At,e.appointment.id,e.appointment.doctor.id,e.appointment.patient.id,"Follow-up for appointment on "+e.appointment.date))}}function $t(n,r){if(1&n){const e=t.RV6();t.j41(0,"button",74),t.bIt("click",function(){t.eBV(e);const i=t.XpG(3);return t.Njj(i.toggleEdit())}),t.nrm(1,"i",61),t.EFF(2," Edit "),t.k0s()}}function Rt(n,r){if(1&n){const e=t.RV6();t.j41(0,"button",75),t.bIt("click",function(){t.eBV(e);const i=t.XpG(3);return t.Njj(i.onCancel())}),t.nrm(1,"i",76),t.EFF(2," Cancel Appointment "),t.k0s()}}const Gt=function(n,r){return{doctorId:n,patientId:r,chatType:"GENERAL",buttonText:"General Chat",buttonClass:"btn-outline-primary",size:"sm"}};function Nt(n,r){if(1&n&&(t.j41(0,"div")(1,"div",68),t.DNE(2,St,1,6,"app-chat-access",69),t.DNE(3,jt,1,6,"app-chat-access",69),t.nrm(4,"app-chat-access",70),t.k0s(),t.j41(5,"div",71),t.DNE(6,$t,3,0,"button",72),t.DNE(7,Rt,3,0,"button",73),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(2),t.Y8G("ngIf",e.isBeforeAppointment()),t.R7$(1),t.Y8G("ngIf",e.isAfterAppointment()),t.R7$(1),t.Y8G("config",t.l_i(5,Gt,e.appointment.doctor.id,e.appointment.patient.id)),t.R7$(2),t.Y8G("ngIf",e.canEdit()),t.R7$(1),t.Y8G("ngIf",e.canCancel())}}function Yt(n,r){if(1&n&&(t.j41(0,"div",9)(1,"div",10)(2,"h4",11),t.nrm(3,"i",12),t.EFF(4,"Appointment Details "),t.k0s(),t.j41(5,"span",13),t.EFF(6),t.k0s()(),t.j41(7,"div",14),t.DNE(8,vt,3,1,"div",15),t.DNE(9,Ft,3,1,"div",16),t.j41(10,"div",17)(11,"div",18)(12,"div",19)(13,"h6",20),t.nrm(14,"i",21),t.EFF(15),t.k0s(),t.j41(16,"p",22),t.EFF(17),t.k0s(),t.j41(18,"p",23),t.EFF(19),t.k0s()()(),t.j41(20,"div",18)(21,"div",19)(22,"h6",20),t.nrm(23,"i",24),t.EFF(24," Date & Time "),t.k0s(),t.j41(25,"p",22),t.EFF(26),t.k0s(),t.j41(27,"p",23),t.EFF(28),t.k0s()()()(),t.DNE(29,Et,27,7,"form",25),t.DNE(30,Mt,36,14,"div",26),t.j41(31,"div",27)(32,"button",28),t.nrm(33,"i",29),t.EFF(34," Back to Appointments "),t.k0s(),t.DNE(35,Nt,8,8,"div",26),t.k0s()()()),2&n){const e=t.XpG();t.R7$(5),t.Y8G("ngClass",e.getStatusBadgeClass(e.appointment.status)),t.R7$(1),t.SpI(" ",e.getStatusDisplayName(e.appointment.status)," "),t.R7$(2),t.Y8G("ngIf",e.success),t.R7$(1),t.Y8G("ngIf",e.error),t.R7$(6),t.SpI(" ",e.isDoctor()?"Patient":"Doctor"," "),t.R7$(2),t.JRh(e.getOtherParty()),t.R7$(2),t.JRh(e.getOtherPartyDetails()),t.R7$(7),t.JRh(e.formatDate(e.appointment.date)),t.R7$(2),t.Lme(" ",e.formatTime(e.appointment.startTime)," - ",e.formatTime(e.appointment.endTime)," "),t.R7$(1),t.Y8G("ngIf",e.isEditing),t.R7$(1),t.Y8G("ngIf",!e.isEditing),t.R7$(5),t.Y8G("ngIf",!e.isEditing)}}let wt=(()=>{class n{constructor(e,o,i,a,l){this.route=e,this.router=o,this.fb=i,this.appointmentService=a,this.authService=l,this.appointment=null,this.currentUser=null,this.loading=!1,this.updating=!1,this.error=null,this.success=null,this.isEditing=!1,this.statusOptions=[{value:c.y.PENDING,label:"Pending"},{value:c.y.SCHEDULED,label:"Scheduled"},{value:c.y.CONFIRMED,label:"Confirmed"},{value:c.y.COMPLETED,label:"Completed"},{value:c.y.CANCELLED,label:"Cancelled"}],this.typeOptions=[{value:c.Y.IN_PERSON,label:"In Person"},{value:c.Y.VIDEO_CALL,label:"Video Call"}],this.editForm=this.fb.group({status:["",s.k0.required],type:["",s.k0.required],reasonForVisit:["",s.k0.required],notes:[""],meetingLink:[""]})}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.loadAppointment()}loadAppointment(){const e=this.route.snapshot.paramMap.get("id");e?(this.loading=!0,this.appointmentService.getAppointment(parseInt(e)).subscribe({next:o=>{this.appointment=o,this.initializeForm(),this.loading=!1},error:o=>{this.error="Failed to load appointment details.",this.loading=!1,console.error("Error loading appointment:",o)}})):this.router.navigate(["/appointments"])}initializeForm(){this.appointment&&this.editForm.patchValue({status:this.appointment.status,type:this.appointment.type,reasonForVisit:this.appointment.reasonForVisit,notes:this.appointment.notes||"",meetingLink:this.appointment.meetingLink||""})}toggleEdit(){this.isEditing=!this.isEditing,this.isEditing||this.initializeForm()}onUpdate(){this.editForm.valid&&this.appointment&&(this.updating=!0,this.error=null,this.success=null,this.appointmentService.updateAppointment(this.appointment.id,{status:this.editForm.value.status,type:this.editForm.value.type,reasonForVisit:this.editForm.value.reasonForVisit,notes:this.editForm.value.notes||void 0,meetingLink:this.editForm.value.meetingLink||void 0}).subscribe({next:o=>{this.appointment=o,this.success="Appointment updated successfully!",this.isEditing=!1,this.updating=!1},error:o=>{this.error="Failed to update appointment. Please try again.",this.updating=!1,console.error("Error updating appointment:",o)}}))}onCancel(){this.appointment&&confirm("Are you sure you want to cancel this appointment?")&&this.appointmentService.cancelAppointment(this.appointment.id).subscribe({next:()=>{this.router.navigate(["/appointments"])},error:e=>{this.error="Failed to cancel appointment. Please try again.",console.error("Error canceling appointment:",e)}})}getStatusDisplayName(e){return this.appointmentService.getStatusDisplayName(e)}getTypeDisplayName(e){return this.appointmentService.getTypeDisplayName(e)}isBeforeAppointment(){return!!this.appointment&&new Date(`${this.appointment.date}T${this.appointment.startTime}`)>new Date}isAfterAppointment(){return!!this.appointment&&new Date(`${this.appointment.date}T${this.appointment.endTime}`)<new Date}getStatusBadgeClass(e){return this.appointmentService.getStatusBadgeClass(e)}formatDate(e){return new Date(e).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}formatTime(e){const[o,i]=e.split(":"),a=parseInt(o);return`${a%12||12}:${i} ${a>=12?"PM":"AM"}`}isDoctor(){return"DOCTOR"===this.currentUser?.role}isPatient(){return"PATIENT"===this.currentUser?.role}canEdit(){return!!this.appointment&&this.appointment.status!==c.y.COMPLETED&&this.appointment.status!==c.y.CANCELLED}canCancel(){return!!this.appointment&&(this.appointment.status===c.y.PENDING||this.appointment.status===c.y.SCHEDULED||this.appointment.status===c.y.CONFIRMED)}getOtherParty(){return this.appointment?this.isDoctor()?this.appointment.patient.fullName:this.appointment.doctor.fullName:""}getOtherPartyDetails(){if(!this.appointment)return"";if(this.isDoctor())return this.appointment.patient.email;{const e=this.appointment.doctor;return`${e.specialization||"General Practice"} \u2022 ${e.affiliation||"Private Practice"}`}}static{this.\u0275fac=function(o){return new(o||n)(t.rXU(d.nX),t.rXU(d.Ix),t.rXU(s.ok),t.rXU(_.h),t.rXU(v.u))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-appointment-details"]],decls:5,vars:2,consts:[[1,"container-fluid","py-4"],[1,"row","justify-content-center"],[1,"col-lg-8"],["class","text-center py-5",4,"ngIf"],["class","card",4,"ngIf"],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2","text-muted"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"card-title","mb-0"],[1,"fas","fa-calendar-alt","me-2"],[1,"badge","badge-lg",3,"ngClass"],[1,"card-body"],["class","alert alert-success","role","alert",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],[1,"row","mb-4"],[1,"col-md-6"],[1,"info-card"],[1,"info-title"],[1,"fas","fa-user","me-2"],[1,"info-value"],[1,"info-subtitle"],[1,"fas","fa-calendar","me-2"],[3,"formGroup","ngSubmit",4,"ngIf"],[4,"ngIf"],[1,"d-flex","justify-content-between","mt-4"],["routerLink","/appointments",1,"btn","btn-outline-secondary"],[1,"fas","fa-arrow-left","me-2"],["role","alert",1,"alert","alert-success"],[1,"fas","fa-check-circle","me-2"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[3,"formGroup","ngSubmit"],[1,"row","mb-3"],["for","status",1,"form-label"],["id","status","formControlName","status",1,"form-select"],[3,"value",4,"ngFor","ngForOf"],["for","type",1,"form-label"],["id","type","formControlName","type",1,"form-select"],[1,"mb-3"],["for","reasonForVisit",1,"form-label"],["type","text","id","reasonForVisit","formControlName","reasonForVisit",1,"form-control"],["for","notes",1,"form-label"],["id","notes","formControlName","notes","rows","3",1,"form-control"],["class","mb-3",4,"ngIf"],[1,"d-flex","justify-content-end"],["type","button",1,"btn","btn-outline-secondary","me-2",3,"click"],["type","submit",1,"btn","btn-primary",3,"disabled"],["class","spinner-border spinner-border-sm me-2","role","status",4,"ngIf"],[3,"value"],["for","meetingLink",1,"form-label"],["type","url","id","meetingLink","formControlName","meetingLink","placeholder","https://...",1,"form-control"],["role","status",1,"spinner-border","spinner-border-sm","me-2"],["class","fas fa-video me-2",4,"ngIf"],["class","fas fa-user-friends me-2",4,"ngIf"],[1,"fas","fa-notes-medical","me-2"],["class","mb-4",4,"ngIf"],[1,"row"],[1,"fas","fa-clock","me-2"],[1,"fas","fa-edit","me-2"],[1,"fas","fa-video","me-2"],[1,"fas","fa-user-friends","me-2"],[1,"mb-4"],[1,"fas","fa-sticky-note","me-2"],["target","_blank",1,"btn","btn-primary",3,"href"],[1,"fas","fa-external-link-alt","me-2"],["role","group",1,"btn-group","me-3"],[3,"config",4,"ngIf"],[3,"config"],["role","group",1,"btn-group"],["class","btn btn-outline-primary me-2",3,"click",4,"ngIf"],["class","btn btn-outline-danger",3,"click",4,"ngIf"],[1,"btn","btn-outline-primary","me-2",3,"click"],[1,"btn","btn-outline-danger",3,"click"],[1,"fas","fa-times","me-2"]],template:function(o,i){1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2),t.DNE(3,bt,6,0,"div",3),t.DNE(4,Yt,36,13,"div",4),t.k0s()()()),2&o&&(t.R7$(3),t.Y8G("ngIf",i.loading),t.R7$(1),t.Y8G("ngIf",!i.loading&&i.appointment))},dependencies:[p.YU,p.Sq,p.bT,s.qT,s.xH,s.y7,s.me,s.wz,s.BC,s.cb,s.j4,s.JD,d.Wk,F.Q,p.vh],styles:[".info-card[_ngcontent-%COMP%]{background:#f8f9fc;border-radius:.5rem;padding:1.5rem;margin-bottom:1rem;border-left:4px solid #667eea}.info-title[_ngcontent-%COMP%]{color:#5a5c69;font-weight:600;margin-bottom:.5rem;font-size:.9rem}.info-value[_ngcontent-%COMP%]{color:#3a3b45;font-weight:500;margin-bottom:.25rem;font-size:1.1rem}.info-subtitle[_ngcontent-%COMP%]{color:#858796;font-size:.9rem;margin-bottom:0}.badge-lg[_ngcontent-%COMP%]{font-size:.9rem;padding:.5rem 1rem}.badge-warning[_ngcontent-%COMP%]{background-color:#f6c23e;color:#1a1a1a}.badge-info[_ngcontent-%COMP%]{background-color:#36b9cc}.badge-primary[_ngcontent-%COMP%]{background-color:#4e73df}.badge-success[_ngcontent-%COMP%]{background-color:#1cc88a}.badge-danger[_ngcontent-%COMP%]{background-color:#e74a3b}.badge-secondary[_ngcontent-%COMP%]{background-color:#858796}.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus{border-color:#667eea;box-shadow:0 0 0 .2rem #667eea40}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border:none;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#5a6fd8 0%,#6a4190 100%);transform:translateY(-1px)}.btn-outline-primary[_ngcontent-%COMP%]{border-color:#667eea;color:#667eea}.btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#667eea;border-color:#667eea}.btn-outline-danger[_ngcontent-%COMP%]{border-color:#e74a3b;color:#e74a3b}.btn-outline-danger[_ngcontent-%COMP%]:hover{background-color:#e74a3b;border-color:#e74a3b}.btn-outline-secondary[_ngcontent-%COMP%]{border-color:#858796;color:#858796}.btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#858796;border-color:#858796}.card[_ngcontent-%COMP%]{border:1px solid #e3e6f0;box-shadow:0 .15rem 1.75rem #3a3b4526}.card-title[_ngcontent-%COMP%]{color:#5a5c69;font-weight:600}.alert-success[_ngcontent-%COMP%]{border-left:4px solid #28a745}.alert-danger[_ngcontent-%COMP%]{border-left:4px solid #e74a3b}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}"]})}}return n})();function Lt(n,r){1&n&&(t.j41(0,"button",26),t.nrm(1,"i",27),t.EFF(2," Book Appointment "),t.k0s())}function Vt(n,r){if(1&n&&(t.j41(0,"div",28),t.nrm(1,"i",29),t.EFF(2),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function Xt(n,r){1&n&&(t.j41(0,"div",30)(1,"div",31)(2,"span",32),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",33),t.EFF(5,"Loading calendar..."),t.k0s()())}function Bt(n,r){if(1&n&&(t.j41(0,"div",39),t.EFF(1),t.k0s()),2&n){const e=r.$implicit;t.R7$(1),t.SpI(" ",e," ")}}function Ut(n,r){if(1&n&&(t.j41(0,"div",47)(1,"span",48),t.EFF(2),t.k0s(),t.j41(3,"span",49),t.EFF(4),t.k0s()()),2&n){const e=r.$implicit,o=t.XpG(4);t.Y8G("ngClass",o.getStatusBadgeClass(e.status)),t.R7$(2),t.SpI(" ",o.formatTime(e.startTime)," "),t.R7$(2),t.SpI(" ",o.isDoctor()?e.patient.fullName:e.doctor.fullName," ")}}function zt(n,r){if(1&n&&(t.j41(0,"div",50),t.EFF(1),t.k0s()),2&n){const e=t.XpG(2).$implicit;t.R7$(1),t.SpI(" +",e.appointments.length-2," more ")}}function Jt(n,r){if(1&n&&(t.j41(0,"div",44),t.DNE(1,Ut,5,3,"div",45),t.DNE(2,zt,2,1,"div",46),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(1),t.Y8G("ngForOf",e.appointments.slice(0,2)),t.R7$(1),t.Y8G("ngIf",e.appointments.length>2)}}function Ht(n,r){1&n&&(t.j41(0,"div",51),t.nrm(1,"i",52),t.k0s())}const qt=function(n,r,e,o){return{"other-month":n,today:r,"has-appointments":e,clickable:o}};function Wt(n,r){if(1&n){const e=t.RV6();t.j41(0,"div",40),t.bIt("click",function(){const a=t.eBV(e).$implicit,l=t.XpG(2);return t.Njj(l.onDayClick(a))}),t.j41(1,"div",41),t.EFF(2),t.k0s(),t.DNE(3,Jt,3,2,"div",42),t.DNE(4,Ht,2,0,"div",43),t.k0s()}if(2&n){const e=r.$implicit,o=t.XpG(2);t.Y8G("ngClass",t.ziG(4,qt,!e.isCurrentMonth,e.isToday,e.appointments.length>0,e.isCurrentMonth)),t.R7$(2),t.SpI(" ",e.date.getDate()," "),t.R7$(1),t.Y8G("ngIf",e.appointments.length>0),t.R7$(1),t.Y8G("ngIf",0===e.appointments.length&&e.isCurrentMonth&&e.date>=o.currentDate)}}function Qt(n,r){if(1&n&&(t.j41(0,"div",34)(1,"div",35),t.DNE(2,Bt,2,1,"div",36),t.k0s(),t.j41(3,"div",37),t.DNE(4,Wt,5,9,"div",38),t.k0s()()),2&n){const e=t.XpG();t.R7$(2),t.Y8G("ngForOf",e.dayNames),t.R7$(2),t.Y8G("ngForOf",e.calendarDays)}}const Zt=[{path:"",canActivate:[C.q],children:[{path:"",redirectTo:"list",pathMatch:"full"},{path:"list",component:X},{path:"calendar",component:(()=>{class n{constructor(e,o,i){this.appointmentService=e,this.authService=o,this.router=i,this.currentDate=new Date,this.currentMonth=new Date,this.calendarDays=[],this.appointments=[],this.currentUser=null,this.loading=!1,this.error=null,this.monthNames=["January","February","March","April","May","June","July","August","September","October","November","December"],this.dayNames=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.loadAppointments(),this.generateCalendar()}loadAppointments(){this.loading=!0,this.error=null;const e=new Date(this.currentMonth.getFullYear(),this.currentMonth.getMonth(),1),o=new Date(this.currentMonth.getFullYear(),this.currentMonth.getMonth()+1,0);this.appointmentService.getAppointments(void 0,void 0,e.toISOString().split("T")[0],o.toISOString().split("T")[0]).subscribe({next:i=>{this.appointments=i,this.generateCalendar(),this.loading=!1},error:i=>{this.error="Failed to load appointments.",this.loading=!1,console.error("Error loading appointments:",i)}})}generateCalendar(){const e=this.currentMonth.getFullYear(),o=this.currentMonth.getMonth(),i=new Date(e,o,1),a=new Date(e,o+1,0),l=new Date(i);l.setDate(l.getDate()-l.getDay());const u=new Date(a);u.setDate(u.getDate()+(6-u.getDay())),this.calendarDays=[];const g=new Date(l);for(;g<=u;){const f=this.getAppointmentsForDate(g);this.calendarDays.push({date:new Date(g),isCurrentMonth:g.getMonth()===o,isToday:this.isSameDay(g,new Date),appointments:f}),g.setDate(g.getDate()+1)}}getAppointmentsForDate(e){const o=e.toISOString().split("T")[0];return this.appointments.filter(i=>i.date===o)}isSameDay(e,o){return e.getDate()===o.getDate()&&e.getMonth()===o.getMonth()&&e.getFullYear()===o.getFullYear()}previousMonth(){this.currentMonth=new Date(this.currentMonth.getFullYear(),this.currentMonth.getMonth()-1,1),this.loadAppointments()}nextMonth(){this.currentMonth=new Date(this.currentMonth.getFullYear(),this.currentMonth.getMonth()+1,1),this.loadAppointments()}goToToday(){this.currentMonth=new Date,this.loadAppointments()}onDayClick(e){e.appointments.length>0?this.router.navigate(["/appointments",e.appointments[0].id]):e.isCurrentMonth&&e.date>=new Date&&this.router.navigate(["/appointments/book"],{queryParams:{date:e.date.toISOString().split("T")[0]}})}getStatusDisplayName(e){return this.appointmentService.getStatusDisplayName(e)}getStatusBadgeClass(e){return this.appointmentService.getStatusBadgeClass(e)}formatTime(e){const[o,i]=e.split(":"),a=parseInt(o);return`${a%12||12}:${i} ${a>=12?"PM":"AM"}`}getCurrentMonthYear(){return`${this.monthNames[this.currentMonth.getMonth()]} ${this.currentMonth.getFullYear()}`}isDoctor(){return"DOCTOR"===this.currentUser?.role}isPatient(){return"PATIENT"===this.currentUser?.role}static{this.\u0275fac=function(o){return new(o||n)(t.rXU(_.h),t.rXU(v.u),t.rXU(d.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-appointment-calendar"]],decls:43,vars:5,consts:[[1,"container-fluid","py-4"],[1,"row"],[1,"col-12"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"card-title","mb-0"],[1,"fas","fa-calendar","me-2"],["class","btn btn-primary","routerLink","/appointments/book",4,"ngIf"],[1,"card-body"],["class","alert alert-danger","role","alert",4,"ngIf"],[1,"calendar-nav","d-flex","justify-content-between","align-items-center","mb-4"],[1,"btn","btn-outline-primary",3,"click"],[1,"fas","fa-chevron-left"],[1,"d-flex","align-items-center"],[1,"mb-0","me-3"],[1,"btn","btn-sm","btn-outline-secondary",3,"click"],[1,"fas","fa-chevron-right"],["class","text-center py-4",4,"ngIf"],["class","calendar-grid",4,"ngIf"],[1,"calendar-legend","mt-4"],[1,"d-flex","flex-wrap","gap-3"],[1,"legend-item"],[1,"legend-color","badge-primary"],[1,"legend-color","badge-warning"],[1,"legend-color","badge-success"],[1,"legend-color","badge-danger"],["routerLink","/appointments/book",1,"btn","btn-primary"],[1,"fas","fa-plus","me-2"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2","text-muted"],[1,"calendar-grid"],[1,"calendar-header"],["class","day-header",4,"ngFor","ngForOf"],[1,"calendar-body"],["class","calendar-day",3,"ngClass","click",4,"ngFor","ngForOf"],[1,"day-header"],[1,"calendar-day",3,"ngClass","click"],[1,"day-number"],["class","appointments-container",4,"ngIf"],["class","add-appointment",4,"ngIf"],[1,"appointments-container"],["class","appointment-item",3,"ngClass",4,"ngFor","ngForOf"],["class","more-appointments",4,"ngIf"],[1,"appointment-item",3,"ngClass"],[1,"appointment-time"],[1,"appointment-title"],[1,"more-appointments"],[1,"add-appointment"],[1,"fas","fa-plus"]],template:function(o,i){1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"h4",5),t.nrm(6,"i",6),t.EFF(7,"Appointment Calendar "),t.k0s(),t.DNE(8,Lt,3,0,"button",7),t.k0s(),t.j41(9,"div",8),t.DNE(10,Vt,3,1,"div",9),t.j41(11,"div",10)(12,"button",11),t.bIt("click",function(){return i.previousMonth()}),t.nrm(13,"i",12),t.k0s(),t.j41(14,"div",13)(15,"h5",14),t.EFF(16),t.k0s(),t.j41(17,"button",15),t.bIt("click",function(){return i.goToToday()}),t.EFF(18," Today "),t.k0s()(),t.j41(19,"button",11),t.bIt("click",function(){return i.nextMonth()}),t.nrm(20,"i",16),t.k0s()(),t.DNE(21,Xt,6,0,"div",17),t.DNE(22,Qt,5,2,"div",18),t.j41(23,"div",19)(24,"h6"),t.EFF(25,"Legend:"),t.k0s(),t.j41(26,"div",20)(27,"div",21),t.nrm(28,"span",22),t.j41(29,"span"),t.EFF(30,"Confirmed"),t.k0s()(),t.j41(31,"div",21),t.nrm(32,"span",23),t.j41(33,"span"),t.EFF(34,"Pending"),t.k0s()(),t.j41(35,"div",21),t.nrm(36,"span",24),t.j41(37,"span"),t.EFF(38,"Completed"),t.k0s()(),t.j41(39,"div",21),t.nrm(40,"span",25),t.j41(41,"span"),t.EFF(42,"Cancelled"),t.k0s()()()()()()()()()),2&o&&(t.R7$(8),t.Y8G("ngIf",i.isPatient()),t.R7$(2),t.Y8G("ngIf",i.error),t.R7$(6),t.JRh(i.getCurrentMonthYear()),t.R7$(5),t.Y8G("ngIf",i.loading),t.R7$(1),t.Y8G("ngIf",!i.loading))},dependencies:[p.YU,p.Sq,p.bT,d.Wk],styles:[".calendar-grid[_ngcontent-%COMP%]{border:1px solid #e3e6f0;border-radius:.5rem;overflow:hidden}.calendar-header[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(7,1fr);background:#f8f9fc;border-bottom:1px solid #e3e6f0}.day-header[_ngcontent-%COMP%]{padding:1rem;text-align:center;font-weight:600;color:#5a5c69;border-right:1px solid #e3e6f0}.day-header[_ngcontent-%COMP%]:last-child{border-right:none}.calendar-body[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(7,1fr)}.calendar-day[_ngcontent-%COMP%]{min-height:120px;padding:.5rem;border-right:1px solid #e3e6f0;border-bottom:1px solid #e3e6f0;position:relative;background:white;transition:background-color .2s ease}.calendar-day[_ngcontent-%COMP%]:nth-child(7n){border-right:none}.calendar-day.clickable[_ngcontent-%COMP%]{cursor:pointer}.calendar-day.clickable[_ngcontent-%COMP%]:hover{background:#f8f9fc}.calendar-day.other-month[_ngcontent-%COMP%]{background:#f8f9fc;color:#858796}.calendar-day.today[_ngcontent-%COMP%]{background:#e3f2fd;border:2px solid #667eea}.calendar-day.has-appointments[_ngcontent-%COMP%]{background:#fff3cd}.day-number[_ngcontent-%COMP%]{font-weight:600;margin-bottom:.5rem;color:#5a5c69}.appointments-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.appointment-item[_ngcontent-%COMP%]{padding:.25rem .5rem;border-radius:.25rem;font-size:.75rem;color:#fff;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.appointment-time[_ngcontent-%COMP%]{font-weight:600;display:block}.appointment-title[_ngcontent-%COMP%]{display:block;opacity:.9}.more-appointments[_ngcontent-%COMP%]{font-size:.7rem;color:#858796;text-align:center;margin-top:.25rem}.add-appointment[_ngcontent-%COMP%]{position:absolute;bottom:.5rem;right:.5rem;color:#858796;font-size:.8rem;opacity:0;transition:opacity .2s ease}.calendar-day.clickable[_ngcontent-%COMP%]:hover   .add-appointment[_ngcontent-%COMP%]{opacity:1}.badge-primary[_ngcontent-%COMP%]{background-color:#4e73df}.badge-warning[_ngcontent-%COMP%]{background-color:#f6c23e;color:#1a1a1a}.badge-success[_ngcontent-%COMP%]{background-color:#1cc88a}.badge-danger[_ngcontent-%COMP%]{background-color:#e74a3b}.badge-info[_ngcontent-%COMP%]{background-color:#36b9cc}.badge-secondary[_ngcontent-%COMP%]{background-color:#858796}.calendar-legend[_ngcontent-%COMP%]{border-top:1px solid #e3e6f0;padding-top:1rem}.legend-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.9rem}.legend-color[_ngcontent-%COMP%]{width:16px;height:16px;border-radius:.25rem;display:inline-block}.calendar-nav[_ngcontent-%COMP%]{background:#f8f9fc;padding:1rem;border-radius:.5rem;border:1px solid #e3e6f0}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border:none;transition:all .3s ease}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#5a6fd8 0%,#6a4190 100%);transform:translateY(-1px)}.btn-outline-primary[_ngcontent-%COMP%]{border-color:#667eea;color:#667eea}.btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#667eea;border-color:#667eea}.card[_ngcontent-%COMP%]{border:1px solid #e3e6f0;box-shadow:0 .15rem 1.75rem #3a3b4526}.alert-danger[_ngcontent-%COMP%]{border-left:4px solid #e74a3b}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}"]})}}return n})()},{path:"book",component:st},{path:"doctors",component:ht},{path:":id",component:wt}]}];let te=(()=>{class n{static{this.\u0275fac=function(o){return new(o||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[d.iI.forChild(Zt),d.iI]})}}return n})();var ee=m(3887);let ne=(()=>{class n{static{this.\u0275fac=function(o){return new(o||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[p.MD,s.X1,s.YN,d.iI,te,ee.G]})}}return n})()}}]);