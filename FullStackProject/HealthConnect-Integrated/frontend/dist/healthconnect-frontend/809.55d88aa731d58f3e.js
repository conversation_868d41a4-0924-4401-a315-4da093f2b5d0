"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[809],{5809:(P,d,s)=>{s.r(d),s.d(d,{ProfileModule:()=>N});var c=s(2434),m=s(3887),o=s(4341),e=s(540),f=s(8010),u=s(3443),a=s(177);function p(i,l){if(1&i){const r=e.RV6();e.j41(0,"button",32),e.bIt("click",function(){e.eBV(r);const t=e.XpG();return e.Njj(t.toggleEdit())}),e.nrm(1,"i",33),e.EFF(2,"Edit Profile "),e.k0s()}}function b(i,l){if(1&i&&(e.j41(0,"div",34),e.nrm(1,"i",35),e.<PERSON><PERSON>(2),e.k0s()),2&i){const r=e.XpG();e.R7$(2),e.SpI(" ",r.successMessage," ")}}function g(i,l){if(1&i&&(e.j41(0,"div",36),e.nrm(1,"i",37),e.EFF(2),e.k0s()),2&i){const r=e.XpG();e.R7$(2),e.SpI(" ",r.errorMessage," ")}}function h(i,l){if(1&i&&(e.j41(0,"div",38),e.EFF(1),e.k0s()),2&i){const r=e.XpG();e.R7$(1),e.SpI(" ",r.getFieldError("fullName")," ")}}function v(i,l){if(1&i&&(e.j41(0,"div",12)(1,"div",13)(2,"h6",14),e.nrm(3,"i",39),e.EFF(4,"Professional Information "),e.k0s()(),e.j41(5,"div",16)(6,"label",40),e.EFF(7,"Specialization"),e.k0s(),e.nrm(8,"input",41),e.k0s(),e.j41(9,"div",16)(10,"label",42),e.EFF(11,"Hospital/Clinic"),e.k0s(),e.nrm(12,"input",43),e.k0s(),e.j41(13,"div",16)(14,"label",44),e.EFF(15,"Years of Experience"),e.k0s(),e.nrm(16,"input",45),e.k0s(),e.j41(17,"div",16)(18,"label",29),e.EFF(19,"License Number"),e.k0s(),e.nrm(20,"input",30),e.j41(21,"small",22),e.EFF(22,"License number cannot be changed"),e.k0s()()()),2&i){const r=e.XpG();e.R7$(8),e.Y8G("readonly",!r.isEditing),e.R7$(4),e.Y8G("readonly",!r.isEditing),e.R7$(4),e.Y8G("readonly",!r.isEditing),e.R7$(4),e.Y8G("value",null==r.currentUser?null:r.currentUser.licenseNumber)}}function F(i,l){1&i&&e.nrm(0,"span",51)}function E(i,l){1&i&&(e.j41(0,"span"),e.EFF(1,"Saving..."),e.k0s())}function y(i,l){1&i&&(e.j41(0,"span"),e.EFF(1,"Save Changes"),e.k0s())}function k(i,l){if(1&i){const r=e.RV6();e.j41(0,"div",46)(1,"button",47),e.DNE(2,F,1,0,"span",48),e.DNE(3,E,2,0,"span",49),e.DNE(4,y,2,0,"span",49),e.k0s(),e.j41(5,"button",50),e.bIt("click",function(){e.eBV(r);const t=e.XpG();return e.Njj(t.toggleEdit())}),e.EFF(6," Cancel "),e.k0s()()}if(2&i){const r=e.XpG();e.R7$(1),e.Y8G("disabled",r.isLoading||r.profileForm.invalid),e.R7$(1),e.Y8G("ngIf",r.isLoading),e.R7$(1),e.Y8G("ngIf",r.isLoading),e.R7$(1),e.Y8G("ngIf",!r.isLoading),e.R7$(1),e.Y8G("disabled",r.isLoading)}}const C=[{path:"",component:(()=>{class i{constructor(r,n,t){this.formBuilder=r,this.authService=n,this.userService=t,this.currentUser=null,this.isLoading=!1,this.isEditing=!1,this.successMessage="",this.errorMessage=""}ngOnInit(){this.initializeForm(),this.loadUserData()}initializeForm(){this.profileForm=this.formBuilder.group({fullName:["",[o.k0.required,o.k0.minLength(2)]],email:[{value:"",disabled:!0}],phoneNumber:[""],address:[""],specialization:[""],affiliation:[""],yearsOfExperience:[""]})}loadUserData(){this.authService.currentUser$.subscribe(r=>{r&&(this.currentUser=r,this.populateForm(r))})}populateForm(r){this.profileForm.patchValue({fullName:r.fullName,email:r.email,phoneNumber:r.phoneNumber||"",address:r.address||"",specialization:r.specialization||"",affiliation:r.affiliation||"",yearsOfExperience:r.yearsOfExperience||""})}toggleEdit(){this.isEditing=!this.isEditing,this.successMessage="",this.errorMessage="",this.isEditing||this.currentUser&&this.populateForm(this.currentUser)}onSubmit(){if(this.profileForm.invalid)return void this.markFormGroupTouched();this.isLoading=!0,this.successMessage="",this.errorMessage="";const r={fullName:this.profileForm.get("fullName")?.value,phoneNumber:this.profileForm.get("phoneNumber")?.value,address:this.profileForm.get("address")?.value};"DOCTOR"===this.currentUser?.role&&(r.specialization=this.profileForm.get("specialization")?.value,r.affiliation=this.profileForm.get("affiliation")?.value,r.yearsOfExperience=this.profileForm.get("yearsOfExperience")?.value),this.userService.updateProfile(r).subscribe({next:n=>{this.isLoading=!1,this.isEditing=!1,this.successMessage="Profile updated successfully!",this.currentUser=n},error:n=>{this.isLoading=!1,this.errorMessage=n.message||"Failed to update profile. Please try again."}})}markFormGroupTouched(){Object.keys(this.profileForm.controls).forEach(r=>{this.profileForm.get(r)?.markAsTouched()})}isFieldInvalid(r){const n=this.profileForm.get(r);return!(!n||!n.invalid||!n.dirty&&!n.touched)}getFieldError(r){const n=this.profileForm.get(r);if(n?.errors){if(n.errors.required)return`${r.charAt(0).toUpperCase()+r.slice(1)} is required`;if(n.errors.minlength)return`${r.charAt(0).toUpperCase()+r.slice(1)} must be at least ${n.errors.minlength.requiredLength} characters`}return""}static{this.\u0275fac=function(n){return new(n||i)(e.rXU(o.ok),e.rXU(f.u),e.rXU(u.D))}}static{this.\u0275cmp=e.VBU({type:i,selectors:[["app-profile"]],decls:54,vars:19,consts:[[1,"container","py-4"],[1,"row","justify-content-center"],[1,"col-md-8"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"bi","bi-person-gear","me-2"],["class","btn btn-outline-primary btn-sm",3,"click",4,"ngIf"],[1,"card-body"],["class","alert alert-success","role","alert",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],[3,"formGroup","ngSubmit"],[1,"row","mb-4"],[1,"col-12"],[1,"text-primary","mb-3"],[1,"bi","bi-person","me-2"],[1,"col-md-6","mb-3"],["for","fullName",1,"form-label"],["type","text","id","fullName","formControlName","fullName",1,"form-control",3,"readonly"],["class","invalid-feedback",4,"ngIf"],["for","email",1,"form-label"],["type","email","id","email","formControlName","email","readonly","",1,"form-control"],[1,"text-muted"],["for","phoneNumber",1,"form-label"],["type","tel","id","phoneNumber","formControlName","phoneNumber","placeholder","Enter your phone number",1,"form-control",3,"readonly"],["for","address",1,"form-label"],["type","text","id","address","formControlName","address","placeholder","Enter your address",1,"form-control",3,"readonly"],["class","row mb-4",4,"ngIf"],[1,"bi","bi-shield-check","me-2"],[1,"form-label"],["type","text","readonly","",1,"form-control",3,"value"],["class","d-flex gap-2",4,"ngIf"],[1,"btn","btn-outline-primary","btn-sm",3,"click"],[1,"bi","bi-pencil","me-1"],["role","alert",1,"alert","alert-success"],[1,"bi","bi-check-circle","me-2"],["role","alert",1,"alert","alert-danger"],[1,"bi","bi-exclamation-triangle","me-2"],[1,"invalid-feedback"],[1,"bi","bi-person-badge","me-2"],["for","specialization",1,"form-label"],["type","text","id","specialization","formControlName","specialization","placeholder","e.g., Cardiology",1,"form-control",3,"readonly"],["for","affiliation",1,"form-label"],["type","text","id","affiliation","formControlName","affiliation","placeholder","Your workplace",1,"form-control",3,"readonly"],["for","yearsOfExperience",1,"form-label"],["type","number","id","yearsOfExperience","formControlName","yearsOfExperience","placeholder","0","min","0","max","50",1,"form-control",3,"readonly"],[1,"d-flex","gap-2"],["type","submit",1,"btn","btn-primary",3,"disabled"],["class","spinner-border spinner-border-sm me-2","role","status",4,"ngIf"],[4,"ngIf"],["type","button",1,"btn","btn-outline-secondary",3,"disabled","click"],["role","status",1,"spinner-border","spinner-border-sm","me-2"]],template:function(n,t){1&n&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"h5",5),e.nrm(6,"i",6),e.EFF(7,"Profile Settings "),e.k0s(),e.DNE(8,p,3,0,"button",7),e.k0s(),e.j41(9,"div",8),e.DNE(10,b,3,1,"div",9),e.DNE(11,g,3,1,"div",10),e.j41(12,"form",11),e.bIt("ngSubmit",function(){return t.onSubmit()}),e.j41(13,"div",12)(14,"div",13)(15,"h6",14),e.nrm(16,"i",15),e.EFF(17,"Basic Information "),e.k0s()(),e.j41(18,"div",16)(19,"label",17),e.EFF(20,"Full Name *"),e.k0s(),e.nrm(21,"input",18),e.DNE(22,h,2,1,"div",19),e.k0s(),e.j41(23,"div",16)(24,"label",20),e.EFF(25,"Email Address"),e.k0s(),e.nrm(26,"input",21),e.j41(27,"small",22),e.EFF(28,"Email cannot be changed"),e.k0s()(),e.j41(29,"div",16)(30,"label",23),e.EFF(31,"Phone Number"),e.k0s(),e.nrm(32,"input",24),e.k0s(),e.j41(33,"div",16)(34,"label",25),e.EFF(35,"Address"),e.k0s(),e.nrm(36,"input",26),e.k0s()(),e.DNE(37,v,23,4,"div",27),e.j41(38,"div",12)(39,"div",13)(40,"h6",14),e.nrm(41,"i",28),e.EFF(42,"Account Information "),e.k0s()(),e.j41(43,"div",16)(44,"label",29),e.EFF(45,"Role"),e.k0s(),e.nrm(46,"input",30),e.nI1(47,"titlecase"),e.k0s(),e.j41(48,"div",16)(49,"label",29),e.EFF(50,"Member Since"),e.k0s(),e.nrm(51,"input",30),e.nI1(52,"date"),e.k0s()(),e.DNE(53,k,7,5,"div",31),e.k0s()()()()()()),2&n&&(e.R7$(8),e.Y8G("ngIf",!t.isEditing),e.R7$(2),e.Y8G("ngIf",t.successMessage),e.R7$(1),e.Y8G("ngIf",t.errorMessage),e.R7$(1),e.Y8G("formGroup",t.profileForm),e.R7$(9),e.AVh("is-invalid",t.isFieldInvalid("fullName")),e.Y8G("readonly",!t.isEditing),e.R7$(1),e.Y8G("ngIf",t.isFieldInvalid("fullName")),e.R7$(10),e.Y8G("readonly",!t.isEditing),e.R7$(4),e.Y8G("readonly",!t.isEditing),e.R7$(1),e.Y8G("ngIf","DOCTOR"===(null==t.currentUser?null:t.currentUser.role)),e.R7$(9),e.Y8G("value",e.bMT(47,14,null==t.currentUser?null:t.currentUser.role)),e.R7$(5),e.Y8G("value",e.i5U(52,16,null==t.currentUser?null:t.currentUser.createdAt,"mediumDate")),e.R7$(2),e.Y8G("ngIf",t.isEditing))},dependencies:[a.bT,o.qT,o.me,o.Q0,o.BC,o.cb,o.VZ,o.zX,o.j4,o.JD,a.PV,a.vh],styles:[".card[_ngcontent-%COMP%]{border:none;border-radius:12px;box-shadow:0 4px 6px #0000001a}.card-header[_ngcontent-%COMP%]{background-color:transparent;border-bottom:1px solid #e9ecef;font-weight:600}.form-control[readonly][_ngcontent-%COMP%]{background-color:#f8f9fa;border-color:#e9ecef}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0d6efd 0%,#0b5ed7 100%);border:none;font-weight:500}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#0b5ed7 0%,#0a58ca 100%);transform:translateY(-1px);box-shadow:0 4px 8px #0d6efd4d}.btn-outline-primary[_ngcontent-%COMP%]{border-color:#0d6efd;color:#0d6efd;font-weight:500}.btn-outline-secondary[_ngcontent-%COMP%]{font-weight:500}.alert[_ngcontent-%COMP%]{border:none;border-radius:8px}@media (max-width: 768px){.container[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.card-body[_ngcontent-%COMP%]{padding:1.5rem}}"]})}}return i})()}];let N=(()=>{class i{static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275mod=e.$C({type:i})}static{this.\u0275inj=e.G2t({imports:[m.G,c.iI.forChild(C)]})}}return i})()}}]);