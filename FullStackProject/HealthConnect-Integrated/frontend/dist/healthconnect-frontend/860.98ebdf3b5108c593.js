"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[860,693],{9339:(F,O,m)=>{m.d(O,{q:()=>M});var n=m(540),t=m(8211),C=m(8010),b=m(177);function _(r,h){1&r&&(n.j41(0,"div",8)(1,"div",9)(2,"span",10),n.EFF(3,"Loading..."),n.k0s()(),n.j41(4,"div",11),n.EFF(5,"Loading chats..."),n.k0s()())}function f(r,h){if(1&r&&(n.j41(0,"div",12),n.nrm(1,"i",13),n.j41(2,"p"),n.EFF(3,"No conversations yet"),n.k0s(),n.j41(4,"small"),n.<PERSON><PERSON>(5),n.k0s()()),2&r){const o=n.XpG();n.R7$(5),n.SpI("Start a conversation with a ","PATIENT"===(null==o.currentUser?null:o.currentUser.role)?"doctor":"patient","")}}function v(r,h){1&r&&n.nrm(0,"span",28)}function x(r,h){if(1&r&&(n.j41(0,"span",31),n.nrm(1,"i",32),n.k0s()),2&r){const o=n.XpG(2).$implicit;n.R7$(1),n.AVh("text-primary","READ"===o.lastMessage.status)("text-muted","READ"!==o.lastMessage.status)}}function y(r,h){if(1&r&&(n.j41(0,"p",29),n.DNE(1,x,2,4,"span",30),n.EFF(2),n.nI1(3,"slice"),n.k0s()),2&r){const o=n.XpG().$implicit,c=n.XpG(2);n.R7$(1),n.Y8G("ngIf",o.lastMessage.sender.id===(null==c.currentUser?null:c.currentUser.id)),n.R7$(1),n.Lme(" ",n.brH(3,3,o.lastMessage.content,0,50),"",o.lastMessage.content.length>50?"...":""," ")}}function T(r,h){1&r&&(n.j41(0,"p",29)(1,"em"),n.EFF(2,"No messages yet"),n.k0s()())}function l(r,h){if(1&r&&(n.j41(0,"span",33),n.EFF(1),n.k0s()),2&r){const o=n.XpG().$implicit;n.R7$(1),n.SpI(" ",o.unreadCount," ")}}function k(r,h){if(1&r){const o=n.RV6();n.j41(0,"div",16),n.bIt("click",function(){const P=n.eBV(o).$implicit,E=n.XpG(2);return n.Njj(E.selectChat(P))}),n.j41(1,"div",17),n.nrm(2,"img",18),n.DNE(3,v,1,0,"span",19),n.k0s(),n.j41(4,"div",20)(5,"div",21)(6,"h6",22),n.EFF(7),n.k0s(),n.j41(8,"small",23),n.EFF(9),n.k0s()(),n.j41(10,"div",24),n.DNE(11,y,4,7,"p",25),n.DNE(12,T,3,0,"p",25),n.k0s()(),n.j41(13,"div",26),n.DNE(14,l,2,1,"span",27),n.k0s()()}if(2&r){const o=h.$implicit,c=n.XpG(2);n.AVh("active",c.selectedChatId===o.id),n.R7$(2),n.Y8G("src",c.getOtherParticipant(o).avatar||"/assets/images/default-avatar.png",n.B4B)("alt",c.getOtherParticipant(o).fullName),n.R7$(1),n.Y8G("ngIf",!1),n.R7$(4),n.SpI(" ",c.getOtherParticipant(o).fullName," "),n.R7$(2),n.SpI(" ",c.formatLastMessageTime(o.lastMessage?o.lastMessage.createdAt:o.createdAt)," "),n.R7$(2),n.Y8G("ngIf",o.lastMessage),n.R7$(1),n.Y8G("ngIf",!o.lastMessage),n.R7$(2),n.Y8G("ngIf",o.unreadCount>0)}}function I(r,h){if(1&r&&(n.j41(0,"div",14),n.DNE(1,k,15,10,"div",15),n.k0s()),2&r){const o=n.XpG();n.R7$(1),n.Y8G("ngForOf",o.chats)}}let M=(()=>{class r{constructor(o,c){this.chatService=o,this.authService=c,this.chatSelected=new n.bkB,this.chats=[],this.selectedChatId=null,this.loading=!0,this.subscriptions=[]}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.loadChats(),this.subscribeToChats()}ngOnDestroy(){this.subscriptions.forEach(o=>o.unsubscribe())}loadChats(){this.chatService.loadUserChats()}subscribeToChats(){const o=this.chatService.chats$.subscribe({next:p=>{this.chats=p,this.loading=!1},error:p=>{console.error("Failed to load chats:",p),this.loading=!1}});this.subscriptions.push(o);const c=this.chatService.messages$.subscribe({next:p=>{this.updateChatWithNewMessage(p)}});this.subscriptions.push(c)}updateChatWithNewMessage(o){const c=this.chats.findIndex(p=>p.id===o.chatId);if(-1!==c){this.chats[c].lastMessage=o,this.chats[c].updatedAt=o.createdAt,o.sender.id!==this.currentUser?.id&&this.chats[c].unreadCount++;const p=this.chats.splice(c,1)[0];this.chats.unshift(p)}}selectChat(o){this.selectedChatId=o.id,this.chatSelected.emit(o),o.unreadCount>0&&this.chatService.markMessagesAsRead(o.id).subscribe({next:()=>{o.unreadCount=0},error:c=>{console.error("Failed to mark messages as read:",c)}})}getOtherParticipant(o){return"PATIENT"===this.currentUser?.role?o.doctor:o.patient}formatLastMessageTime(o){const c=new Date(o),P=((new Date).getTime()-c.getTime())/36e5;return P<1?"Just now":P<24?c.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):c.toLocaleDateString()}static{this.\u0275fac=function(c){return new(c||r)(n.rXU(t.m),n.rXU(C.u))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-chat-list"]],outputs:{chatSelected:"chatSelected"},decls:9,vars:3,consts:[[1,"chat-list"],[1,"chat-list-header"],[1,"mb-0"],[1,"bi","bi-chat-dots","me-2"],[1,"chat-list-body"],["class","text-center p-3",4,"ngIf"],["class","text-center p-4 text-muted",4,"ngIf"],["class","chat-items",4,"ngIf"],[1,"text-center","p-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"mt-2"],[1,"text-center","p-4","text-muted"],[1,"bi","bi-chat-square-text","fs-1","mb-3","d-block"],[1,"chat-items"],["class","chat-item",3,"active","click",4,"ngFor","ngForOf"],[1,"chat-item",3,"click"],[1,"chat-avatar"],[1,"avatar-img",3,"src","alt"],["class","online-indicator",4,"ngIf"],[1,"chat-content"],[1,"chat-header"],[1,"chat-name","mb-0"],[1,"chat-time","text-muted"],[1,"chat-preview"],["class","mb-0 text-muted",4,"ngIf"],[1,"chat-meta"],["class","badge bg-primary rounded-pill",4,"ngIf"],[1,"online-indicator"],[1,"mb-0","text-muted"],["class","me-1",4,"ngIf"],[1,"me-1"],[1,"bi","bi-check2-all"],[1,"badge","bg-primary","rounded-pill"]],template:function(c,p){1&c&&(n.j41(0,"div",0)(1,"div",1)(2,"h5",2),n.nrm(3,"i",3),n.EFF(4," Messages "),n.k0s()(),n.j41(5,"div",4),n.DNE(6,_,6,0,"div",5),n.DNE(7,f,6,1,"div",6),n.DNE(8,I,2,1,"div",7),n.k0s()()),2&c&&(n.R7$(6),n.Y8G("ngIf",p.loading),n.R7$(1),n.Y8G("ngIf",!p.loading&&0===p.chats.length),n.R7$(1),n.Y8G("ngIf",!p.loading&&p.chats.length>0))},dependencies:[b.Sq,b.bT,b.P9],styles:[".chat-list[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;border-right:1px solid #e9ecef}.chat-list-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid #e9ecef;background-color:#f8f9fa}.chat-list-body[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem 1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:background-color .2s ease}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.chat-items[_ngcontent-%COMP%]   .chat-item.active[_ngcontent-%COMP%]{background-color:#e3f2fd;border-left:3px solid #2196f3}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]{position:relative;margin-right:.75rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%]{width:45px;height:45px;border-radius:50%;object-fit:cover;border:2px solid #e9ecef}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:12px;height:12px;background-color:#4caf50;border:2px solid white;border-radius:50%}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]{flex:1;min-width:0}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.25rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-name[_ngcontent-%COMP%]{font-weight:600;color:#333;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-time[_ngcontent-%COMP%]{font-size:.75rem;white-space:nowrap}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-preview[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.3;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]{margin-left:.5rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;min-width:20px;height:20px;display:flex;align-items:center;justify-content:center}@media (max-width: 768px){.chat-list[_ngcontent-%COMP%]{border-right:none}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{padding:1rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%]{width:50px;height:50px}}"]})}}return r})()},4002:(F,O,m)=>{m.d(O,{E:()=>Y});var n=m(4341),t=m(540),C=m(8211),b=m(8010),_=m(177),f=m(4719);function v(i,d){if(1&i&&(t.j41(0,"span",7),t.nrm(1,"i",8),t.k0s()),2&i){const e=t.XpG();t.HbH(e.getStatusClass()),t.R7$(1),t.HbH(e.getStatusIcon())}}let x=(()=>{class i{constructor(){this.isOwn=!1}formatTime(e){return new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}getStatusIcon(){switch(this.message.status){case"SENT":return"bi-check";case"DELIVERED":return"bi-check2";case"READ":return"bi-check2-all";default:return"bi-clock"}}getStatusClass(){return"READ"===this.message.status?"text-primary":"text-muted"}static{this.\u0275fac=function(s){return new(s||i)}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-message-item"]],inputs:{message:"message",isOwn:"isOwn"},decls:9,vars:7,consts:[[1,"message-item"],[1,"message-content"],[1,"message-bubble"],[1,"message-text","mb-1"],[1,"message-meta"],[1,"message-time"],["class","message-status ms-1",3,"class",4,"ngIf"],[1,"message-status","ms-1"],[1,"bi"]],template:function(s,a){1&s&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"p",3),t.EFF(4),t.k0s(),t.j41(5,"div",4)(6,"small",5),t.EFF(7),t.k0s(),t.DNE(8,v,2,4,"span",6),t.k0s()()()()),2&s&&(t.AVh("own-message",a.isOwn)("other-message",!a.isOwn),t.R7$(4),t.JRh(a.message.content),t.R7$(3),t.JRh(a.formatTime(a.message.createdAt)),t.R7$(1),t.Y8G("ngIf",a.isOwn))},dependencies:[_.bT],styles:[".message-item[_ngcontent-%COMP%]{margin-bottom:.75rem;display:flex}.message-item.own-message[_ngcontent-%COMP%]{justify-content:flex-end}.message-item.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{background-color:#007bff;color:#fff;border-bottom-right-radius:.25rem}.message-item.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{color:#fffc}.message-item.other-message[_ngcontent-%COMP%]{justify-content:flex-start}.message-item.other-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{background-color:#fff;color:#333;border:1px solid #e9ecef;border-bottom-left-radius:.25rem}.message-item.other-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{color:#6c757d}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:70%}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{padding:.75rem 1rem;border-radius:1rem;word-wrap:break-word;box-shadow:0 1px 2px #0000001a}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]{line-height:1.4;margin:0}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;margin-top:.25rem}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%], .message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-status[_ngcontent-%COMP%]{font-size:.75rem}@media (max-width: 768px){.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:85%}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{padding:.5rem .75rem}}"]})}}return i})();var y=m(9545),T=m(2434);function l(i,d){1&i&&(t.j41(0,"div",5)(1,"div",6)(2,"span",7),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"span",8),t.EFF(5,"Loading appointment details..."),t.k0s()())}function k(i,d){if(1&i&&(t.j41(0,"div",9),t.nrm(1,"i",10),t.EFF(2),t.k0s()),2&i){const e=t.XpG(2);t.R7$(2),t.SpI(" ",e.error," ")}}function I(i,d){if(1&i&&(t.j41(0,"div",27),t.nrm(1,"i",38),t.j41(2,"span",39),t.EFF(3),t.k0s()()),2&i){const e=t.XpG(3);t.R7$(3),t.JRh(e.appointment.reasonForVisit)}}function M(i,d){if(1&i&&(t.j41(0,"div",40),t.nrm(1,"i",41),t.j41(2,"strong"),t.EFF(3,"Upcoming:"),t.k0s(),t.EFF(4),t.k0s()),2&i){const e=t.XpG(3);t.R7$(4),t.SpI(" ",e.getTimeUntilAppointment()," ")}}function r(i,d){1&i&&(t.j41(0,"div",42),t.nrm(1,"i",43),t.j41(2,"strong"),t.EFF(3,"Completed:"),t.k0s(),t.EFF(4," Follow-up discussion "),t.k0s())}function h(i,d){if(1&i){const e=t.RV6();t.j41(0,"button",44),t.bIt("click",function(){t.eBV(e);const a=t.XpG(3);return t.Njj(a.openMeetingLink(a.appointment.meetingLink))}),t.nrm(1,"i",45),t.EFF(2," Join Call "),t.k0s()}}function o(i,d){if(1&i){const e=t.RV6();t.j41(0,"div",11)(1,"div",12)(2,"div",13),t.nrm(3,"i"),t.j41(4,"h6",14),t.EFF(5),t.k0s()(),t.j41(6,"button",15),t.bIt("click",function(){t.eBV(e);const a=t.XpG(2);return t.Njj(a.navigateToAppointment())}),t.nrm(7,"i",16),t.k0s()(),t.j41(8,"div",17)(9,"div",18)(10,"div",19)(11,"div",20),t.nrm(12,"i",21),t.j41(13,"span",22),t.EFF(14),t.nI1(15,"date"),t.k0s()()(),t.j41(16,"div",19)(17,"div",20),t.nrm(18,"i",23),t.j41(19,"span",22),t.EFF(20),t.k0s()()(),t.j41(21,"div",19)(22,"div",20),t.nrm(23,"i",24),t.j41(24,"span",22),t.EFF(25),t.k0s()()(),t.j41(26,"div",19)(27,"div",20),t.nrm(28,"i",25),t.j41(29,"span",26),t.EFF(30),t.k0s()()()(),t.j41(31,"div",27),t.nrm(32,"i",28),t.j41(33,"span",22),t.EFF(34),t.k0s()(),t.DNE(35,I,4,1,"div",29),t.j41(36,"div",30),t.DNE(37,M,5,1,"div",31),t.DNE(38,r,5,0,"div",32),t.k0s()(),t.j41(39,"div",33)(40,"div",34),t.DNE(41,h,3,0,"button",35),t.j41(42,"button",36),t.bIt("click",function(){t.eBV(e);const a=t.XpG(2);return t.Njj(a.navigateToAppointment())}),t.nrm(43,"i",37),t.EFF(44," View Details "),t.k0s()()()()}if(2&i){const e=t.XpG(2);t.R7$(3),t.HbH(e.getContextIcon()+" me-2"),t.R7$(2),t.JRh(e.getContextTitle()),t.R7$(9),t.JRh(t.i5U(15,17,e.appointment.date,"mediumDate")),t.R7$(6),t.Lme("",e.appointment.startTime," - ",e.appointment.endTime,""),t.R7$(5),t.JRh(e.appointment.doctor.fullName),t.R7$(4),t.HbH("badge-"+e.appointment.status.toLowerCase()),t.R7$(1),t.SpI(" ",e.appointment.status," "),t.R7$(2),t.HbH("VIDEO_CALL"===e.appointment.type?"fa-video":"fa-user-friends"),t.R7$(2),t.SpI(" ","VIDEO_CALL"===e.appointment.type?"Video Call":"In-Person"," "),t.R7$(1),t.Y8G("ngIf",e.appointment.reasonForVisit),t.R7$(2),t.Y8G("ngIf",e.isBeforeAppointment()),t.R7$(1),t.Y8G("ngIf",e.isAfterAppointment()),t.R7$(3),t.Y8G("ngIf","VIDEO_CALL"===e.appointment.type&&e.appointment.meetingLink&&e.isBeforeAppointment())}}function c(i,d){if(1&i&&(t.j41(0,"div",1),t.DNE(1,l,6,0,"div",2),t.DNE(2,k,3,1,"div",3),t.DNE(3,o,45,20,"div",4),t.k0s()),2&i){const e=t.XpG();t.R7$(1),t.Y8G("ngIf",e.loading),t.R7$(1),t.Y8G("ngIf",e.error),t.R7$(1),t.Y8G("ngIf",e.appointment&&!e.loading&&!e.error)}}let p=(()=>{class i{constructor(e,s){this.appointmentService=e,this.router=s,this.appointment=null,this.loading=!1,this.error=null}ngOnInit(){this.appointmentId&&this.loadAppointment()}loadAppointment(){this.appointmentId&&(this.loading=!0,this.error=null,this.appointmentService.getAppointment(this.appointmentId).subscribe({next:e=>{this.appointment=e,this.loading=!1},error:e=>{this.error="Failed to load appointment details",this.loading=!1,console.error("Error loading appointment:",e)}}))}navigateToAppointment(){this.appointment&&this.router.navigate(["/appointments",this.appointment.id])}getContextTitle(){switch(this.chatType){case"PRE_APPOINTMENT":return"Pre-Appointment Discussion";case"POST_APPOINTMENT":return"Post-Appointment Follow-up";case"URGENT":return"Urgent Medical Consultation";case"PRESCRIPTION_INQUIRY":return"Prescription Questions";case"FOLLOW_UP":return"Follow-up Care";default:return"Appointment Discussion"}}getContextIcon(){switch(this.chatType){case"PRE_APPOINTMENT":return"fas fa-clock text-info";case"POST_APPOINTMENT":return"fas fa-check-circle text-success";case"URGENT":return"fas fa-exclamation-triangle text-danger";case"PRESCRIPTION_INQUIRY":return"fas fa-pills text-primary";case"FOLLOW_UP":return"fas fa-stethoscope text-secondary";default:return"fas fa-calendar text-primary"}}isBeforeAppointment(){return!!this.appointment&&new Date(`${this.appointment.date}T${this.appointment.startTime}`)>new Date}isAfterAppointment(){return!!this.appointment&&new Date(`${this.appointment.date}T${this.appointment.endTime}`)<new Date}getTimeUntilAppointment(){if(!this.appointment)return"";const e=new Date(`${this.appointment.date}T${this.appointment.startTime}`),s=new Date,a=e.getTime()-s.getTime();if(a<=0)return"Appointment has passed";const g=Math.floor(a/864e5),u=Math.floor(a%864e5/36e5),w=Math.floor(a%36e5/6e4);return g>0?`${g} day${g>1?"s":""} remaining`:u>0?`${u} hour${u>1?"s":""} remaining`:`${w} minute${w>1?"s":""} remaining`}openMeetingLink(e){window.open(e,"_blank")}static{this.\u0275fac=function(s){return new(s||i)(t.rXU(y.h),t.rXU(T.Ix))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-appointment-context"]],inputs:{appointmentId:"appointmentId",chatType:"chatType"},decls:1,vars:1,consts:[["class","appointment-context-card",4,"ngIf"],[1,"appointment-context-card"],["class","text-center p-3",4,"ngIf"],["class","alert alert-warning mb-0",4,"ngIf"],["class","appointment-context",4,"ngIf"],[1,"text-center","p-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"ms-2"],[1,"alert","alert-warning","mb-0"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"appointment-context"],[1,"context-header"],[1,"d-flex","align-items-center"],[1,"mb-0","fw-bold"],["type","button","title","View full appointment details",1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"fas","fa-external-link-alt"],[1,"appointment-summary"],[1,"row","g-2"],[1,"col-md-6"],[1,"summary-item"],[1,"fas","fa-calendar-alt","text-muted","me-2"],[1,"fw-medium"],[1,"fas","fa-clock","text-muted","me-2"],[1,"fas","fa-user-md","text-muted","me-2"],[1,"fas","fa-tag","text-muted","me-2"],[1,"badge"],[1,"summary-item","mt-2"],[1,"text-muted","me-2"],["class","summary-item mt-2",4,"ngIf"],[1,"time-context","mt-3"],["class","alert alert-info py-2 mb-0",4,"ngIf"],["class","alert alert-success py-2 mb-0",4,"ngIf"],[1,"quick-actions","mt-3"],["role","group",1,"btn-group","w-100"],["type","button","class","btn btn-sm btn-primary",3,"click",4,"ngIf"],["type","button",1,"btn","btn-sm","btn-outline-secondary",3,"click"],[1,"fas","fa-eye","me-1"],[1,"fas","fa-notes-medical","text-muted","me-2"],[1,"text-muted"],[1,"alert","alert-info","py-2","mb-0"],[1,"fas","fa-info-circle","me-2"],[1,"alert","alert-success","py-2","mb-0"],[1,"fas","fa-check-circle","me-2"],["type","button",1,"btn","btn-sm","btn-primary",3,"click"],[1,"fas","fa-video","me-1"]],template:function(s,a){1&s&&t.DNE(0,c,4,3,"div",0),2&s&&t.Y8G("ngIf",a.appointmentId)},dependencies:[_.bT,_.vh],styles:[".appointment-context-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%);border:1px solid #dee2e6;border-radius:12px;margin-bottom:1rem;overflow:hidden;box-shadow:0 2px 4px #0000000d}.context-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem;background:white;border-bottom:1px solid #dee2e6}.context-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057}.appointment-summary[_ngcontent-%COMP%]{padding:1rem}.summary-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.5rem}.summary-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.summary-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:16px;text-align:center}.badge[_ngcontent-%COMP%]{font-size:.75rem}.badge.badge-pending[_ngcontent-%COMP%]{background-color:#ffc107;color:#000}.badge.badge-scheduled[_ngcontent-%COMP%]{background-color:#17a2b8;color:#fff}.badge.badge-confirmed[_ngcontent-%COMP%]{background-color:#28a745;color:#fff}.badge.badge-completed[_ngcontent-%COMP%]{background-color:#6c757d;color:#fff}.badge.badge-cancelled[_ngcontent-%COMP%]{background-color:#dc3545;color:#fff}.time-context[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{border-radius:8px;font-size:.875rem}.quick-actions[_ngcontent-%COMP%]{padding:0 1rem 1rem}.quick-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:6px;font-size:.875rem}@media (max-width: 768px){.context-header[_ngcontent-%COMP%], .appointment-summary[_ngcontent-%COMP%]{padding:.75rem}.quick-actions[_ngcontent-%COMP%]{padding:0 .75rem .75rem}.quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]{flex-direction:column}.quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:6px!important;margin-bottom:.25rem}.quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-bottom:0}}.appointment-context-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}"]})}}return i})();const P=["messagesContainer"];function E(i,d){if(1&i&&t.nrm(0,"app-doctor-availability",26),2&i){const e=t.XpG(2);let s;t.Y8G("doctorId",null==(s=e.getOtherParticipant())?null:s.id)("showDetails",!1)}}function D(i,d){if(1&i&&t.nrm(0,"app-appointment-context",27),2&i){const e=t.XpG(2);t.Y8G("appointmentId",e.chat.relatedAppointment.id)("chatType",e.chat.type)}}function R(i,d){1&i&&(t.j41(0,"div",28)(1,"div",29)(2,"span",30),t.EFF(3,"Loading messages..."),t.k0s()()())}function j(i,d){1&i&&(t.j41(0,"div",31),t.nrm(1,"i",32),t.j41(2,"p"),t.EFF(3,"No messages yet"),t.k0s(),t.j41(4,"small"),t.EFF(5,"Start the conversation!"),t.k0s()())}function S(i,d){if(1&i&&(t.j41(0,"div",38)(1,"span",39),t.EFF(2),t.k0s()()),2&i){const e=t.XpG().$implicit,s=t.XpG(3);t.R7$(2),t.JRh(s.formatMessageDate(e.createdAt))}}function $(i,d){if(1&i&&(t.qex(0),t.DNE(1,S,3,1,"div",36),t.nrm(2,"app-message-item",37),t.bVm()),2&i){const e=d.$implicit,s=d.index,a=t.XpG(3);t.R7$(1),t.Y8G("ngIf",a.shouldShowDateSeparator(s)),t.R7$(1),t.Y8G("message",e)("isOwn",e.sender.id===(null==a.currentUser?null:a.currentUser.id))}}function G(i,d){if(1&i&&(t.j41(0,"div",40)(1,"div",41),t.nrm(2,"span")(3,"span")(4,"span"),t.k0s(),t.j41(5,"small",42),t.EFF(6),t.k0s()()),2&i){const e=t.XpG(3);let s;t.R7$(6),t.SpI("",null==(s=e.getOtherParticipant())?null:s.fullName," is typing...")}}function N(i,d){if(1&i&&(t.j41(0,"div",33),t.DNE(1,$,3,3,"ng-container",34),t.DNE(2,G,7,1,"div",35),t.k0s()),2&i){const e=t.XpG(2);t.R7$(1),t.Y8G("ngForOf",e.messages),t.R7$(1),t.Y8G("ngIf",e.isTyping())}}function A(i,d){1&i&&(t.j41(0,"div",43)(1,"small",44),t.nrm(2,"i",45),t.EFF(3," Connection lost. Trying to reconnect... "),t.k0s()())}function L(i,d){if(1&i){const e=t.RV6();t.j41(0,"div",2)(1,"div",3)(2,"div",4),t.nrm(3,"img",5),t.j41(4,"div",6)(5,"h6",7),t.EFF(6),t.k0s(),t.j41(7,"small",8),t.EFF(8),t.k0s(),t.DNE(9,E,1,2,"app-doctor-availability",9),t.k0s()(),t.j41(10,"div",10)(11,"span",11),t.nrm(12,"i",12),t.k0s()()(),t.DNE(13,D,1,2,"app-appointment-context",13),t.j41(14,"div",14,15),t.DNE(16,R,4,0,"div",16),t.DNE(17,j,6,0,"div",17),t.DNE(18,N,3,2,"div",18),t.k0s(),t.j41(19,"div",19)(20,"form",20),t.bIt("ngSubmit",function(){t.eBV(e);const a=t.XpG();return t.Njj(a.sendMessage())}),t.j41(21,"div",21)(22,"input",22),t.bIt("input",function(){t.eBV(e);const a=t.XpG();return t.Njj(a.onTyping())}),t.k0s(),t.j41(23,"button",23),t.nrm(24,"i",24),t.k0s()()(),t.DNE(25,A,4,0,"div",25),t.k0s()()}if(2&i){const e=t.XpG();let s,a,g,u,w;t.R7$(3),t.Y8G("src",(null==(s=e.getOtherParticipant())?null:s.avatar)||"/assets/images/default-avatar.png",t.B4B)("alt",null==(a=e.getOtherParticipant())?null:a.fullName),t.R7$(3),t.JRh(null==(g=e.getOtherParticipant())?null:g.fullName),t.R7$(2),t.SpI(" ","DOCTOR"===(null==(u=e.getOtherParticipant())?null:u.role)?"Dr. "+(null==(u=e.getOtherParticipant())?null:u.specialization):"Patient"," "),t.R7$(1),t.Y8G("ngIf","DOCTOR"===(null==(w=e.getOtherParticipant())?null:w.role)),t.R7$(2),t.AVh("connected",e.connectionStatus)("disconnected",!e.connectionStatus),t.R7$(1),t.AVh("bi-wifi",e.connectionStatus)("bi-wifi-off",!e.connectionStatus),t.R7$(1),t.Y8G("ngIf",null==e.chat?null:e.chat.relatedAppointment),t.R7$(3),t.Y8G("ngIf",e.loading),t.R7$(1),t.Y8G("ngIf",!e.loading&&0===e.messages.length),t.R7$(1),t.Y8G("ngIf",!e.loading&&e.messages.length>0),t.R7$(2),t.Y8G("formGroup",e.messageForm),t.R7$(2),t.Y8G("disabled",!e.connectionStatus),t.R7$(1),t.Y8G("disabled",!e.messageForm.valid||!e.connectionStatus),t.R7$(2),t.Y8G("ngIf",!e.connectionStatus)}}function U(i,d){1&i&&(t.j41(0,"div",46)(1,"div",47),t.nrm(2,"i",32),t.j41(3,"h5"),t.EFF(4,"Select a conversation"),t.k0s(),t.j41(5,"p"),t.EFF(6,"Choose a conversation from the list to start messaging"),t.k0s()()())}let Y=(()=>{class i{constructor(e,s,a){this.fb=e,this.chatService=s,this.authService=a,this.chat=null,this.messages=[],this.loading=!1,this.typingUsers=new Set,this.connectionStatus=!1,this.subscriptions=[],this.shouldScrollToBottom=!1,this.messageForm=this.fb.group({content:["",[n.k0.required,n.k0.minLength(1)]]})}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.subscribeToServices()}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe()),this.typingTimeout&&clearTimeout(this.typingTimeout)}ngAfterViewChecked(){this.shouldScrollToBottom&&(this.scrollToBottom(),this.shouldScrollToBottom=!1)}subscribeToServices(){const e=this.chatService.connectionStatus$.subscribe(g=>{this.connectionStatus=g});this.subscriptions.push(e);const s=this.chatService.messages$.subscribe(g=>{this.chat&&g.chatId===this.chat.id&&(this.messages.push(g),this.shouldScrollToBottom=!0,g.sender.id!==this.currentUser?.id&&this.chatService.markMessagesAsRead(this.chat.id).subscribe())});this.subscriptions.push(s);const a=this.chatService.typing$.subscribe(g=>{this.handleTypingNotification(g)});this.subscriptions.push(a)}loadChat(e){this.chat=e,this.messages=[],this.typingUsers.clear(),e&&(this.loadMessages(),this.chatService.subscribeToChatMessages(e.id))}loadMessages(){this.chat&&(this.loading=!0,this.chatService.getChatMessages(this.chat.id).subscribe({next:e=>{this.messages=e.reverse(),this.loading=!1,this.shouldScrollToBottom=!0},error:e=>{console.error("Failed to load messages:",e),this.loading=!1}}))}sendMessage(){if(!this.messageForm.valid||!this.chat||!this.connectionStatus)return;const e=this.messageForm.get("content")?.value?.trim();if(e)try{this.chatService.sendMessage(this.chat.id,e),this.messageForm.reset(),this.stopTyping()}catch(s){console.error("Failed to send message:",s)}}onTyping(){this.chat&&(this.chatService.sendTypingNotification(this.chat.id,!0),this.typingTimeout&&clearTimeout(this.typingTimeout),this.typingTimeout=setTimeout(()=>{this.stopTyping()},3e3))}stopTyping(){this.chat&&(this.chatService.sendTypingNotification(this.chat.id,!1),this.typingTimeout&&(clearTimeout(this.typingTimeout),this.typingTimeout=null))}handleTypingNotification(e){e.userId!==this.currentUser?.id&&("typing"===e.status?this.typingUsers.add(e.userId):this.typingUsers.delete(e.userId))}scrollToBottom(){try{if(this.messagesContainer){const e=this.messagesContainer.nativeElement;e.scrollTop=e.scrollHeight}}catch(e){console.error("Error scrolling to bottom:",e)}}getOtherParticipant(){return this.chat?"PATIENT"===this.currentUser?.role?this.chat.doctor:this.chat.patient:null}isTyping(){return this.typingUsers.size>0}formatMessageTime(e){return new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}formatMessageDate(e){const s=new Date(e),a=new Date,g=new Date(a);return g.setDate(g.getDate()-1),s.toDateString()===a.toDateString()?"Today":s.toDateString()===g.toDateString()?"Yesterday":s.toLocaleDateString()}shouldShowDateSeparator(e){if(0===e)return!0;const a=this.messages[e-1];return new Date(this.messages[e].createdAt).toDateString()!==new Date(a.createdAt).toDateString()}static{this.\u0275fac=function(s){return new(s||i)(t.rXU(n.ok),t.rXU(C.m),t.rXU(b.u))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-chat-window"]],viewQuery:function(s,a){if(1&s&&t.GBs(P,5),2&s){let g;t.mGM(g=t.lsd())&&(a.messagesContainer=g.first)}},inputs:{chat:"chat"},decls:2,vars:2,consts:[["class","chat-window",4,"ngIf"],["class","chat-placeholder",4,"ngIf"],[1,"chat-window"],[1,"chat-header"],[1,"participant-info"],[1,"participant-avatar",3,"src","alt"],[1,"participant-details"],[1,"mb-0"],[1,"text-muted"],["size","sm",3,"doctorId","showDetails",4,"ngIf"],[1,"connection-status"],[1,"status-indicator"],[1,"bi"],[3,"appointmentId","chatType",4,"ngIf"],[1,"messages-container"],["messagesContainer",""],["class","text-center p-3",4,"ngIf"],["class","text-center p-4 text-muted",4,"ngIf"],["class","messages-list",4,"ngIf"],[1,"message-input-container"],[1,"message-form",3,"formGroup","ngSubmit"],[1,"input-group"],["type","text","formControlName","content","placeholder","Type a message...","autocomplete","off",1,"form-control",3,"disabled","input"],["type","submit",1,"btn","btn-primary",3,"disabled"],[1,"bi","bi-send"],["class","connection-warning",4,"ngIf"],["size","sm",3,"doctorId","showDetails"],[3,"appointmentId","chatType"],[1,"text-center","p-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"text-center","p-4","text-muted"],[1,"bi","bi-chat-square-text","fs-1","mb-3","d-block"],[1,"messages-list"],[4,"ngFor","ngForOf"],["class","typing-indicator",4,"ngIf"],["class","date-separator",4,"ngIf"],[3,"message","isOwn"],[1,"date-separator"],[1,"date-label"],[1,"typing-indicator"],[1,"typing-dots"],[1,"text-muted","ms-2"],[1,"connection-warning"],[1,"text-warning"],[1,"bi","bi-exclamation-triangle","me-1"],[1,"chat-placeholder"],[1,"text-center","text-muted"]],template:function(s,a){1&s&&(t.DNE(0,L,26,21,"div",0),t.DNE(1,U,7,0,"div",1)),2&s&&(t.Y8G("ngIf",a.chat),t.R7$(1),t.Y8G("ngIf",!a.chat))},dependencies:[_.Sq,_.bT,n.qT,n.me,n.BC,n.cb,n.j4,n.JD,f.U,x,p],styles:[".chat-window[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column}.chat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem;border-bottom:1px solid #e9ecef;background-color:#f8f9fa}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]{display:flex;align-items:center}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;margin-right:.75rem;border:2px solid #e9ecef}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-details[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-weight:600;color:#333}.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator.connected[_ngcontent-%COMP%]{color:#28a745}.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator.disconnected[_ngcontent-%COMP%]{color:#dc3545}.messages-container[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1rem;background-color:#f8f9fa}.messages-list[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%]{text-align:center;margin:1rem 0}.messages-list[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%]   .date-label[_ngcontent-%COMP%]{background-color:#e9ecef;padding:.25rem .75rem;border-radius:1rem;font-size:.75rem;color:#6c757d}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;margin:.5rem 0}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:#e9ecef;padding:.5rem .75rem;border-radius:1rem}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:6px;height:6px;background-color:#6c757d;border-radius:50%;margin:0 2px;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}.message-input-container[_ngcontent-%COMP%]{padding:1rem;border-top:1px solid #e9ecef;background-color:#fff}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-right:none}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{box-shadow:none;border-color:#80bdff}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-left:none;padding:.5rem 1rem}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled{opacity:.5}.message-input-container[_ngcontent-%COMP%]   .connection-warning[_ngcontent-%COMP%]{margin-top:.5rem;text-align:center}.chat-placeholder[_ngcontent-%COMP%]{height:100%;display:flex;align-items:center;justify-content:center;background-color:#f8f9fa}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]{max-width:300px}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6c757d}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:#495057}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d}@keyframes _ngcontent-%COMP%_typing{0%,60%,to{transform:translateY(0)}30%{transform:translateY(-10px)}}@media (max-width: 768px){.chat-header[_ngcontent-%COMP%]{padding:.75rem}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%]{width:35px;height:35px}.messages-container[_ngcontent-%COMP%], .message-input-container[_ngcontent-%COMP%]{padding:.75rem}}"]})}}return i})()},2693:(F,O,m)=>{m.r(O),m.d(O,{ChatModule:()=>T});var n=m(177),t=m(4341),C=m(2434),b=m(4978),_=m(9339),f=m(540);const v=[{path:"",canActivate:[b.q],component:_.q}];let x=(()=>{class l{static{this.\u0275fac=function(M){return new(M||l)}}static{this.\u0275mod=f.$C({type:l})}static{this.\u0275inj=f.G2t({imports:[C.iI.forChild(v),C.iI]})}}return l})();var y=m(3887);let T=(()=>{class l{static{this.\u0275fac=function(M){return new(M||l)}}static{this.\u0275mod=f.$C({type:l})}static{this.\u0275inj=f.G2t({imports:[n.MD,t.YN,t.X1,C.iI,x,y.G]})}}return l})()}}]);