"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[76],{3285:(C,r,a)=>{a.d(r,{Q:()=>d});var c=a(467),t=a(540),l=a(8211),_=a(9545),u=a(8010),h=a(2434),f=a(177);function p(o,m){1&o&&(t.j41(0,"span",3)(1,"span",4),t.EFF(2,"Loading..."),t.k0s()())}function g(o,m){if(1&o&&t.nrm(0,"i"),2&o){const e=t.XpG();t.HbH(e.iconClass+" me-2")}}let d=(()=>{class o{constructor(e,s,n,i){this.chatService=e,this.appointmentService=s,this.authService=n,this.router=i,this.config={},this.loading=!1}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.setDefaults()}setDefaults(){this.config.buttonText||(this.config.buttonText=this.getDefaultButtonText()),this.config.buttonClass||(this.config.buttonClass="btn-primary"),void 0===this.config.showIcon&&(this.config.showIcon=!0),this.config.size||(this.config.size="md")}getDefaultButtonText(){switch(this.config.chatType){case"PRE_APPOINTMENT":return"Chat Before Appointment";case"POST_APPOINTMENT":return"Follow-up Chat";case"URGENT":return"Urgent Chat";case"PRESCRIPTION_INQUIRY":return"Ask About Prescription";case"FOLLOW_UP":return"Follow-up Questions";default:return"Start Chat"}}startChat(){var e=this;return(0,c.A)(function*(){if(!e.loading){e.loading=!0;try{let s,n;s="PATIENT"===e.currentUser.role?e.config.doctorId:e.config.patientId,n=e.config.appointmentId?yield e.appointmentService.createAppointmentChat(e.config.appointmentId,s,e.config.chatType||"GENERAL",e.config.subject).toPromise():yield e.chatService.createOrGetChat(s).toPromise(),e.router.navigate(["/chat"],{queryParams:{chatId:n.id,appointmentId:e.config.appointmentId}})}catch(s){console.error("Error starting chat:",s)}finally{e.loading=!1}}})()}get buttonSizeClass(){switch(this.config.size){case"sm":return"btn-sm";case"lg":return"btn-lg";default:return""}}get iconClass(){switch(this.config.chatType){case"URGENT":return"fas fa-exclamation-triangle text-warning";case"PRESCRIPTION_INQUIRY":return"fas fa-pills";case"FOLLOW_UP":return"fas fa-stethoscope";default:return"fas fa-comments"}}static{this.\u0275fac=function(s){return new(s||o)(t.rXU(l.m),t.rXU(_.h),t.rXU(u.u),t.rXU(h.Ix))}}static{this.\u0275cmp=t.VBU({type:o,selectors:[["app-chat-access"]],inputs:{config:"config"},decls:4,vars:6,consts:[["type","button",3,"disabled","click"],["class","spinner-border spinner-border-sm me-2","role","status",4,"ngIf"],[3,"class",4,"ngIf"],["role","status",1,"spinner-border","spinner-border-sm","me-2"],[1,"visually-hidden"]],template:function(s,n){1&s&&(t.j41(0,"button",0),t.bIt("click",function(){return n.startChat()}),t.DNE(1,p,3,0,"span",1),t.DNE(2,g,1,2,"i",2),t.EFF(3),t.k0s()),2&s&&(t.HbH("btn "+n.config.buttonClass+" "+n.buttonSizeClass),t.Y8G("disabled",n.loading),t.R7$(1),t.Y8G("ngIf",n.loading),t.R7$(1),t.Y8G("ngIf",!n.loading&&n.config.showIcon),t.R7$(1),t.SpI(" ",n.config.buttonText,"\n"))},dependencies:[f.bT],styles:[".btn[_ngcontent-%COMP%]{transition:all .2s ease-in-out}.btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 8px #0000001a}.btn[_ngcontent-%COMP%]:disabled{transform:none;box-shadow:none}.text-warning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.7}to{opacity:1}}"]})}}return o})()}}]);