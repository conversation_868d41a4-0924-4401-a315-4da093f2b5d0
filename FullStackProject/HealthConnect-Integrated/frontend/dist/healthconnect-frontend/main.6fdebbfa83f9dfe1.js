"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[792],{4978:(Je,me,<PERSON>)=>{O.d(me,{q:()=>se});var c=O(540),C=O(8010),ue=O(2434);let se=(()=>{class Q{constructor(ne,fe){this.authService=ne,this.router=fe}canActivate(ne,fe){if(this.authService.isAuthenticated()){const ae=ne.data.roles;if(ae&&ae.length>0&&!this.authService.hasRole(ae)){const z=this.authService.getCurrentUser();return this.router.navigate("DOCTOR"===z?.role?["/doctor/dashboard"]:"PATIENT"===z?.role?["/patient/dashboard"]:["/auth/login"]),!1}return!0}return this.router.navigate(["/auth/login"],{queryParams:{returnUrl:fe.url}}),!1}static{this.\u0275fac=function(fe){return new(fe||Q)(c.KVO(C.u),c.KVO(ue.Ix))}}static{this.\u0275prov=c.jDH({token:Q,factory:Q.\u0275fac,providedIn:"root"})}}return Q})()},8010:(Je,me,O)=>{O.d(me,{u:()=>ae});var c=O(4412),C=O(8141),ue=O(9437),se=O(8810),Q=O(5312),oe=O(540),ne=O(1626),fe=O(2434);let ae=(()=>{class pe{constructor(G,De){this.http=G,this.router=De,this.API_URL=Q.c.apiUrl+"/auth",this.currentUserSubject=new c.t(null),this.currentUser$=this.currentUserSubject.asObservable(),this.loadUserFromStorage()}loadUserFromStorage(){const G=this.getToken(),De=localStorage.getItem("currentUser");if(G&&De&&!this.isTokenExpired(G))try{const we=JSON.parse(De);this.currentUserSubject.next(we)}catch(we){console.error("Error parsing stored user data:",we),this.clearAuthData()}else this.clearAuthData()}clearAuthData(){localStorage.removeItem("token"),localStorage.removeItem("currentUser"),this.currentUserSubject.next(null)}register(G){return this.http.post(`${this.API_URL}/register`,G).pipe((0,C.M)(De=>{De.token&&this.setAuthData(De)}),(0,ue.W)(this.handleError))}login(G){return this.http.post(`${this.API_URL}/login`,G).pipe((0,C.M)(De=>{De.token&&this.setAuthData(De)}),(0,ue.W)(this.handleError))}logout(){this.clearAuthData(),this.router.url.includes("/auth")||this.router.navigate(["/auth/login"])}setAuthData(G){localStorage.setItem("token",G.token);const De={id:G.id,fullName:G.fullName,email:G.email,role:G.role,avatar:G.avatar,specialization:G.specialization,licenseNumber:G.licenseNumber,affiliation:G.affiliation,yearsOfExperience:G.yearsOfExperience,phoneNumber:G.phoneNumber,address:G.address,createdAt:G.createdAt,updatedAt:G.updatedAt};localStorage.setItem("currentUser",JSON.stringify(De)),this.currentUserSubject.next(De)}getToken(){return localStorage.getItem("token")}getCurrentUser(){return this.currentUserSubject.value}isAuthenticated(){const G=this.getToken();return!!G&&!this.isTokenExpired(G)}hasRole(G){const De=this.getCurrentUser();return!!De&&G.includes(De.role)}isTokenExpired(G){try{const De=JSON.parse(atob(G.split(".")[1])),we=Math.floor(Date.now()/1e3);return De.exp<we}catch{return!0}}handleError(G){let De="An error occurred";return G.error?"string"==typeof G.error?De=G.error:G.error.message&&(De=G.error.message):G.message&&(De=G.message),(0,se.$)(()=>new Error(De))}static{this.\u0275fac=function(De){return new(De||pe)(oe.KVO(ne.Qq),oe.KVO(fe.Ix))}}static{this.\u0275prov=oe.jDH({token:pe,factory:pe.\u0275fac,providedIn:"root"})}}return pe})()},7436:(Je,me,O)=>{O.d(me,{s:()=>fe});var c=O(4412),C=O(7673),ue=O(9437),se=O(6354),Q=O(5312),oe=O(540),ne=O(1626);let fe=(()=>{class ae{constructor(z){this.http=z,this.apiUrl=`${Q.c.apiUrl}/api/i18n`,this.currentLanguage$=new c.t("en"),this.translations$=new c.t({}),this.supportedLanguages=[{code:"en",name:"English",flag:"\u{1f1fa}\u{1f1f8}"},{code:"es",name:"Espa\xf1ol",flag:"\u{1f1ea}\u{1f1f8}"},{code:"fr",name:"Fran\xe7ais",flag:"\u{1f1eb}\u{1f1f7}"},{code:"de",name:"Deutsch",flag:"\u{1f1e9}\u{1f1ea}"},{code:"pt",name:"Portugu\xeas",flag:"\u{1f1f5}\u{1f1f9}"}],this.initializeLanguage()}initializeLanguage(){const z=localStorage.getItem("healthconnect_language"),G=navigator.language.split("-")[0],De=z||this.supportedLanguages.find(we=>we.code===G)?.code||"en";this.setLanguage(De)}getCurrentLanguage(){return this.currentLanguage$.asObservable()}getCurrentLanguageValue(){return this.currentLanguage$.value}getTranslations(){return this.translations$.asObservable()}getSupportedLanguages(){return this.supportedLanguages}setLanguage(z){this.supportedLanguages.find(G=>G.code===z)&&(this.currentLanguage$.next(z),localStorage.setItem("healthconnect_language",z),this.loadTranslations(z))}loadTranslations(z){this.getTranslationsFromServer(z).subscribe({next:G=>{this.translations$.next(G.translations)},error:G=>{console.error("Failed to load translations:",G),this.loadDefaultTranslations()}})}getTranslationsFromServer(z){return this.http.get(`${this.apiUrl}/translations/${z}`).pipe((0,ue.W)(G=>(console.error("Error fetching translations from server:",G),(0,C.of)(this.getDefaultTranslations(z)))))}translate(z,G){return this.translations$.pipe((0,se.T)(De=>{let we=De[z]||z;return G&&Object.keys(G).forEach(Fe=>{we=we.replace(`{${Fe}}`,G[Fe])}),we}))}translateSync(z,G){let we=this.translations$.value[z]||z;return G&&Object.keys(G).forEach(Fe=>{we=we.replace(`{${Fe}}`,G[Fe])}),we}translateBatch(z){return this.http.post(`${this.apiUrl}/translate/batch`,{keys:z,language:this.getCurrentLanguageValue()}).pipe((0,se.T)(G=>G.translations),(0,ue.W)(G=>{console.error("Error in batch translation:",G);const De=this.translations$.value,we={};return z.forEach(Fe=>{we[Fe]=De[Fe]||Fe}),(0,C.of)(we)}))}detectLanguage(z){return this.http.post(`${this.apiUrl}/detect-language`,{text:z}).pipe((0,ue.W)(G=>(console.error("Error detecting language:",G),(0,C.of)({detectedLanguage:"en",confidence:.5}))))}formatMessage(z,G){return this.http.post(`${this.apiUrl}/format-message`,{messageKey:z,language:this.getCurrentLanguageValue(),parameters:G}).pipe((0,se.T)(De=>De.formattedMessage),(0,ue.W)(De=>(console.error("Error formatting message:",De),this.translate(z))))}loadDefaultTranslations(){const z=this.getDefaultTranslations(this.getCurrentLanguageValue());this.translations$.next(z.translations)}getDefaultTranslations(z){const G={en:{welcome:"Welcome to HealthConnect",login:"Login",logout:"Logout",register:"Register",dashboard:"Dashboard",appointments:"Appointments",prescriptions:"Prescriptions",chat:"Chat",video_consultation:"Video Consultation",profile:"Profile",settings:"Settings",doctor:"Doctor",patient:"Patient",success:"Success",error:"Error",cancel:"Cancel",save:"Save",delete:"Delete",edit:"Edit",view:"View",search:"Search",loading:"Loading...",no_data:"No data available",confirm:"Confirm",yes:"Yes",no:"No"},es:{welcome:"Bienvenido a HealthConnect",login:"Iniciar Sesi\xf3n",logout:"Cerrar Sesi\xf3n",register:"Registrarse",dashboard:"Panel de Control",appointments:"Citas",prescriptions:"Recetas",chat:"Chat",video_consultation:"Consulta por Video",profile:"Perfil",settings:"Configuraci\xf3n",doctor:"Doctor",patient:"Paciente",success:"\xc9xito",error:"Error",cancel:"Cancelar",save:"Guardar",delete:"Eliminar",edit:"Editar",view:"Ver",search:"Buscar",loading:"Cargando...",no_data:"No hay datos disponibles",confirm:"Confirmar",yes:"S\xed",no:"No"},fr:{welcome:"Bienvenue \xe0 HealthConnect",login:"Connexion",logout:"D\xe9connexion",register:"S'inscrire",dashboard:"Tableau de Bord",appointments:"Rendez-vous",prescriptions:"Ordonnances",chat:"Chat",video_consultation:"Consultation Vid\xe9o",profile:"Profil",settings:"Param\xe8tres",doctor:"Docteur",patient:"Patient",success:"Succ\xe8s",error:"Erreur",cancel:"Annuler",save:"Sauvegarder",delete:"Supprimer",edit:"Modifier",view:"Voir",search:"Rechercher",loading:"Chargement...",no_data:"Aucune donn\xe9e disponible",confirm:"Confirmer",yes:"Oui",no:"Non"}};return{language:z,translations:G[z]||G.en,count:Object.keys(G[z]||G.en).length}}getLanguageName(z){const G=this.supportedLanguages.find(De=>De.code===z);return G?G.name:z}getLanguageFlag(z){return this.supportedLanguages.find(De=>De.code===z)?.flag||"\u{1f310}"}static{this.\u0275fac=function(G){return new(G||ae)(oe.KVO(ne.Qq))}}static{this.\u0275prov=oe.jDH({token:ae,factory:ae.\u0275fac,providedIn:"root"})}}return ae})()},5567:(Je,me,O)=>{O.d(me,{J:()=>se});var c=O(467),C=O(4412),ue=O(540);let se=(()=>{class Q{constructor(){this.notifications$=new C.t([]),this.unreadCount$=new C.t(0),this.loadNotifications()}getNotifications(){return this.notifications$.asObservable()}getUnreadCount(){return this.unreadCount$.asObservable()}addNotification(ne){const fe={...ne,id:this.generateId(),timestamp:new Date,read:!1},pe=[fe,...this.notifications$.value];this.notifications$.next(pe),this.updateUnreadCount(),this.saveNotifications(pe),this.showBrowserNotification(fe)}markAsRead(ne){const fe=this.notifications$.value.map(ae=>ae.id===ne?{...ae,read:!0}:ae);this.notifications$.next(fe),this.updateUnreadCount(),this.saveNotifications(fe)}markAllAsRead(){const ne=this.notifications$.value.map(fe=>({...fe,read:!0}));this.notifications$.next(ne),this.updateUnreadCount(),this.saveNotifications(ne)}removeNotification(ne){const fe=this.notifications$.value.filter(ae=>ae.id!==ne);this.notifications$.next(fe),this.updateUnreadCount(),this.saveNotifications(fe)}clearAll(){this.notifications$.next([]),this.unreadCount$.next(0),this.saveNotifications([])}addMessageNotification(ne,fe,ae){this.addNotification({type:"message",title:`New message from ${ne.fullName}`,message:fe.length>100?fe.substring(0,100)+"...":fe,priority:"medium",fromUser:{id:ne.id,name:ne.fullName,avatar:ne.avatar},actionUrl:`/chat?chatId=${ae}`,actionText:"Reply"})}addAppointmentNotification(ne,fe){let ae="",pe="",z="medium";switch(ne){case"booked":ae="Appointment Booked",pe=`Your appointment with ${fe.doctor.fullName} is scheduled for ${fe.date}`;break;case"reminder":ae="Appointment Reminder",pe=`Your appointment with ${fe.doctor.fullName} is in 1 hour`,z="high";break;case"cancelled":ae="Appointment Cancelled",pe=`Your appointment with ${fe.doctor.fullName} has been cancelled`,z="high"}this.addNotification({type:"appointment",title:ae,message:pe,priority:z,actionUrl:`/appointments/${fe.id}`,actionText:"View Details"})}addUrgentNotification(ne,fe,ae){this.addNotification({type:"urgent",title:ne,message:fe,priority:"urgent",actionUrl:ae,actionText:ae?"View":void 0})}loadNotifications(){const ne=localStorage.getItem("healthconnect_notifications");if(ne)try{const fe=JSON.parse(ne).map(ae=>({...ae,timestamp:new Date(ae.timestamp)}));this.notifications$.next(fe),this.updateUnreadCount()}catch(fe){console.error("Error loading notifications:",fe)}}saveNotifications(ne){try{localStorage.setItem("healthconnect_notifications",JSON.stringify(ne))}catch(fe){console.error("Error saving notifications:",fe)}}updateUnreadCount(){const ne=this.notifications$.value.filter(fe=>!fe.read).length;this.unreadCount$.next(ne)}generateId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}showBrowserNotification(ne){var fe=this;return(0,c.A)(function*(){"Notification"in window&&("granted"===Notification.permission?new Notification(ne.title,{body:ne.message,icon:"/assets/icons/icon-192x192.png",badge:"/assets/icons/icon-72x72.png",tag:ne.id}):"denied"!==Notification.permission&&"granted"===(yield Notification.requestPermission())&&fe.showBrowserNotification(ne))})()}requestNotificationPermission(){return(0,c.A)(function*(){return"Notification"in window&&("granted"===Notification.permission||"denied"!==Notification.permission&&"granted"===(yield Notification.requestPermission()))})()}static{this.\u0275fac=function(fe){return new(fe||Q)}}static{this.\u0275prov=ue.jDH({token:Q,factory:Q.\u0275fac,providedIn:"root"})}}return Q})()},3443:(Je,me,O)=>{O.d(me,{D:()=>fe});var c=O(1626),C=O(9437),ue=O(8141),se=O(8810),Q=O(5312),oe=O(540),ne=O(8010);let fe=(()=>{class ae{constructor(z,G){this.http=z,this.authService=G,this.API_URL=Q.c.apiUrl+"/users"}getHeaders(){const z=this.authService.getToken();return new c.Lr({"Content-Type":"application/json",Authorization:`Bearer ${z}`})}getCurrentUserProfile(){return this.http.get(`${this.API_URL}/me`,{headers:this.getHeaders()}).pipe((0,C.W)(this.handleError))}updateProfile(z){return this.http.put(`${this.API_URL}/me`,z,{headers:this.getHeaders()}).pipe((0,ue.M)(G=>{const De=this.authService.getCurrentUser();if(De){const we={...De,...G};localStorage.setItem("currentUser",JSON.stringify(we))}}),(0,C.W)(this.handleError))}getUserById(z){return this.http.get(`${this.API_URL}/${z}`,{headers:this.getHeaders()}).pipe((0,C.W)(this.handleError))}handleError(z){let G="An error occurred";return z.error?"string"==typeof z.error?G=z.error:z.error.message&&(G=z.error.message):z.message&&(G=z.message),(0,se.$)(()=>new Error(G))}static{this.\u0275fac=function(G){return new(G||ae)(oe.KVO(c.Qq),oe.KVO(ne.u))}}static{this.\u0275prov=oe.jDH({token:ae,factory:ae.\u0275fac,providedIn:"root"})}}return ae})()},3887:(Je,me,O)=>{O.d(me,{G:()=>oe});var c=O(177),C=O(4341),ue=O(2434),se=O(1626),Q=O(540);let oe=(()=>{class ne{static{this.\u0275fac=function(pe){return new(pe||ne)}}static{this.\u0275mod=Q.$C({type:ne})}static{this.\u0275inj=Q.G2t({imports:[c.MD,C.X1,C.YN,ue.iI,se.q1,c.MD,C.X1,C.YN,ue.iI,se.q1]})}}return ne})()},5312:(Je,me,O)=>{O.d(me,{c:()=>c});const c={production:!1,apiUrl:"http://localhost:8080/api",appName:"HealthConnect",version:"1.0.0"}},1413:(Je,me,O)=>{var c=O(345),C=O(540);class ue{}class se{}const Q="*";function ae(_,a=null){return{type:2,steps:_,options:a}}function pe(_){return{type:6,styles:_,offset:null}}class Vt{constructor(a=0,l=0){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._originalOnDoneFns=[],this._originalOnStartFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this._position=0,this.parentPlayer=null,this.totalTime=a+l}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(a=>a()),this._onDoneFns=[])}onStart(a){this._originalOnStartFns.push(a),this._onStartFns.push(a)}onDone(a){this._originalOnDoneFns.push(a),this._onDoneFns.push(a)}onDestroy(a){this._onDestroyFns.push(a)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(a=>a()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(a=>a()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(a){this._position=this.totalTime?a*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(a){const l="start"==a?this._onStartFns:this._onDoneFns;l.forEach(y=>y()),l.length=0}}class Qt{constructor(a){this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0,this.players=a;let l=0,y=0,M=0;const R=this.players.length;0==R?queueMicrotask(()=>this._onFinish()):this.players.forEach(L=>{L.onDone(()=>{++l==R&&this._onFinish()}),L.onDestroy(()=>{++y==R&&this._onDestroy()}),L.onStart(()=>{++M==R&&this._onStart()})}),this.totalTime=this.players.reduce((L,U)=>Math.max(L,U.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(a=>a()),this._onDoneFns=[])}init(){this.players.forEach(a=>a.init())}onStart(a){this._onStartFns.push(a)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(a=>a()),this._onStartFns=[])}onDone(a){this._onDoneFns.push(a)}onDestroy(a){this._onDestroyFns.push(a)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(a=>a.play())}pause(){this.players.forEach(a=>a.pause())}restart(){this.players.forEach(a=>a.restart())}finish(){this._onFinish(),this.players.forEach(a=>a.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(a=>a.destroy()),this._onDestroyFns.forEach(a=>a()),this._onDestroyFns=[])}reset(){this.players.forEach(a=>a.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(a){const l=a*this.totalTime;this.players.forEach(y=>{const M=y.totalTime?Math.min(1,l/y.totalTime):1;y.setPosition(M)})}getPosition(){const a=this.players.reduce((l,y)=>null===l||y.totalTime>l.totalTime?y:l,null);return null!=a?a.getPosition():0}beforeDestroy(){this.players.forEach(a=>{a.beforeDestroy&&a.beforeDestroy()})}triggerCallback(a){const l="start"==a?this._onStartFns:this._onDoneFns;l.forEach(y=>y()),l.length=0}}function Ve(_){return new C.wOt(3e3,!1)}function ht(_){switch(_.length){case 0:return new Vt;case 1:return _[0];default:return new Qt(_)}}function st(_,a,l=new Map,y=new Map){const M=[],R=[];let L=-1,U=null;if(a.forEach(J=>{const de=J.get("offset"),Be=de==L,Ke=Be&&U||new Map;J.forEach((Pt,It)=>{let rt=It,Ct=Pt;if("offset"!==It)switch(rt=_.normalizePropertyName(rt,M),Ct){case"!":Ct=l.get(It);break;case Q:Ct=y.get(It);break;default:Ct=_.normalizeStyleValue(It,rt,Ct,M)}Ke.set(rt,Ct)}),Be||R.push(Ke),U=Ke,L=de}),M.length)throw function lt(_){return new C.wOt(3502,!1)}();return R}function wr(_,a,l,y){switch(a){case"start":_.onStart(()=>y(l&&zn(l,"start",_)));break;case"done":_.onDone(()=>y(l&&zn(l,"done",_)));break;case"destroy":_.onDestroy(()=>y(l&&zn(l,"destroy",_)))}}function zn(_,a,l){const R=vn(_.element,_.triggerName,_.fromState,_.toState,a||_.phaseName,l.totalTime??_.totalTime,!!l.disabled),L=_._data;return null!=L&&(R._data=L),R}function vn(_,a,l,y,M="",R=0,L){return{element:_,triggerName:a,fromState:l,toState:y,phaseName:M,totalTime:R,disabled:!!L}}function rn(_,a,l){let y=_.get(a);return y||_.set(a,y=l),y}function pn(_){const a=_.indexOf(":");return[_.substring(1,a),_.slice(a+1)]}const ui=(()=>typeof document>"u"?null:document.documentElement)();function Yt(_){const a=_.parentNode||_.host||null;return a===ui?null:a}let ze=null,Br=!1;function _t(_,a){for(;a;){if(a===_)return!0;a=Yt(a)}return!1}function At(_,a,l){if(l)return Array.from(_.querySelectorAll(a));const y=_.querySelector(a);return y?[y]:[]}let hr=(()=>{class _{validateStyleProperty(l){return function $r(_){ze||(ze=function ir(){return typeof document<"u"?document.body:null}()||{},Br=!!ze.style&&"WebkitAppearance"in ze.style);let a=!0;return ze.style&&!function xi(_){return"ebkit"==_.substring(1,6)}(_)&&(a=_ in ze.style,!a&&Br&&(a="Webkit"+_.charAt(0).toUpperCase()+_.slice(1)in ze.style)),a}(l)}matchesElement(l,y){return!1}containsElement(l,y){return _t(l,y)}getParentElement(l){return Yt(l)}query(l,y,M){return At(l,y,M)}computeStyle(l,y,M){return M||""}animate(l,y,M,R,L,U=[],J){return new Vt(M,R)}static{this.\u0275fac=function(y){return new(y||_)}}static{this.\u0275prov=C.jDH({token:_,factory:_.\u0275fac})}}return _})(),vt=(()=>{class _{static{this.NOOP=new hr}}return _})();const er=1e3,or="ng-enter",ce="ng-leave",q="ng-trigger",V=".ng-trigger",W="ng-animating",ye=".ng-animating";function xe(_){if("number"==typeof _)return _;const a=_.match(/^(-?[\.\d]+)(m?s)/);return!a||a.length<2?0:Ye(parseFloat(a[1]),a[2])}function Ye(_,a){return"s"===a?_*er:_}function Et(_,a,l){return _.hasOwnProperty("duration")?_:function Ft(_,a,l){let M,R=0,L="";if("string"==typeof _){const U=_.match(/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i);if(null===U)return a.push(Ve()),{duration:0,delay:0,easing:""};M=Ye(parseFloat(U[1]),U[2]);const J=U[3];null!=J&&(R=Ye(parseFloat(J),U[4]));const de=U[5];de&&(L=de)}else M=_;if(!l){let U=!1,J=a.length;M<0&&(a.push(function tt(){return new C.wOt(3100,!1)}()),U=!0),R<0&&(a.push(function Pe(){return new C.wOt(3101,!1)}()),U=!0),U&&a.splice(J,0,Ve())}return{duration:M,delay:R,easing:L}}(_,a,l)}function en(_,a={}){return Object.keys(_).forEach(l=>{a[l]=_[l]}),a}function Wn(_){const a=new Map;return Object.keys(_).forEach(l=>{a.set(l,_[l])}),a}function j(_,a=new Map,l){if(l)for(let[y,M]of l)a.set(y,M);for(let[y,M]of _)a.set(y,M);return a}function $(_,a,l){a.forEach((y,M)=>{const R=Dn(M);l&&!l.has(M)&&l.set(M,_.style[R]),_.style[R]=y})}function he(_,a){a.forEach((l,y)=>{const M=Dn(y);_.style[M]=""})}function Ie(_){return Array.isArray(_)?1==_.length?_[0]:ae(_):_}const Ne=new RegExp("{{\\s*(.+?)\\s*}}","g");function $t(_){let a=[];if("string"==typeof _){let l;for(;l=Ne.exec(_);)a.push(l[1]);Ne.lastIndex=0}return a}function ke(_,a,l){const y=_.toString(),M=y.replace(Ne,(R,L)=>{let U=a[L];return null==U&&(l.push(function be(_){return new C.wOt(3003,!1)}()),U=""),U.toString()});return M==y?_:M}function dn(_){const a=[];let l=_.next();for(;!l.done;)a.push(l.value),l=_.next();return a}const Nn=/-+([a-z0-9])/g;function Dn(_){return _.replace(Nn,(...a)=>a[1].toUpperCase())}function ct(_,a,l){switch(a.type){case 7:return _.visitTrigger(a,l);case 0:return _.visitState(a,l);case 1:return _.visitTransition(a,l);case 2:return _.visitSequence(a,l);case 3:return _.visitGroup(a,l);case 4:return _.visitAnimate(a,l);case 5:return _.visitKeyframes(a,l);case 6:return _.visitStyle(a,l);case 8:return _.visitReference(a,l);case 9:return _.visitAnimateChild(a,l);case 10:return _.visitAnimateRef(a,l);case 11:return _.visitQuery(a,l);case 12:return _.visitStagger(a,l);default:throw function nt(_){return new C.wOt(3004,!1)}()}}function Ut(_,a){return window.getComputedStyle(_)[a]}const Or="*";function gr(_,a){const l=[];return"string"==typeof _?_.split(/\s*,\s*/).forEach(y=>function Nr(_,a,l){if(":"==_[0]){const J=function Fi(_,a){switch(_){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(l,y)=>parseFloat(y)>parseFloat(l);case":decrement":return(l,y)=>parseFloat(y)<parseFloat(l);default:return a.push(function Me(_){return new C.wOt(3016,!1)}()),"* => *"}}(_,l);if("function"==typeof J)return void a.push(J);_=J}const y=_.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(null==y||y.length<4)return l.push(function Te(_){return new C.wOt(3015,!1)}()),a;const M=y[1],R=y[2],L=y[3];a.push(wi(M,L));"<"==R[0]&&!(M==Or&&L==Or)&&a.push(wi(L,M))}(y,l,a)):l.push(_),l}const nr=new Set(["true","1"]),zr=new Set(["false","0"]);function wi(_,a){const l=nr.has(_)||zr.has(_),y=nr.has(a)||zr.has(a);return(M,R)=>{let L=_==Or||_==M,U=a==Or||a==R;return!L&&l&&"boolean"==typeof M&&(L=M?nr.has(_):zr.has(_)),!U&&y&&"boolean"==typeof R&&(U=R?nr.has(a):zr.has(a)),L&&U}}const Qn=new RegExp("s*:selfs*,?","g");function mr(_,a,l,y){return new Pr(_).build(a,l,y)}class Pr{constructor(a){this._driver=a}build(a,l,y){const M=new Yi(l);return this._resetContextStyleTimingState(M),ct(this,Ie(a),M)}_resetContextStyleTimingState(a){a.currentQuerySelector="",a.collectedStyles=new Map,a.collectedStyles.set("",new Map),a.currentTime=0}visitTrigger(a,l){let y=l.queryCount=0,M=l.depCount=0;const R=[],L=[];return"@"==a.name.charAt(0)&&l.errors.push(function Mn(){return new C.wOt(3006,!1)}()),a.definitions.forEach(U=>{if(this._resetContextStyleTimingState(l),0==U.type){const J=U,de=J.name;de.toString().split(/\s*,\s*/).forEach(Be=>{J.name=Be,R.push(this.visitState(J,l))}),J.name=de}else if(1==U.type){const J=this.visitTransition(U,l);y+=J.queryCount,M+=J.depCount,L.push(J)}else l.errors.push(function Hn(){return new C.wOt(3007,!1)}())}),{type:7,name:a.name,states:R,transitions:L,queryCount:y,depCount:M,options:null}}visitState(a,l){const y=this.visitStyle(a.styles,l),M=a.options&&a.options.params||null;if(y.containsDynamicStyles){const R=new Set,L=M||{};y.styles.forEach(U=>{U instanceof Map&&U.forEach(J=>{$t(J).forEach(de=>{L.hasOwnProperty(de)||R.add(de)})})}),R.size&&(dn(R.values()),l.errors.push(function Re(_,a){return new C.wOt(3008,!1)}()))}return{type:0,name:a.name,style:y,options:M?{params:M}:null}}visitTransition(a,l){l.queryCount=0,l.depCount=0;const y=ct(this,Ie(a.animation),l);return{type:1,matchers:gr(a.expr,l.errors),animation:y,queryCount:l.queryCount,depCount:l.depCount,options:Rr(a.options)}}visitSequence(a,l){return{type:2,steps:a.steps.map(y=>ct(this,y,l)),options:Rr(a.options)}}visitGroup(a,l){const y=l.currentTime;let M=0;const R=a.steps.map(L=>{l.currentTime=y;const U=ct(this,L,l);return M=Math.max(M,l.currentTime),U});return l.currentTime=M,{type:3,steps:R,options:Rr(a.options)}}visitAnimate(a,l){const y=function No(_,a){if(_.hasOwnProperty("duration"))return _;if("number"==typeof _)return eo(Et(_,a).duration,0,"");const l=_;if(l.split(/\s+/).some(R=>"{"==R.charAt(0)&&"{"==R.charAt(1))){const R=eo(0,0,"");return R.dynamic=!0,R.strValue=l,R}const M=Et(l,a);return eo(M.duration,M.delay,M.easing)}(a.timings,l.errors);l.currentAnimateTimings=y;let M,R=a.styles?a.styles:pe({});if(5==R.type)M=this.visitKeyframes(R,l);else{let L=a.styles,U=!1;if(!L){U=!0;const de={};y.easing&&(de.easing=y.easing),L=pe(de)}l.currentTime+=y.duration+y.delay;const J=this.visitStyle(L,l);J.isEmptyStep=U,M=J}return l.currentAnimateTimings=null,{type:4,timings:y,style:M,options:null}}visitStyle(a,l){const y=this._makeStyleAst(a,l);return this._validateStyleAst(y,l),y}_makeStyleAst(a,l){const y=[],M=Array.isArray(a.styles)?a.styles:[a.styles];for(let U of M)"string"==typeof U?U===Q?y.push(U):l.errors.push(new C.wOt(3002,!1)):y.push(Wn(U));let R=!1,L=null;return y.forEach(U=>{if(U instanceof Map&&(U.has("easing")&&(L=U.get("easing"),U.delete("easing")),!R))for(let J of U.values())if(J.toString().indexOf("{{")>=0){R=!0;break}}),{type:6,styles:y,easing:L,offset:a.offset,containsDynamicStyles:R,options:null}}_validateStyleAst(a,l){const y=l.currentAnimateTimings;let M=l.currentTime,R=l.currentTime;y&&R>0&&(R-=y.duration+y.delay),a.styles.forEach(L=>{"string"!=typeof L&&L.forEach((U,J)=>{const de=l.collectedStyles.get(l.currentQuerySelector),Be=de.get(J);let Ke=!0;Be&&(R!=M&&R>=Be.startTime&&M<=Be.endTime&&(l.errors.push(function ft(_,a,l,y,M){return new C.wOt(3010,!1)}()),Ke=!1),R=Be.startTime),Ke&&de.set(J,{startTime:R,endTime:M}),l.options&&function Ue(_,a,l){const y=a.params||{},M=$t(_);M.length&&M.forEach(R=>{y.hasOwnProperty(R)||l.push(function Oe(_){return new C.wOt(3001,!1)}())})}(U,l.options,l.errors)})})}visitKeyframes(a,l){const y={type:5,styles:[],options:null};if(!l.currentAnimateTimings)return l.errors.push(function Ln(){return new C.wOt(3011,!1)}()),y;let R=0;const L=[];let U=!1,J=!1,de=0;const Be=a.steps.map(Bn=>{const hn=this._makeStyleAst(Bn,l);let ur=null!=hn.offset?hn.offset:function yr(_){if("string"==typeof _)return null;let a=null;if(Array.isArray(_))_.forEach(l=>{if(l instanceof Map&&l.has("offset")){const y=l;a=parseFloat(y.get("offset")),y.delete("offset")}});else if(_ instanceof Map&&_.has("offset")){const l=_;a=parseFloat(l.get("offset")),l.delete("offset")}return a}(hn.styles),tn=0;return null!=ur&&(R++,tn=hn.offset=ur),J=J||tn<0||tn>1,U=U||tn<de,de=tn,L.push(tn),hn});J&&l.errors.push(function jr(){return new C.wOt(3012,!1)}()),U&&l.errors.push(function Z(){return new C.wOt(3200,!1)}());const Ke=a.steps.length;let Pt=0;R>0&&R<Ke?l.errors.push(function te(){return new C.wOt(3202,!1)}()):0==R&&(Pt=1/(Ke-1));const It=Ke-1,rt=l.currentTime,Ct=l.currentAnimateTimings,mn=Ct.duration;return Be.forEach((Bn,hn)=>{const ur=Pt>0?hn==It?1:Pt*hn:L[hn],tn=ur*mn;l.currentTime=rt+Ct.delay+tn,Ct.duration=tn,this._validateStyleAst(Bn,l),Bn.offset=ur,y.styles.push(Bn)}),y}visitReference(a,l){return{type:8,animation:ct(this,Ie(a.animation),l),options:Rr(a.options)}}visitAnimateChild(a,l){return l.depCount++,{type:9,options:Rr(a.options)}}visitAnimateRef(a,l){return{type:10,animation:this.visitReference(a.animation,l),options:Rr(a.options)}}visitQuery(a,l){const y=l.currentQuerySelector,M=a.options||{};l.queryCount++,l.currentQuery=a;const[R,L]=function Li(_){const a=!!_.split(/\s*,\s*/).find(l=>":self"==l);return a&&(_=_.replace(Qn,"")),_=_.replace(/@\*/g,V).replace(/@\w+/g,l=>V+"-"+l.slice(1)).replace(/:animating/g,ye),[_,a]}(a.selector);l.currentQuerySelector=y.length?y+" "+R:R,rn(l.collectedStyles,l.currentQuerySelector,new Map);const U=ct(this,Ie(a.animation),l);return l.currentQuery=null,l.currentQuerySelector=y,{type:11,selector:R,limit:M.limit||0,optional:!!M.optional,includeSelf:L,animation:U,originalSelector:a.selector,options:Rr(a.options)}}visitStagger(a,l){l.currentQuery||l.errors.push(function Y(){return new C.wOt(3013,!1)}());const y="full"===a.timings?{duration:0,delay:0,easing:"full"}:Et(a.timings,l.errors,!0);return{type:12,animation:ct(this,Ie(a.animation),l),timings:y,options:null}}}class Yi{constructor(a){this.errors=a,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}}function Rr(_){return _?(_=en(_)).params&&(_.params=function jn(_){return _?en(_):null}(_.params)):_={},_}function eo(_,a,l){return{duration:_,delay:a,easing:l}}function Vi(_,a,l,y,M,R,L=null,U=!1){return{type:1,element:_,keyframes:a,preStyleProps:l,postStyleProps:y,duration:M,delay:R,totalTime:M+R,easing:L,subTimeline:U}}class fi{constructor(){this._map=new Map}get(a){return this._map.get(a)||[]}append(a,l){let y=this._map.get(a);y||this._map.set(a,y=[]),y.push(...l)}has(a){return this._map.has(a)}clear(){this._map.clear()}}const no=new RegExp(":enter","g"),Ho=new RegExp(":leave","g");function ei(_,a,l,y,M,R=new Map,L=new Map,U,J,de=[]){return(new ro).buildKeyframes(_,a,l,y,M,R,L,U,J,de)}class ro{buildKeyframes(a,l,y,M,R,L,U,J,de,Be=[]){de=de||new fi;const Ke=new go(a,l,de,M,R,Be,[]);Ke.options=J;const Pt=J.delay?xe(J.delay):0;Ke.currentTimeline.delayNextStep(Pt),Ke.currentTimeline.setStyles([L],null,Ke.errors,J),ct(this,y,Ke);const It=Ke.timelines.filter(rt=>rt.containsAnimation());if(It.length&&U.size){let rt;for(let Ct=It.length-1;Ct>=0;Ct--){const mn=It[Ct];if(mn.element===l){rt=mn;break}}rt&&!rt.allowOnlyTimelineStyles()&&rt.setStyles([U],null,Ke.errors,J)}return It.length?It.map(rt=>rt.buildKeyframes()):[Vi(l,[],[],[],0,Pt,"",!1)]}visitTrigger(a,l){}visitState(a,l){}visitTransition(a,l){}visitAnimateChild(a,l){const y=l.subInstructions.get(l.element);if(y){const M=l.createSubContext(a.options),R=l.currentTimeline.currentTime,L=this._visitSubInstructions(y,M,M.options);R!=L&&l.transformIntoNewTimeline(L)}l.previousNode=a}visitAnimateRef(a,l){const y=l.createSubContext(a.options);y.transformIntoNewTimeline(),this._applyAnimationRefDelays([a.options,a.animation.options],l,y),this.visitReference(a.animation,y),l.transformIntoNewTimeline(y.currentTimeline.currentTime),l.previousNode=a}_applyAnimationRefDelays(a,l,y){for(const M of a){const R=M?.delay;if(R){const L="number"==typeof R?R:xe(ke(R,M?.params??{},l.errors));y.delayNextStep(L)}}}_visitSubInstructions(a,l,y){let R=l.currentTimeline.currentTime;const L=null!=y.duration?xe(y.duration):null,U=null!=y.delay?xe(y.delay):null;return 0!==L&&a.forEach(J=>{const de=l.appendInstructionToTimeline(J,L,U);R=Math.max(R,de.duration+de.delay)}),R}visitReference(a,l){l.updateOptions(a.options,!0),ct(this,a.animation,l),l.previousNode=a}visitSequence(a,l){const y=l.subContextCount;let M=l;const R=a.options;if(R&&(R.params||R.delay)&&(M=l.createSubContext(R),M.transformIntoNewTimeline(),null!=R.delay)){6==M.previousNode.type&&(M.currentTimeline.snapshotCurrentStyles(),M.previousNode=Gr);const L=xe(R.delay);M.delayNextStep(L)}a.steps.length&&(a.steps.forEach(L=>ct(this,L,M)),M.currentTimeline.applyStylesToKeyframe(),M.subContextCount>y&&M.transformIntoNewTimeline()),l.previousNode=a}visitGroup(a,l){const y=[];let M=l.currentTimeline.currentTime;const R=a.options&&a.options.delay?xe(a.options.delay):0;a.steps.forEach(L=>{const U=l.createSubContext(a.options);R&&U.delayNextStep(R),ct(this,L,U),M=Math.max(M,U.currentTimeline.currentTime),y.push(U.currentTimeline)}),y.forEach(L=>l.currentTimeline.mergeTimelineCollectedStyles(L)),l.transformIntoNewTimeline(M),l.previousNode=a}_visitTiming(a,l){if(a.dynamic){const y=a.strValue;return Et(l.params?ke(y,l.params,l.errors):y,l.errors)}return{duration:a.duration,delay:a.delay,easing:a.easing}}visitAnimate(a,l){const y=l.currentAnimateTimings=this._visitTiming(a.timings,l),M=l.currentTimeline;y.delay&&(l.incrementTime(y.delay),M.snapshotCurrentStyles());const R=a.style;5==R.type?this.visitKeyframes(R,l):(l.incrementTime(y.duration),this.visitStyle(R,l),M.applyStylesToKeyframe()),l.currentAnimateTimings=null,l.previousNode=a}visitStyle(a,l){const y=l.currentTimeline,M=l.currentAnimateTimings;!M&&y.hasCurrentStyleProperties()&&y.forwardFrame();const R=M&&M.easing||a.easing;a.isEmptyStep?y.applyEmptyStep(R):y.setStyles(a.styles,R,l.errors,l.options),l.previousNode=a}visitKeyframes(a,l){const y=l.currentAnimateTimings,M=l.currentTimeline.duration,R=y.duration,U=l.createSubContext().currentTimeline;U.easing=y.easing,a.styles.forEach(J=>{U.forwardTime((J.offset||0)*R),U.setStyles(J.styles,J.easing,l.errors,l.options),U.applyStylesToKeyframe()}),l.currentTimeline.mergeTimelineCollectedStyles(U),l.transformIntoNewTimeline(M+R),l.previousNode=a}visitQuery(a,l){const y=l.currentTimeline.currentTime,M=a.options||{},R=M.delay?xe(M.delay):0;R&&(6===l.previousNode.type||0==y&&l.currentTimeline.hasCurrentStyleProperties())&&(l.currentTimeline.snapshotCurrentStyles(),l.previousNode=Gr);let L=y;const U=l.invokeQuery(a.selector,a.originalSelector,a.limit,a.includeSelf,!!M.optional,l.errors);l.currentQueryTotal=U.length;let J=null;U.forEach((de,Be)=>{l.currentQueryIndex=Be;const Ke=l.createSubContext(a.options,de);R&&Ke.delayNextStep(R),de===l.element&&(J=Ke.currentTimeline),ct(this,a.animation,Ke),Ke.currentTimeline.applyStylesToKeyframe(),L=Math.max(L,Ke.currentTimeline.currentTime)}),l.currentQueryIndex=0,l.currentQueryTotal=0,l.transformIntoNewTimeline(L),J&&(l.currentTimeline.mergeTimelineCollectedStyles(J),l.currentTimeline.snapshotCurrentStyles()),l.previousNode=a}visitStagger(a,l){const y=l.parentContext,M=l.currentTimeline,R=a.timings,L=Math.abs(R.duration),U=L*(l.currentQueryTotal-1);let J=L*l.currentQueryIndex;switch(R.duration<0?"reverse":R.easing){case"reverse":J=U-J;break;case"full":J=y.currentStaggerTime}const Be=l.currentTimeline;J&&Be.delayNextStep(J);const Ke=Be.currentTime;ct(this,a.animation,l),l.previousNode=a,y.currentStaggerTime=M.currentTime-Ke+(M.startTime-y.currentTimeline.startTime)}}const Gr={};class go{constructor(a,l,y,M,R,L,U,J){this._driver=a,this.element=l,this.subInstructions=y,this._enterClassName=M,this._leaveClassName=R,this.errors=L,this.timelines=U,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=Gr,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=J||new ti(this._driver,l,0),U.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(a,l){if(!a)return;const y=a;let M=this.options;null!=y.duration&&(M.duration=xe(y.duration)),null!=y.delay&&(M.delay=xe(y.delay));const R=y.params;if(R){let L=M.params;L||(L=this.options.params={}),Object.keys(R).forEach(U=>{(!l||!L.hasOwnProperty(U))&&(L[U]=ke(R[U],L,this.errors))})}}_copyOptions(){const a={};if(this.options){const l=this.options.params;if(l){const y=a.params={};Object.keys(l).forEach(M=>{y[M]=l[M]})}}return a}createSubContext(a=null,l,y){const M=l||this.element,R=new go(this._driver,M,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(M,y||0));return R.previousNode=this.previousNode,R.currentAnimateTimings=this.currentAnimateTimings,R.options=this._copyOptions(),R.updateOptions(a),R.currentQueryIndex=this.currentQueryIndex,R.currentQueryTotal=this.currentQueryTotal,R.parentContext=this,this.subContextCount++,R}transformIntoNewTimeline(a){return this.previousNode=Gr,this.currentTimeline=this.currentTimeline.fork(this.element,a),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(a,l,y){const M={duration:l??a.duration,delay:this.currentTimeline.currentTime+(y??0)+a.delay,easing:""},R=new ni(this._driver,a.element,a.keyframes,a.preStyleProps,a.postStyleProps,M,a.stretchStartingKeyframe);return this.timelines.push(R),M}incrementTime(a){this.currentTimeline.forwardTime(this.currentTimeline.duration+a)}delayNextStep(a){a>0&&this.currentTimeline.delayNextStep(a)}invokeQuery(a,l,y,M,R,L){let U=[];if(M&&U.push(this.element),a.length>0){a=(a=a.replace(no,"."+this._enterClassName)).replace(Ho,"."+this._leaveClassName);let de=this._driver.query(this.element,a,1!=y);0!==y&&(de=y<0?de.slice(de.length+y,de.length):de.slice(0,y)),U.push(...de)}return!R&&0==U.length&&L.push(function le(_){return new C.wOt(3014,!1)}()),U}}class ti{constructor(a,l,y,M){this._driver=a,this.element=l,this.startTime=y,this._elementTimelineStylesLookup=M,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(l),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(l,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(a){const l=1===this._keyframes.size&&this._pendingStyles.size;this.duration||l?(this.forwardTime(this.currentTime+a),l&&this.snapshotCurrentStyles()):this.startTime+=a}fork(a,l){return this.applyStylesToKeyframe(),new ti(this._driver,a,l||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=1,this._loadKeyframe()}forwardTime(a){this.applyStylesToKeyframe(),this.duration=a,this._loadKeyframe()}_updateStyle(a,l){this._localTimelineStyles.set(a,l),this._globalTimelineStyles.set(a,l),this._styleSummary.set(a,{time:this.currentTime,value:l})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(a){a&&this._previousKeyframe.set("easing",a);for(let[l,y]of this._globalTimelineStyles)this._backFill.set(l,y||Q),this._currentKeyframe.set(l,Q);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(a,l,y,M){l&&this._previousKeyframe.set("easing",l);const R=M&&M.params||{},L=function hi(_,a){const l=new Map;let y;return _.forEach(M=>{if("*"===M){y=y||a.keys();for(let R of y)l.set(R,Q)}else j(M,l)}),l}(a,this._globalTimelineStyles);for(let[U,J]of L){const de=ke(J,R,y);this._pendingStyles.set(U,de),this._localTimelineStyles.has(U)||this._backFill.set(U,this._globalTimelineStyles.get(U)??Q),this._updateStyle(U,de)}}applyStylesToKeyframe(){0!=this._pendingStyles.size&&(this._pendingStyles.forEach((a,l)=>{this._currentKeyframe.set(l,a)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((a,l)=>{this._currentKeyframe.has(l)||this._currentKeyframe.set(l,a)}))}snapshotCurrentStyles(){for(let[a,l]of this._localTimelineStyles)this._pendingStyles.set(a,l),this._updateStyle(a,l)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){const a=[];for(let l in this._currentKeyframe)a.push(l);return a}mergeTimelineCollectedStyles(a){a._styleSummary.forEach((l,y)=>{const M=this._styleSummary.get(y);(!M||l.time>M.time)&&this._updateStyle(y,l.value)})}buildKeyframes(){this.applyStylesToKeyframe();const a=new Set,l=new Set,y=1===this._keyframes.size&&0===this.duration;let M=[];this._keyframes.forEach((U,J)=>{const de=j(U,new Map,this._backFill);de.forEach((Be,Ke)=>{"!"===Be?a.add(Ke):Be===Q&&l.add(Ke)}),y||de.set("offset",J/this.duration),M.push(de)});const R=a.size?dn(a.values()):[],L=l.size?dn(l.values()):[];if(y){const U=M[0],J=new Map(U);U.set("offset",0),J.set("offset",1),M=[U,J]}return Vi(this.element,M,R,L,this.duration,this.startTime,this.easing,!1)}}class ni extends ti{constructor(a,l,y,M,R,L,U=!1){super(a,l,L.delay),this.keyframes=y,this.preStyleProps=M,this.postStyleProps=R,this._stretchStartingKeyframe=U,this.timings={duration:L.duration,delay:L.delay,easing:L.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let a=this.keyframes,{delay:l,duration:y,easing:M}=this.timings;if(this._stretchStartingKeyframe&&l){const R=[],L=y+l,U=l/L,J=j(a[0]);J.set("offset",0),R.push(J);const de=j(a[0]);de.set("offset",ji(U)),R.push(de);const Be=a.length-1;for(let Ke=1;Ke<=Be;Ke++){let Pt=j(a[Ke]);const It=Pt.get("offset");Pt.set("offset",ji((l+It*y)/L)),R.push(Pt)}y=L,l=0,M="",a=R}return Vi(this.element,a,this.preStyleProps,this.postStyleProps,y,l,M,!0)}}function ji(_,a=3){const l=Math.pow(10,a-1);return Math.round(_*l)/l}class pt{}const wn=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]);class xr extends pt{normalizePropertyName(a,l){return Dn(a)}normalizeStyleValue(a,l,y,M){let R="";const L=y.toString().trim();if(wn.has(l)&&0!==y&&"0"!==y)if("number"==typeof y)R="px";else{const U=y.match(/^[+-]?[\d\.]+([a-z]*)$/);U&&0==U[1].length&&M.push(function jt(_,a){return new C.wOt(3005,!1)}())}return L+R}}function Pn(_,a,l,y,M,R,L,U,J,de,Be,Ke,Pt){return{type:0,element:_,triggerName:a,isRemovalTransition:M,fromState:l,fromStyles:R,toState:y,toStyles:L,timelines:U,queriedElements:J,preStyleProps:de,postStyleProps:Be,totalTime:Ke,errors:Pt}}const br={};class Ui{constructor(a,l,y){this._triggerName=a,this.ast=l,this._stateStyles=y}match(a,l,y,M){return function Bi(_,a,l,y,M){return _.some(R=>R(a,l,y,M))}(this.ast.matchers,a,l,y,M)}buildStyles(a,l,y){let M=this._stateStyles.get("*");return void 0!==a&&(M=this._stateStyles.get(a?.toString())||M),M?M.buildStyles(l,y):new Map}build(a,l,y,M,R,L,U,J,de,Be){const Ke=[],Pt=this.ast.options&&this.ast.options.params||br,rt=this.buildStyles(y,U&&U.params||br,Ke),Ct=J&&J.params||br,mn=this.buildStyles(M,Ct,Ke),Bn=new Set,hn=new Map,ur=new Map,tn="void"===M,Jt={params:oo(Ct,Pt),delay:this.ast.options?.delay},Vr=Be?[]:ei(a,l,this.ast.animation,R,L,rt,mn,Jt,de,Ke);let Rn=0;if(Vr.forEach(Oi=>{Rn=Math.max(Oi.duration+Oi.delay,Rn)}),Ke.length)return Pn(l,this._triggerName,y,M,tn,rt,mn,[],[],hn,ur,Rn,Ke);Vr.forEach(Oi=>{const oi=Oi.element,_o=rn(hn,oi,new Set);Oi.preStyleProps.forEach(uo=>_o.add(uo));const cr=rn(ur,oi,new Set);Oi.postStyleProps.forEach(uo=>cr.add(uo)),oi!==l&&Bn.add(oi)});const bn=dn(Bn.values());return Pn(l,this._triggerName,y,M,tn,rt,mn,Vr,bn,hn,ur,Rn)}}function oo(_,a){const l=en(a);for(const y in _)_.hasOwnProperty(y)&&null!=_[y]&&(l[y]=_[y]);return l}class an{constructor(a,l,y){this.styles=a,this.defaultParams=l,this.normalizer=y}buildStyles(a,l){const y=new Map,M=en(this.defaultParams);return Object.keys(a).forEach(R=>{const L=a[R];null!==L&&(M[R]=L)}),this.styles.styles.forEach(R=>{"string"!=typeof R&&R.forEach((L,U)=>{L&&(L=ke(L,M,l));const J=this.normalizer.normalizePropertyName(U,l);L=this.normalizer.normalizeStyleValue(U,J,L,l),y.set(U,L)})}),y}}class mt{constructor(a,l,y){this.name=a,this.ast=l,this._normalizer=y,this.transitionFactories=[],this.states=new Map,l.states.forEach(M=>{this.states.set(M.name,new an(M.style,M.options&&M.options.params||{},y))}),qn(this.states,"true","1"),qn(this.states,"false","0"),l.transitions.forEach(M=>{this.transitionFactories.push(new Ui(a,M,this.states))}),this.fallbackTransition=function on(_,a,l){return new Ui(_,{type:1,animation:{type:2,steps:[],options:null},matchers:[(L,U)=>!0],options:null,queryCount:0,depCount:0},a)}(a,this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(a,l,y,M){return this.transitionFactories.find(L=>L.match(a,l,y,M))||null}matchStyles(a,l,y){return this.fallbackTransition.buildStyles(a,l,y)}}function qn(_,a,l){_.has(a)?_.has(l)||_.set(l,_.get(a)):_.has(l)&&_.set(a,_.get(l))}const $i=new fi;class Tn{constructor(a,l,y){this.bodyNode=a,this._driver=l,this._normalizer=y,this._animations=new Map,this._playersById=new Map,this.players=[]}register(a,l){const y=[],R=mr(this._driver,l,y,[]);if(y.length)throw function xt(_){return new C.wOt(3503,!1)}();this._animations.set(a,R)}_buildPlayer(a,l,y){const M=a.element,R=st(this._normalizer,a.keyframes,l,y);return this._driver.animate(M,R,a.duration,a.delay,a.easing,[],!0)}create(a,l,y={}){const M=[],R=this._animations.get(a);let L;const U=new Map;if(R?(L=ei(this._driver,l,R,or,ce,new Map,new Map,y,$i,M),L.forEach(Be=>{const Ke=rn(U,Be.element,new Map);Be.postStyleProps.forEach(Pt=>Ke.set(Pt,null))})):(M.push(function qt(){return new C.wOt(3300,!1)}()),L=[]),M.length)throw function rr(_){return new C.wOt(3504,!1)}();U.forEach((Be,Ke)=>{Be.forEach((Pt,It)=>{Be.set(It,this._driver.computeStyle(Ke,It,Q))})});const de=ht(L.map(Be=>{const Ke=U.get(Be.element);return this._buildPlayer(Be,new Map,Ke)}));return this._playersById.set(a,de),de.onDestroy(()=>this.destroy(a)),this.players.push(de),de}destroy(a){const l=this._getPlayer(a);l.destroy(),this._playersById.delete(a);const y=this.players.indexOf(l);y>=0&&this.players.splice(y,1)}_getPlayer(a){const l=this._playersById.get(a);if(!l)throw function yt(_){return new C.wOt(3301,!1)}();return l}listen(a,l,y,M){const R=vn(l,"","","");return wr(this._getPlayer(a),y,R,M),()=>{}}command(a,l,y,M){if("register"==y)return void this.register(a,M[0]);if("create"==y)return void this.create(a,l,M[0]||{});const R=this._getPlayer(a);switch(y){case"play":R.play();break;case"pause":R.pause();break;case"reset":R.reset();break;case"restart":R.restart();break;case"finish":R.finish();break;case"init":R.init();break;case"setPosition":R.setPosition(parseFloat(M[0]));break;case"destroy":this.destroy(a)}}}const Mr="ng-animate-queued",Zn="ng-animate-disabled",Hi=[],Wr={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},fn={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},xn="__ng_removed";class ar{get params(){return this.options.params}constructor(a,l=""){this.namespaceId=l;const y=a&&a.hasOwnProperty("value");if(this.value=function zi(_){return _??null}(y?a.value:a),y){const R=en(a);delete R.value,this.options=R}else this.options={};this.options.params||(this.options.params={})}absorbOptions(a){const l=a.params;if(l){const y=this.options.params;Object.keys(l).forEach(M=>{null==y[M]&&(y[M]=l[M])})}}}const Fn="void",gi=new ar(Fn);class Ro{constructor(a,l,y){this.id=a,this.hostElement=l,this._engine=y,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+a,An(l,this._hostClassName)}listen(a,l,y,M){if(!this._triggers.has(l))throw function yn(_,a){return new C.wOt(3302,!1)}();if(null==y||0==y.length)throw function Gt(_){return new C.wOt(3303,!1)}();if(!function zo(_){return"start"==_||"done"==_}(y))throw function qe(_,a){return new C.wOt(3400,!1)}();const R=rn(this._elementListeners,a,[]),L={name:l,phase:y,callback:M};R.push(L);const U=rn(this._engine.statesByElement,a,new Map);return U.has(l)||(An(a,q),An(a,q+"-"+l),U.set(l,gi)),()=>{this._engine.afterFlush(()=>{const J=R.indexOf(L);J>=0&&R.splice(J,1),this._triggers.has(l)||U.delete(l)})}}register(a,l){return!this._triggers.has(a)&&(this._triggers.set(a,l),!0)}_getTrigger(a){const l=this._triggers.get(a);if(!l)throw function Xe(_){return new C.wOt(3401,!1)}();return l}trigger(a,l,y,M=!0){const R=this._getTrigger(l),L=new kr(this.id,l,a);let U=this._engine.statesByElement.get(a);U||(An(a,q),An(a,q+"-"+l),this._engine.statesByElement.set(a,U=new Map));let J=U.get(l);const de=new ar(y,this.id);if(!(y&&y.hasOwnProperty("value"))&&J&&de.absorbOptions(J.options),U.set(l,de),J||(J=gi),de.value!==Fn&&J.value===de.value){if(!function On(_,a){const l=Object.keys(_),y=Object.keys(a);if(l.length!=y.length)return!1;for(let M=0;M<l.length;M++){const R=l[M];if(!a.hasOwnProperty(R)||_[R]!==a[R])return!1}return!0}(J.params,de.params)){const Ct=[],mn=R.matchStyles(J.value,J.params,Ct),Bn=R.matchStyles(de.value,de.params,Ct);Ct.length?this._engine.reportError(Ct):this._engine.afterFlush(()=>{he(a,mn),$(a,Bn)})}return}const Pt=rn(this._engine.playersByElement,a,[]);Pt.forEach(Ct=>{Ct.namespaceId==this.id&&Ct.triggerName==l&&Ct.queued&&Ct.destroy()});let It=R.matchTransition(J.value,de.value,a,de.params),rt=!1;if(!It){if(!M)return;It=R.fallbackTransition,rt=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:a,triggerName:l,transition:It,fromState:J,toState:de,player:L,isFallbackTransition:rt}),rt||(An(a,Mr),L.onStart(()=>{Dr(a,Mr)})),L.onDone(()=>{let Ct=this.players.indexOf(L);Ct>=0&&this.players.splice(Ct,1);const mn=this._engine.playersByElement.get(a);if(mn){let Bn=mn.indexOf(L);Bn>=0&&mn.splice(Bn,1)}}),this.players.push(L),Pt.push(L),L}deregister(a){this._triggers.delete(a),this._engine.statesByElement.forEach(l=>l.delete(a)),this._elementListeners.forEach((l,y)=>{this._elementListeners.set(y,l.filter(M=>M.name!=a))})}clearElementCache(a){this._engine.statesByElement.delete(a),this._elementListeners.delete(a);const l=this._engine.playersByElement.get(a);l&&(l.forEach(y=>y.destroy()),this._engine.playersByElement.delete(a))}_signalRemovalForInnerTriggers(a,l){const y=this._engine.driver.query(a,V,!0);y.forEach(M=>{if(M[xn])return;const R=this._engine.fetchNamespacesByElement(M);R.size?R.forEach(L=>L.triggerLeaveAnimation(M,l,!1,!0)):this.clearElementCache(M)}),this._engine.afterFlushAnimationsDone(()=>y.forEach(M=>this.clearElementCache(M)))}triggerLeaveAnimation(a,l,y,M){const R=this._engine.statesByElement.get(a),L=new Map;if(R){const U=[];if(R.forEach((J,de)=>{if(L.set(de,J.value),this._triggers.has(de)){const Be=this.trigger(a,de,Fn,M);Be&&U.push(Be)}}),U.length)return this._engine.markElementAsRemoved(this.id,a,!0,l,L),y&&ht(U).onDone(()=>this._engine.processLeaveNode(a)),!0}return!1}prepareLeaveAnimationListeners(a){const l=this._elementListeners.get(a),y=this._engine.statesByElement.get(a);if(l&&y){const M=new Set;l.forEach(R=>{const L=R.name;if(M.has(L))return;M.add(L);const J=this._triggers.get(L).fallbackTransition,de=y.get(L)||gi,Be=new ar(Fn),Ke=new kr(this.id,L,a);this._engine.totalQueuedPlayers++,this._queue.push({element:a,triggerName:L,transition:J,fromState:de,toState:Be,player:Ke,isFallbackTransition:!0})})}}removeNode(a,l){const y=this._engine;if(a.childElementCount&&this._signalRemovalForInnerTriggers(a,l),this.triggerLeaveAnimation(a,l,!0))return;let M=!1;if(y.totalAnimations){const R=y.players.length?y.playersByQueriedElement.get(a):[];if(R&&R.length)M=!0;else{let L=a;for(;L=L.parentNode;)if(y.statesByElement.get(L)){M=!0;break}}}if(this.prepareLeaveAnimationListeners(a),M)y.markElementAsRemoved(this.id,a,!1,l);else{const R=a[xn];(!R||R===Wr)&&(y.afterFlush(()=>this.clearElementCache(a)),y.destroyInnerAnimations(a),y._onRemovalComplete(a,l))}}insertNode(a,l){An(a,this._hostClassName)}drainQueuedTransitions(a){const l=[];return this._queue.forEach(y=>{const M=y.player;if(M.destroyed)return;const R=y.element,L=this._elementListeners.get(R);L&&L.forEach(U=>{if(U.name==y.triggerName){const J=vn(R,y.triggerName,y.fromState.value,y.toState.value);J._data=a,wr(y.player,U.phase,J,U.callback)}}),M.markedForDestroy?this._engine.afterFlush(()=>{M.destroy()}):l.push(y)}),this._queue=[],l.sort((y,M)=>{const R=y.transition.ast.depCount,L=M.transition.ast.depCount;return 0==R||0==L?R-L:this._engine.driver.containsElement(y.element,M.element)?1:-1})}destroy(a){this.players.forEach(l=>l.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,a)}}class Kr{_onRemovalComplete(a,l){this.onRemovalComplete(a,l)}constructor(a,l,y){this.bodyNode=a,this.driver=l,this._normalizer=y,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(M,R)=>{}}get queuedPlayers(){const a=[];return this._namespaceList.forEach(l=>{l.players.forEach(y=>{y.queued&&a.push(y)})}),a}createNamespace(a,l){const y=new Ro(a,l,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,l)?this._balanceNamespaceList(y,l):(this.newHostElements.set(l,y),this.collectEnterElement(l)),this._namespaceLookup[a]=y}_balanceNamespaceList(a,l){const y=this._namespaceList,M=this.namespacesByHostElement;if(y.length-1>=0){let L=!1,U=this.driver.getParentElement(l);for(;U;){const J=M.get(U);if(J){const de=y.indexOf(J);y.splice(de+1,0,a),L=!0;break}U=this.driver.getParentElement(U)}L||y.unshift(a)}else y.push(a);return M.set(l,a),a}register(a,l){let y=this._namespaceLookup[a];return y||(y=this.createNamespace(a,l)),y}registerTrigger(a,l,y){let M=this._namespaceLookup[a];M&&M.register(l,y)&&this.totalAnimations++}destroy(a,l){a&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{const y=this._fetchNamespace(a);this.namespacesByHostElement.delete(y.hostElement);const M=this._namespaceList.indexOf(y);M>=0&&this._namespaceList.splice(M,1),y.destroy(l),delete this._namespaceLookup[a]}))}_fetchNamespace(a){return this._namespaceLookup[a]}fetchNamespacesByElement(a){const l=new Set,y=this.statesByElement.get(a);if(y)for(let M of y.values())if(M.namespaceId){const R=this._fetchNamespace(M.namespaceId);R&&l.add(R)}return l}trigger(a,l,y,M){if(wt(l)){const R=this._fetchNamespace(a);if(R)return R.trigger(l,y,M),!0}return!1}insertNode(a,l,y,M){if(!wt(l))return;const R=l[xn];if(R&&R.setForRemoval){R.setForRemoval=!1,R.setForMove=!0;const L=this.collectedLeaveElements.indexOf(l);L>=0&&this.collectedLeaveElements.splice(L,1)}if(a){const L=this._fetchNamespace(a);L&&L.insertNode(l,y)}M&&this.collectEnterElement(l)}collectEnterElement(a){this.collectedEnterElements.push(a)}markElementAsDisabled(a,l){l?this.disabledNodes.has(a)||(this.disabledNodes.add(a),An(a,Zn)):this.disabledNodes.has(a)&&(this.disabledNodes.delete(a),Dr(a,Zn))}removeNode(a,l,y){if(wt(l)){const M=a?this._fetchNamespace(a):null;M?M.removeNode(l,y):this.markElementAsRemoved(a,l,!1,y);const R=this.namespacesByHostElement.get(l);R&&R.id!==a&&R.removeNode(l,y)}else this._onRemovalComplete(l,y)}markElementAsRemoved(a,l,y,M,R){this.collectedLeaveElements.push(l),l[xn]={namespaceId:a,setForRemoval:M,hasAnimation:y,removedBeforeQueried:!1,previousTriggersValues:R}}listen(a,l,y,M,R){return wt(l)?this._fetchNamespace(a).listen(l,y,M,R):()=>{}}_buildInstruction(a,l,y,M,R){return a.transition.build(this.driver,a.element,a.fromState.value,a.toState.value,y,M,a.fromState.options,a.toState.options,l,R)}destroyInnerAnimations(a){let l=this.driver.query(a,V,!0);l.forEach(y=>this.destroyActiveAnimationsForElement(y)),0!=this.playersByQueriedElement.size&&(l=this.driver.query(a,ye,!0),l.forEach(y=>this.finishActiveQueriedAnimationOnElement(y)))}destroyActiveAnimationsForElement(a){const l=this.playersByElement.get(a);l&&l.forEach(y=>{y.queued?y.markedForDestroy=!0:y.destroy()})}finishActiveQueriedAnimationOnElement(a){const l=this.playersByQueriedElement.get(a);l&&l.forEach(y=>y.finish())}whenRenderingDone(){return new Promise(a=>{if(this.players.length)return ht(this.players).onDone(()=>a());a()})}processLeaveNode(a){const l=a[xn];if(l&&l.setForRemoval){if(a[xn]=Wr,l.namespaceId){this.destroyInnerAnimations(a);const y=this._fetchNamespace(l.namespaceId);y&&y.clearElementCache(a)}this._onRemovalComplete(a,l.setForRemoval)}a.classList?.contains(Zn)&&this.markElementAsDisabled(a,!1),this.driver.query(a,".ng-animate-disabled",!0).forEach(y=>{this.markElementAsDisabled(y,!1)})}flush(a=-1){let l=[];if(this.newHostElements.size&&(this.newHostElements.forEach((y,M)=>this._balanceNamespaceList(y,M)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let y=0;y<this.collectedEnterElements.length;y++)An(this.collectedEnterElements[y],"ng-star-inserted");if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){const y=[];try{l=this._flushAnimations(y,a)}finally{for(let M=0;M<y.length;M++)y[M]()}}else for(let y=0;y<this.collectedLeaveElements.length;y++)this.processLeaveNode(this.collectedLeaveElements[y]);if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(y=>y()),this._flushFns=[],this._whenQuietFns.length){const y=this._whenQuietFns;this._whenQuietFns=[],l.length?ht(l).onDone(()=>{y.forEach(M=>M())}):y.forEach(M=>M())}}reportError(a){throw function Vn(_){return new C.wOt(3402,!1)}()}_flushAnimations(a,l){const y=new fi,M=[],R=new Map,L=[],U=new Map,J=new Map,de=new Map,Be=new Set;this.disabledNodes.forEach(Qe=>{Be.add(Qe);const at=this.driver.query(Qe,".ng-animate-queued",!0);for(let it=0;it<at.length;it++)Be.add(at[it])});const Ke=this.bodyNode,Pt=Array.from(this.statesByElement.keys()),It=Sr(Pt,this.collectedEnterElements),rt=new Map;let Ct=0;It.forEach((Qe,at)=>{const it=or+Ct++;rt.set(at,it),Qe.forEach(Bt=>An(Bt,it))});const mn=[],Bn=new Set,hn=new Set;for(let Qe=0;Qe<this.collectedLeaveElements.length;Qe++){const at=this.collectedLeaveElements[Qe],it=at[xn];it&&it.setForRemoval&&(mn.push(at),Bn.add(at),it.hasAnimation?this.driver.query(at,".ng-star-inserted",!0).forEach(Bt=>Bn.add(Bt)):hn.add(at))}const ur=new Map,tn=Sr(Pt,Array.from(Bn));tn.forEach((Qe,at)=>{const it=ce+Ct++;ur.set(at,it),Qe.forEach(Bt=>An(Bt,it))}),a.push(()=>{It.forEach((Qe,at)=>{const it=rt.get(at);Qe.forEach(Bt=>Dr(Bt,it))}),tn.forEach((Qe,at)=>{const it=ur.get(at);Qe.forEach(Bt=>Dr(Bt,it))}),mn.forEach(Qe=>{this.processLeaveNode(Qe)})});const Jt=[],Vr=[];for(let Qe=this._namespaceList.length-1;Qe>=0;Qe--)this._namespaceList[Qe].drainQueuedTransitions(l).forEach(it=>{const Bt=it.player,Yn=it.element;if(Jt.push(Bt),this.collectedEnterElements.length){const dr=Yn[xn];if(dr&&dr.setForMove){if(dr.previousTriggersValues&&dr.previousTriggersValues.has(it.triggerName)){const Eo=dr.previousTriggersValues.get(it.triggerName),Tr=this.statesByElement.get(it.element);if(Tr&&Tr.has(it.triggerName)){const Ni=Tr.get(it.triggerName);Ni.value=Eo,Tr.set(it.triggerName,Ni)}}return void Bt.destroy()}}const vi=!Ke||!this.driver.containsElement(Ke,Yn),Qr=ur.get(Yn),ot=rt.get(Yn),_n=this._buildInstruction(it,y,ot,Qr,vi);if(_n.errors&&_n.errors.length)return void Vr.push(_n);if(vi)return Bt.onStart(()=>he(Yn,_n.fromStyles)),Bt.onDestroy(()=>$(Yn,_n.toStyles)),void M.push(Bt);if(it.isFallbackTransition)return Bt.onStart(()=>he(Yn,_n.fromStyles)),Bt.onDestroy(()=>$(Yn,_n.toStyles)),void M.push(Bt);const fs=[];_n.timelines.forEach(dr=>{dr.stretchStartingKeyframe=!0,this.disabledNodes.has(dr.element)||fs.push(dr)}),_n.timelines=fs,y.append(Yn,_n.timelines),L.push({instruction:_n,player:Bt,element:Yn}),_n.queriedElements.forEach(dr=>rn(U,dr,[]).push(Bt)),_n.preStyleProps.forEach((dr,Eo)=>{if(dr.size){let Tr=J.get(Eo);Tr||J.set(Eo,Tr=new Set),dr.forEach((Ni,Ts)=>Tr.add(Ts))}}),_n.postStyleProps.forEach((dr,Eo)=>{let Tr=de.get(Eo);Tr||de.set(Eo,Tr=new Set),dr.forEach((Ni,Ts)=>Tr.add(Ts))})});if(Vr.length){const Qe=[];Vr.forEach(at=>{Qe.push(function Nt(_,a){return new C.wOt(3505,!1)}())}),Jt.forEach(at=>at.destroy()),this.reportError(Qe)}const Rn=new Map,bn=new Map;L.forEach(Qe=>{const at=Qe.element;y.has(at)&&(bn.set(at,at),this._beforeAnimationBuild(Qe.player.namespaceId,Qe.instruction,Rn))}),M.forEach(Qe=>{const at=Qe.element;this._getPreviousPlayers(at,!1,Qe.namespaceId,Qe.triggerName,null).forEach(Bt=>{rn(Rn,at,[]).push(Bt),Bt.destroy()})});const Oi=mn.filter(Qe=>kn(Qe,J,de)),oi=new Map;vr(oi,this.driver,hn,de,Q).forEach(Qe=>{kn(Qe,J,de)&&Oi.push(Qe)});const cr=new Map;It.forEach((Qe,at)=>{vr(cr,this.driver,new Set(Qe),J,"!")}),Oi.forEach(Qe=>{const at=oi.get(Qe),it=cr.get(Qe);oi.set(Qe,new Map([...at?.entries()??[],...it?.entries()??[]]))});const uo=[],qo=[],ds={};L.forEach(Qe=>{const{element:at,player:it,instruction:Bt}=Qe;if(y.has(at)){if(Be.has(at))return it.onDestroy(()=>$(at,Bt.toStyles)),it.disabled=!0,it.overrideTotalTime(Bt.totalTime),void M.push(it);let Yn=ds;if(bn.size>1){let Qr=at;const ot=[];for(;Qr=Qr.parentNode;){const _n=bn.get(Qr);if(_n){Yn=_n;break}ot.push(Qr)}ot.forEach(_n=>bn.set(_n,Yn))}const vi=this._buildAnimation(it.namespaceId,Bt,Rn,R,cr,oi);if(it.setRealPlayer(vi),Yn===ds)uo.push(it);else{const Qr=this.playersByElement.get(Yn);Qr&&Qr.length&&(it.parentPlayer=ht(Qr)),M.push(it)}}else he(at,Bt.fromStyles),it.onDestroy(()=>$(at,Bt.toStyles)),qo.push(it),Be.has(at)&&M.push(it)}),qo.forEach(Qe=>{const at=R.get(Qe.element);if(at&&at.length){const it=ht(at);Qe.setRealPlayer(it)}}),M.forEach(Qe=>{Qe.parentPlayer?Qe.syncPlayerEvents(Qe.parentPlayer):Qe.destroy()});for(let Qe=0;Qe<mn.length;Qe++){const at=mn[Qe],it=at[xn];if(Dr(at,ce),it&&it.hasAnimation)continue;let Bt=[];if(U.size){let vi=U.get(at);vi&&vi.length&&Bt.push(...vi);let Qr=this.driver.query(at,ye,!0);for(let ot=0;ot<Qr.length;ot++){let _n=U.get(Qr[ot]);_n&&_n.length&&Bt.push(..._n)}}const Yn=Bt.filter(vi=>!vi.destroyed);Yn.length?Si(this,at,Yn):this.processLeaveNode(at)}return mn.length=0,uo.forEach(Qe=>{this.players.push(Qe),Qe.onDone(()=>{Qe.destroy();const at=this.players.indexOf(Qe);this.players.splice(at,1)}),Qe.play()}),uo}afterFlush(a){this._flushFns.push(a)}afterFlushAnimationsDone(a){this._whenQuietFns.push(a)}_getPreviousPlayers(a,l,y,M,R){let L=[];if(l){const U=this.playersByQueriedElement.get(a);U&&(L=U)}else{const U=this.playersByElement.get(a);if(U){const J=!R||R==Fn;U.forEach(de=>{de.queued||!J&&de.triggerName!=M||L.push(de)})}}return(y||M)&&(L=L.filter(U=>!(y&&y!=U.namespaceId||M&&M!=U.triggerName))),L}_beforeAnimationBuild(a,l,y){const R=l.element,L=l.isRemovalTransition?void 0:a,U=l.isRemovalTransition?void 0:l.triggerName;for(const J of l.timelines){const de=J.element,Be=de!==R,Ke=rn(y,de,[]);this._getPreviousPlayers(de,Be,L,U,l.toState).forEach(It=>{const rt=It.getRealPlayer();rt.beforeDestroy&&rt.beforeDestroy(),It.destroy(),Ke.push(It)})}he(R,l.fromStyles)}_buildAnimation(a,l,y,M,R,L){const U=l.triggerName,J=l.element,de=[],Be=new Set,Ke=new Set,Pt=l.timelines.map(rt=>{const Ct=rt.element;Be.add(Ct);const mn=Ct[xn];if(mn&&mn.removedBeforeQueried)return new Vt(rt.duration,rt.delay);const Bn=Ct!==J,hn=function Un(_){const a=[];return xo(_,a),a}((y.get(Ct)||Hi).map(Rn=>Rn.getRealPlayer())).filter(Rn=>!!Rn.element&&Rn.element===Ct),ur=R.get(Ct),tn=L.get(Ct),Jt=st(this._normalizer,rt.keyframes,ur,tn),Vr=this._buildPlayer(rt,Jt,hn);if(rt.subTimeline&&M&&Ke.add(Ct),Bn){const Rn=new kr(a,U,Ct);Rn.setRealPlayer(Vr),de.push(Rn)}return Vr});de.forEach(rt=>{rn(this.playersByQueriedElement,rt.element,[]).push(rt),rt.onDone(()=>function Mi(_,a,l){let y=_.get(a);if(y){if(y.length){const M=y.indexOf(l);y.splice(M,1)}0==y.length&&_.delete(a)}return y}(this.playersByQueriedElement,rt.element,rt))}),Be.forEach(rt=>An(rt,W));const It=ht(Pt);return It.onDestroy(()=>{Be.forEach(rt=>Dr(rt,W)),$(J,l.toStyles)}),Ke.forEach(rt=>{rn(M,rt,[]).push(It)}),It}_buildPlayer(a,l,y){return l.length>0?this.driver.animate(a.element,l,a.duration,a.delay,a.easing,y):new Vt(a.duration,a.delay)}}class kr{constructor(a,l,y){this.namespaceId=a,this.triggerName=l,this.element=y,this._player=new Vt,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(a){this._containsRealPlayer||(this._player=a,this._queuedCallbacks.forEach((l,y)=>{l.forEach(M=>wr(a,y,void 0,M))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(a.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(a){this.totalTime=a}syncPlayerEvents(a){const l=this._player;l.triggerCallback&&a.onStart(()=>l.triggerCallback("start")),a.onDone(()=>this.finish()),a.onDestroy(()=>this.destroy())}_queueEvent(a,l){rn(this._queuedCallbacks,a,[]).push(l)}onDone(a){this.queued&&this._queueEvent("done",a),this._player.onDone(a)}onStart(a){this.queued&&this._queueEvent("start",a),this._player.onStart(a)}onDestroy(a){this.queued&&this._queueEvent("destroy",a),this._player.onDestroy(a)}init(){this._player.init()}hasStarted(){return!this.queued&&this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(a){this.queued||this._player.setPosition(a)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(a){const l=this._player;l.triggerCallback&&l.triggerCallback(a)}}function wt(_){return _&&1===_.nodeType}function mi(_,a){const l=_.style.display;return _.style.display=a??"none",l}function vr(_,a,l,y,M){const R=[];l.forEach(J=>R.push(mi(J)));const L=[];y.forEach((J,de)=>{const Be=new Map;J.forEach(Ke=>{const Pt=a.computeStyle(de,Ke,M);Be.set(Ke,Pt),(!Pt||0==Pt.length)&&(de[xn]=fn,L.push(de))}),_.set(de,Be)});let U=0;return l.forEach(J=>mi(J,R[U++])),L}function Sr(_,a){const l=new Map;if(_.forEach(U=>l.set(U,[])),0==a.length)return l;const M=new Set(a),R=new Map;function L(U){if(!U)return 1;let J=R.get(U);if(J)return J;const de=U.parentNode;return J=l.has(de)?de:M.has(de)?1:L(de),R.set(U,J),J}return a.forEach(U=>{const J=L(U);1!==J&&l.get(J).push(U)}),l}function An(_,a){_.classList?.add(a)}function Dr(_,a){_.classList?.remove(a)}function Si(_,a,l){ht(l).onDone(()=>_.processLeaveNode(a))}function xo(_,a){for(let l=0;l<_.length;l++){const y=_[l];y instanceof Qt?xo(y.players,a):a.push(y)}}function kn(_,a,l){const y=l.get(_);if(!y)return!1;let M=a.get(_);return M?y.forEach(R=>M.add(R)):a.set(_,y),l.delete(_),!0}class Gi{constructor(a,l,y){this.bodyNode=a,this._driver=l,this._normalizer=y,this._triggerCache={},this.onRemovalComplete=(M,R)=>{},this._transitionEngine=new Kr(a,l,y),this._timelineEngine=new Tn(a,l,y),this._transitionEngine.onRemovalComplete=(M,R)=>this.onRemovalComplete(M,R)}registerTrigger(a,l,y,M,R){const L=a+"-"+M;let U=this._triggerCache[L];if(!U){const J=[],Be=mr(this._driver,R,J,[]);if(J.length)throw function zt(_,a){return new C.wOt(3404,!1)}();U=function Le(_,a,l){return new mt(_,a,l)}(M,Be,this._normalizer),this._triggerCache[L]=U}this._transitionEngine.registerTrigger(l,M,U)}register(a,l){this._transitionEngine.register(a,l)}destroy(a,l){this._transitionEngine.destroy(a,l)}onInsert(a,l,y,M){this._transitionEngine.insertNode(a,l,y,M)}onRemove(a,l,y){this._transitionEngine.removeNode(a,l,y)}disableAnimations(a,l){this._transitionEngine.markElementAsDisabled(a,l)}process(a,l,y,M){if("@"==y.charAt(0)){const[R,L]=pn(y);this._timelineEngine.command(R,l,L,M)}else this._transitionEngine.trigger(a,l,y,M)}listen(a,l,y,M,R){if("@"==y.charAt(0)){const[L,U]=pn(y);return this._timelineEngine.listen(L,l,U,R)}return this._transitionEngine.listen(a,l,y,M,R)}flush(a=-1){this._transitionEngine.flush(a)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(a){this._transitionEngine.afterFlushAnimationsDone(a)}}let Wt=(()=>{class _{static{this.initialStylesByElement=new WeakMap}constructor(l,y,M){this._element=l,this._startStyles=y,this._endStyles=M,this._state=0;let R=_.initialStylesByElement.get(l);R||_.initialStylesByElement.set(l,R=new Map),this._initialStyles=R}start(){this._state<1&&(this._startStyles&&$(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&($(this._element,this._initialStyles),this._endStyles&&($(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(_.initialStylesByElement.delete(this._element),this._startStyles&&(he(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(he(this._element,this._endStyles),this._endStyles=null),$(this._element,this._initialStyles),this._state=3)}}return _})();function lr(_){let a=null;return _.forEach((l,y)=>{(function mo(_){return"display"===_||"position"===_})(y)&&(a=a||new Map,a.set(y,l))}),a}class b{constructor(a,l,y,M){this.element=a,this.keyframes=l,this.options=y,this._specialStyles=M,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=y.duration,this._delay=y.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(a=>a()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;const a=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,a,this.options),this._finalKeyframe=a.length?a[a.length-1]:new Map;const l=()=>this._onFinish();this.domPlayer.addEventListener("finish",l),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",l)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(a){const l=[];return a.forEach(y=>{l.push(Object.fromEntries(y))}),l}_triggerWebAnimation(a,l,y){return a.animate(this._convertKeyframesToObject(l),y)}onStart(a){this._originalOnStartFns.push(a),this._onStartFns.push(a)}onDone(a){this._originalOnDoneFns.push(a),this._onDoneFns.push(a)}onDestroy(a){this._onDestroyFns.push(a)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(a=>a()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(a=>a()),this._onDestroyFns=[])}setPosition(a){void 0===this.domPlayer&&this.init(),this.domPlayer.currentTime=a*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){const a=new Map;this.hasStarted()&&this._finalKeyframe.forEach((y,M)=>{"offset"!==M&&a.set(M,this._finished?y:Ut(this.element,M))}),this.currentSnapshot=a}triggerCallback(a){const l="start"===a?this._onStartFns:this._onDoneFns;l.forEach(y=>y()),l.length=0}}class S{validateStyleProperty(a){return!0}validateAnimatableStyleProperty(a){return!0}matchesElement(a,l){return!1}containsElement(a,l){return _t(a,l)}getParentElement(a){return Yt(a)}query(a,l,y){return At(a,l,y)}computeStyle(a,l,y){return window.getComputedStyle(a)[l]}animate(a,l,y,M,R,L=[]){const J={duration:y,delay:M,fill:0==M?"both":"forwards"};R&&(J.easing=R);const de=new Map,Be=L.filter(It=>It instanceof b);(function Yr(_,a){return 0===_||0===a})(y,M)&&Be.forEach(It=>{It.currentSnapshot.forEach((rt,Ct)=>de.set(Ct,rt))});let Ke=function pr(_){return _.length?_[0]instanceof Map?_:_.map(a=>Wn(a)):[]}(l).map(It=>j(It));Ke=function sr(_,a,l){if(l.size&&a.length){let y=a[0],M=[];if(l.forEach((R,L)=>{y.has(L)||M.push(L),y.set(L,R)}),M.length)for(let R=1;R<a.length;R++){let L=a[R];M.forEach(U=>L.set(U,Ut(_,U)))}}return a}(a,Ke,de);const Pt=function Ir(_,a){let l=null,y=null;return Array.isArray(a)&&a.length?(l=lr(a[0]),a.length>1&&(y=lr(a[a.length-1]))):a instanceof Map&&(l=lr(a)),l||y?new Wt(_,l,y):null}(a,Ke);return new b(a,Ke,J,Pt)}}var v=O(177);let P=(()=>{class _ extends ue{constructor(l,y){super(),this._nextAnimationId=0,this._renderer=l.createRenderer(y.body,{id:"0",encapsulation:C.gXe.None,styles:[],data:{animation:[]}})}build(l){const y=this._nextAnimationId.toString();this._nextAnimationId++;const M=Array.isArray(l)?ae(l):l;return Dt(this._renderer,null,y,"register",[M]),new B(y,this._renderer)}static{this.\u0275fac=function(y){return new(y||_)(C.KVO(C._9s),C.KVO(v.qQ))}}static{this.\u0275prov=C.jDH({token:_,factory:_.\u0275fac})}}return _})();class B extends se{constructor(a,l){super(),this._id=a,this._renderer=l}create(a,l){return new We(this._id,a,l||{},this._renderer)}}class We{constructor(a,l,y,M){this.id=a,this.element=l,this._renderer=M,this.parentPlayer=null,this._started=!1,this.totalTime=0,this._command("create",y)}_listen(a,l){return this._renderer.listen(this.element,`@@${this.id}:${a}`,l)}_command(a,...l){return Dt(this._renderer,this.element,this.id,a,l)}onDone(a){this._listen("done",a)}onStart(a){this._listen("start",a)}onDestroy(a){this._listen("destroy",a)}init(){this._command("init")}hasStarted(){return this._started}play(){this._command("play"),this._started=!0}pause(){this._command("pause")}restart(){this._command("restart")}finish(){this._command("finish")}destroy(){this._command("destroy")}reset(){this._command("reset"),this._started=!1}setPosition(a){this._command("setPosition",a)}getPosition(){return this._renderer.engine.players[+this.id]?.getPosition()??0}}function Dt(_,a,l,y,M){return _.setProperty(a,`@@${l}:${y}`,M)}const Ii="@.disabled";let yi=(()=>{class _{constructor(l,y,M){this.delegate=l,this.engine=y,this._zone=M,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,y.onRemovalComplete=(R,L)=>{const U=L?.parentNode(R);U&&L.removeChild(U,R)}}createRenderer(l,y){const R=this.delegate.createRenderer(l,y);if(!(l&&y&&y.data&&y.data.animation)){let Be=this._rendererCache.get(R);return Be||(Be=new Ti("",R,this.engine,()=>this._rendererCache.delete(R)),this._rendererCache.set(R,Be)),Be}const L=y.id,U=y.id+"-"+this._currentId;this._currentId++,this.engine.register(U,l);const J=Be=>{Array.isArray(Be)?Be.forEach(J):this.engine.registerTrigger(L,U,l,Be.name,Be)};return y.data.animation.forEach(J),new Ai(this,U,R,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(l,y,M){l>=0&&l<this._microtaskId?this._zone.run(()=>y(M)):(0==this._animationCallbacksBuffer.length&&queueMicrotask(()=>{this._zone.run(()=>{this._animationCallbacksBuffer.forEach(R=>{const[L,U]=R;L(U)}),this._animationCallbacksBuffer=[]})}),this._animationCallbacksBuffer.push([y,M]))}end(){this._cdRecurDepth--,0==this._cdRecurDepth&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}static{this.\u0275fac=function(y){return new(y||_)(C.KVO(C._9s),C.KVO(Gi),C.KVO(C.SKi))}}static{this.\u0275prov=C.jDH({token:_,factory:_.\u0275fac})}}return _})();class Ti{constructor(a,l,y,M){this.namespaceId=a,this.delegate=l,this.engine=y,this._onDestroy=M}get data(){return this.delegate.data}destroyNode(a){this.delegate.destroyNode?.(a)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(a,l){return this.delegate.createElement(a,l)}createComment(a){return this.delegate.createComment(a)}createText(a){return this.delegate.createText(a)}appendChild(a,l){this.delegate.appendChild(a,l),this.engine.onInsert(this.namespaceId,l,a,!1)}insertBefore(a,l,y,M=!0){this.delegate.insertBefore(a,l,y),this.engine.onInsert(this.namespaceId,l,a,M)}removeChild(a,l,y){this.engine.onRemove(this.namespaceId,l,this.delegate)}selectRootElement(a,l){return this.delegate.selectRootElement(a,l)}parentNode(a){return this.delegate.parentNode(a)}nextSibling(a){return this.delegate.nextSibling(a)}setAttribute(a,l,y,M){this.delegate.setAttribute(a,l,y,M)}removeAttribute(a,l,y){this.delegate.removeAttribute(a,l,y)}addClass(a,l){this.delegate.addClass(a,l)}removeClass(a,l){this.delegate.removeClass(a,l)}setStyle(a,l,y,M){this.delegate.setStyle(a,l,y,M)}removeStyle(a,l,y){this.delegate.removeStyle(a,l,y)}setProperty(a,l,y){"@"==l.charAt(0)&&l==Ii?this.disableAnimations(a,!!y):this.delegate.setProperty(a,l,y)}setValue(a,l){this.delegate.setValue(a,l)}listen(a,l,y){return this.delegate.listen(a,l,y)}disableAnimations(a,l){this.engine.disableAnimations(a,l)}}class Ai extends Ti{constructor(a,l,y,M,R){super(l,y,M,R),this.factory=a,this.namespaceId=l}setProperty(a,l,y){"@"==l.charAt(0)?"."==l.charAt(1)&&l==Ii?this.disableAnimations(a,y=void 0===y||!!y):this.engine.process(this.namespaceId,a,l.slice(1),y):this.delegate.setProperty(a,l,y)}listen(a,l,y){if("@"==l.charAt(0)){const M=function ii(_){switch(_){case"body":return document.body;case"document":return document;case"window":return window;default:return _}}(a);let R=l.slice(1),L="";return"@"!=R.charAt(0)&&([R,L]=function yo(_){const a=_.indexOf(".");return[_.substring(0,a),_.slice(a+1)]}(R)),this.engine.listen(this.namespaceId,M,R,L,U=>{this.factory.scheduleListenerCallback(U._data||-1,y,U)})}return this.delegate.listen(a,l,y)}}const vo=[{provide:ue,useClass:P},{provide:pt,useFactory:function ya(){return new xr}},{provide:Gi,useClass:(()=>{class _ extends Gi{constructor(l,y,M,R){super(l.body,y,M)}ngOnDestroy(){this.flush()}static{this.\u0275fac=function(y){return new(y||_)(C.KVO(v.qQ),C.KVO(vt),C.KVO(pt),C.KVO(C.o8S))}}static{this.\u0275prov=C.jDH({token:_,factory:_.\u0275fac})}}return _})()},{provide:C._9s,useFactory:function os(_,a,l){return new yi(_,a,l)},deps:[c.B7,Gi,C.SKi]}],Fo=[{provide:vt,useFactory:()=>new S},{provide:C.bc$,useValue:"BrowserAnimations"},...vo],Ms=[{provide:vt,useClass:hr},{provide:C.bc$,useValue:"NoopAnimations"},...vo];let Ss=(()=>{class _{static withConfig(l){return{ngModule:_,providers:l.disableAnimations?Ms:Fo}}static{this.\u0275fac=function(y){return new(y||_)}}static{this.\u0275mod=C.$C({type:_})}static{this.\u0275inj=C.G2t({providers:Fo,imports:[c.Bb]})}}return _})();var ko=O(1626),ss=O(4341),Zt=O(2434),Xr=O(4978);const Lr=[{path:"",redirectTo:"/auth/login",pathMatch:"full"},{path:"auth",loadChildren:()=>O.e(395).then(O.bind(O,8395)).then(_=>_.AuthModule)},{path:"patient",canActivate:[Xr.q],data:{roles:["PATIENT"]},loadChildren:()=>Promise.all([O.e(211),O.e(919),O.e(860),O.e(76),O.e(340)]).then(O.bind(O,340)).then(_=>_.PatientModule)},{path:"doctor",canActivate:[Xr.q],data:{roles:["DOCTOR"]},loadChildren:()=>Promise.all([O.e(211),O.e(919),O.e(860),O.e(561)]).then(O.bind(O,9561)).then(_=>_.DoctorModule)},{path:"profile",canActivate:[Xr.q],loadChildren:()=>O.e(809).then(O.bind(O,5809)).then(_=>_.ProfileModule)},{path:"appointments",canActivate:[Xr.q],loadChildren:()=>Promise.all([O.e(211),O.e(919),O.e(76),O.e(402)]).then(O.bind(O,5402)).then(_=>_.AppointmentsModule)},{path:"chat",canActivate:[Xr.q],loadChildren:()=>Promise.all([O.e(211),O.e(693)]).then(O.bind(O,2693)).then(_=>_.ChatModule)},{path:"ai-health-bot",canActivate:[Xr.q],data:{roles:["PATIENT"]},loadChildren:()=>O.e(479).then(O.bind(O,6860)).then(_=>_.AiHealthBotModule)},{path:"**",redirectTo:"/auth/login"}];let as=(()=>{class _{static{this.\u0275fac=function(y){return new(y||_)}}static{this.\u0275mod=C.$C({type:_})}static{this.\u0275inj=C.G2t({imports:[Zt.iI.forRoot(Lr),Zt.iI]})}}return _})();var Go=O(5964),so=O(8010),Wo=O(5567);function ao(_,a){if(1&_&&(C.j41(0,"span",16),C.EFF(1),C.k0s()),2&_){const l=C.XpG();C.R7$(1),C.SpI(" ",l.unreadCount>99?"99+":l.unreadCount," ")}}function Ko(_,a){if(1&_){const l=C.RV6();C.j41(0,"button",17),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.markAllAsRead())}),C.EFF(1," Mark all read "),C.k0s()}}function Xo(_,a){if(1&_){const l=C.RV6();C.j41(0,"button",18),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.clearAll())}),C.EFF(1," Clear all "),C.k0s()}}function Qo(_,a){1&_&&(C.j41(0,"div",19)(1,"div",20)(2,"span",21),C.EFF(3,"Loading..."),C.k0s()()())}function ls(_,a){1&_&&(C.j41(0,"div",22)(1,"div",23),C.nrm(2,"i",24),C.j41(3,"p",25),C.EFF(4,"No notifications"),C.k0s()()())}function us(_,a){if(1&_&&C.nrm(0,"img",41),2&_){const l=C.XpG(2).$implicit;C.Y8G("src",l.fromUser.avatar,C.B4B)("alt",l.fromUser.name)}}function Do(_,a){if(1&_&&(C.j41(0,"div",38),C.DNE(1,us,1,2,"img",39),C.j41(2,"span",40),C.EFF(3),C.k0s()()),2&_){const l=C.XpG().$implicit;C.R7$(1),C.Y8G("ngIf",l.fromUser.avatar),C.R7$(2),C.JRh(l.fromUser.name)}}function Lo(_,a){1&_&&C.nrm(0,"div",42)}function Co(_,a){if(1&_){const l=C.RV6();C.j41(0,"div",26),C.bIt("click",function(){const R=C.eBV(l).$implicit,L=C.XpG();return C.Njj(L.handleNotificationClick(R))}),C.j41(1,"div",27)(2,"div",28),C.nrm(3,"i"),C.k0s(),C.j41(4,"div",29)(5,"div",30),C.EFF(6),C.k0s(),C.j41(7,"div",31),C.EFF(8),C.k0s(),C.DNE(9,Do,4,2,"div",32),C.j41(10,"div",33),C.EFF(11),C.k0s()(),C.j41(12,"div",34)(13,"button",35),C.bIt("click",function(M){const L=C.eBV(l).$implicit,U=C.XpG();return C.Njj(U.removeNotification(L,M))}),C.nrm(14,"i",36),C.k0s()()(),C.DNE(15,Lo,1,0,"div",37),C.k0s()}if(2&_){const l=a.$implicit,y=C.XpG();C.HbH(y.getNotificationClass(l)),C.R7$(3),C.HbH(y.getNotificationIcon(l.type)),C.R7$(3),C.JRh(l.title),C.R7$(2),C.JRh(l.message),C.R7$(1),C.Y8G("ngIf",l.fromUser),C.R7$(2),C.JRh(y.formatTime(l.timestamp)),C.R7$(4),C.Y8G("ngIf",!l.read)}}function lo(_,a){1&_&&(C.j41(0,"div",43)(1,"a",44),C.EFF(2," View All Notifications "),C.k0s()())}let cs=(()=>{class _{constructor(l,y){this.notificationService=l,this.router=y,this.notifications=[],this.unreadCount=0,this.showDropdown=!1,this.loading=!1,this.subscriptions=[]}ngOnInit(){const l=this.notificationService.getNotifications().subscribe(M=>{this.notifications=M.slice(0,10)});this.subscriptions.push(l);const y=this.notificationService.getUnreadCount().subscribe(M=>this.unreadCount=M);this.subscriptions.push(y),this.notificationService.requestNotificationPermission()}ngOnDestroy(){this.subscriptions.forEach(l=>l.unsubscribe())}toggleDropdown(){this.showDropdown=!this.showDropdown}closeDropdown(){this.showDropdown=!1}markAsRead(l){l.read||this.notificationService.markAsRead(l.id)}markAllAsRead(){this.notificationService.markAllAsRead()}handleNotificationClick(l){this.markAsRead(l),l.actionUrl&&this.router.navigate([l.actionUrl]),this.closeDropdown()}removeNotification(l,y){y.stopPropagation(),this.notificationService.removeNotification(l.id)}clearAll(){this.notificationService.clearAll()}getNotificationIcon(l){switch(l){case"message":return"fas fa-comment";case"appointment":return"fas fa-calendar";case"urgent":return"fas fa-exclamation-triangle";case"system":return"fas fa-cog";default:return"fas fa-bell"}}getNotificationClass(l){const y=["notification-item"];return l.read||y.push("unread"),y.push(`priority-${l.priority}`),y.join(" ")}formatTime(l){const M=(new Date).getTime()-l.getTime(),R=Math.floor(M/6e4),L=Math.floor(R/60),U=Math.floor(L/24);return R<1?"Just now":R<60?`${R}m ago`:L<24?`${L}h ago`:U<7?`${U}d ago`:l.toLocaleDateString()}trackByNotificationId(l,y){return y.id}static{this.\u0275fac=function(y){return new(y||_)(C.rXU(Wo.J),C.rXU(Zt.Ix))}}static{this.\u0275cmp=C.VBU({type:_,selectors:[["app-notification-bell"]],decls:17,vars:12,consts:[[1,"notification-bell",3,"clickOutside"],["type","button",1,"btn","btn-link","notification-trigger",3,"click"],[1,"fas","fa-bell"],["class","badge bg-danger notification-badge",4,"ngIf"],[1,"notification-dropdown",3,"click"],[1,"dropdown-header"],[1,"d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"dropdown-actions"],["type","button","class","btn btn-sm btn-link text-primary",3,"click",4,"ngIf"],["type","button","class","btn btn-sm btn-link text-danger",3,"click",4,"ngIf"],[1,"notifications-list"],["class","text-center py-3",4,"ngIf"],["class","no-notifications",4,"ngIf"],[3,"class","click",4,"ngFor","ngForOf","ngForTrackBy"],["class","dropdown-footer",4,"ngIf"],[1,"badge","bg-danger","notification-badge"],["type","button",1,"btn","btn-sm","btn-link","text-primary",3,"click"],["type","button",1,"btn","btn-sm","btn-link","text-danger",3,"click"],[1,"text-center","py-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"no-notifications"],[1,"text-center","py-4"],[1,"fas","fa-bell-slash","fa-2x","text-muted","mb-2"],[1,"text-muted","mb-0"],[3,"click"],[1,"notification-content"],[1,"notification-icon"],[1,"notification-body"],[1,"notification-title"],[1,"notification-message"],["class","notification-from",4,"ngIf"],[1,"notification-time"],[1,"notification-actions"],["type","button","title","Remove notification",1,"btn","btn-sm","btn-link","text-muted",3,"click"],[1,"fas","fa-times"],["class","unread-indicator",4,"ngIf"],[1,"notification-from"],["class","from-avatar",3,"src","alt",4,"ngIf"],[1,"from-name"],[1,"from-avatar",3,"src","alt"],[1,"unread-indicator"],[1,"dropdown-footer"],["routerLink","/notifications",1,"btn","btn-sm","btn-outline-primary","w-100"]],template:function(y,M){1&y&&(C.j41(0,"div",0),C.bIt("clickOutside",function(){return M.closeDropdown()}),C.j41(1,"button",1),C.bIt("click",function(){return M.toggleDropdown()}),C.nrm(2,"i",2),C.DNE(3,ao,2,1,"span",3),C.k0s(),C.j41(4,"div",4),C.bIt("click",function(L){return L.stopPropagation()}),C.j41(5,"div",5)(6,"div",6)(7,"h6",7),C.EFF(8,"Notifications"),C.k0s(),C.j41(9,"div",8),C.DNE(10,Ko,2,0,"button",9),C.DNE(11,Xo,2,0,"button",10),C.k0s()()(),C.j41(12,"div",11),C.DNE(13,Qo,4,0,"div",12),C.DNE(14,ls,5,0,"div",13),C.DNE(15,Co,16,9,"div",14),C.k0s(),C.DNE(16,lo,3,0,"div",15),C.k0s()()),2&y&&(C.R7$(1),C.AVh("has-notifications",M.unreadCount>0),C.R7$(2),C.Y8G("ngIf",M.unreadCount>0),C.R7$(1),C.AVh("show",M.showDropdown),C.R7$(6),C.Y8G("ngIf",M.unreadCount>0),C.R7$(1),C.Y8G("ngIf",M.notifications.length>0),C.R7$(2),C.Y8G("ngIf",M.loading),C.R7$(1),C.Y8G("ngIf",!M.loading&&0===M.notifications.length),C.R7$(1),C.Y8G("ngForOf",M.notifications)("ngForTrackBy",M.trackByNotificationId),C.R7$(1),C.Y8G("ngIf",M.notifications.length>0))},dependencies:[v.Sq,v.bT,Zt.Wk],styles:[".notification-bell[_ngcontent-%COMP%]{position:relative;display:inline-block}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]{position:relative;padding:.5rem;color:#6c757d;border:none;background:none;font-size:1.25rem;transition:color .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]:hover{color:#495057}.notification-bell[_ngcontent-%COMP%]   .notification-trigger.has-notifications[_ngcontent-%COMP%]{color:#007bff;animation:_ngcontent-%COMP%_bellShake 2s infinite}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%]{position:absolute;top:0;right:0;font-size:.75rem;min-width:18px;height:18px;border-radius:9px;display:flex;align-items:center;justify-content:center;transform:translate(25%,-25%)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{position:absolute;top:100%;right:0;width:380px;max-height:500px;background:white;border:1px solid #dee2e6;border-radius:8px;box-shadow:0 4px 12px #00000026;z-index:1050;opacity:0;visibility:hidden;transform:translateY(-10px);transition:all .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown.show[_ngcontent-%COMP%]{opacity:1;visibility:visible;transform:translateY(0)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid #dee2e6;background:#f8f9fa;border-radius:8px 8px 0 0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057;font-weight:600}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.875rem;text-decoration:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{text-decoration:underline}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]{max-height:350px;overflow-y:auto}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]{position:relative;padding:1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:background-color .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%]{background-color:#f0f8ff;border-left:3px solid #007bff}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%]{font-weight:600}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-urgent[_ngcontent-%COMP%]{border-left-color:#dc3545}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-urgent[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#dc3545;animation:_ngcontent-%COMP%_pulse 2s infinite}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-high[_ngcontent-%COMP%]{border-left-color:#fd7e14}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:.75rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]{flex-shrink:0;width:32px;height:32px;border-radius:50%;background:#e9ecef;display:flex;align-items:center;justify-content:center}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]{flex:1;min-width:0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%]{font-size:.875rem;color:#212529;margin-bottom:.25rem;line-height:1.4}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-message[_ngcontent-%COMP%]{font-size:.8125rem;color:#6c757d;line-height:1.4;margin-bottom:.5rem;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.25rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]   .from-avatar[_ngcontent-%COMP%]{width:20px;height:20px;border-radius:50%;object-fit:cover}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]   .from-name[_ngcontent-%COMP%]{font-size:.75rem;color:#495057;font-weight:500}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-time[_ngcontent-%COMP%]{font-size:.75rem;color:#adb5bd}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]{flex-shrink:0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.25rem;border:none;background:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{background:#e9ecef;border-radius:4px}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .unread-indicator[_ngcontent-%COMP%]{position:absolute;top:50%;right:.5rem;width:8px;height:8px;background:#007bff;border-radius:50%;transform:translateY(-50%)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .no-notifications[_ngcontent-%COMP%]{padding:2rem 1rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]{padding:.75rem 1rem;border-top:1px solid #dee2e6;background:#f8f9fa;border-radius:0 0 8px 8px}@keyframes _ngcontent-%COMP%_bellShake{0%,50%,to{transform:rotate(0)}10%,30%{transform:rotate(-10deg)}20%,40%{transform:rotate(10deg)}}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}@media (max-width: 768px){.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{width:320px;right:-50px}}@media (max-width: 576px){.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{width:280px;right:-100px}}"]})}}return _})();var Vo=O(3794),va=O(6977),h=O(7436);function T(_,a){if(1&_){const l=C.RV6();C.j41(0,"li")(1,"a",8),C.bIt("click",function(M){const L=C.eBV(l).$implicit;return C.XpG().onLanguageChange(L.code),C.Njj(M.preventDefault())}),C.j41(2,"span",3),C.EFF(3),C.k0s(),C.j41(4,"span",9),C.EFF(5),C.k0s(),C.j41(6,"span",10),C.EFF(7),C.k0s()()()}if(2&_){const l=a.$implicit,y=C.XpG();C.R7$(1),C.AVh("active",l.code===y.currentLanguage),C.R7$(2),C.JRh(l.flag),C.R7$(2),C.JRh(l.name),C.R7$(2),C.SpI("(",l.code.toUpperCase(),")")}}let D=(()=>{class _{constructor(l){this.i18nService=l,this.destroy$=new Vo.B,this.currentLanguage="en",this.supportedLanguages=[],this.isDropdownOpen=!1}ngOnInit(){this.supportedLanguages=this.i18nService.getSupportedLanguages(),this.i18nService.getCurrentLanguage().pipe((0,va.Q)(this.destroy$)).subscribe(l=>{this.currentLanguage=l})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}onLanguageChange(l){this.i18nService.setLanguage(l),this.isDropdownOpen=!1}toggleDropdown(){this.isDropdownOpen=!this.isDropdownOpen}getCurrentLanguageInfo(){return this.supportedLanguages.find(l=>l.code===this.currentLanguage)}onClickOutside(){this.isDropdownOpen=!1}static{this.\u0275fac=function(y){return new(y||_)(C.rXU(h.s))}}static{this.\u0275cmp=C.VBU({type:_,selectors:[["app-language-selector"]],decls:11,vars:7,consts:[[1,"language-selector",3,"clickOutside"],[1,"dropdown"],["type","button",1,"btn","btn-outline-secondary","dropdown-toggle","language-btn",3,"click"],[1,"flag"],[1,"language-name","d-none","d-md-inline"],[1,"language-code","d-md-none"],[1,"dropdown-menu"],[4,"ngFor","ngForOf"],["href","#",1,"dropdown-item",3,"click"],[1,"language-name"],[1,"language-code"]],template:function(y,M){if(1&y&&(C.j41(0,"div",0),C.bIt("clickOutside",function(){return M.onClickOutside()}),C.j41(1,"div",1)(2,"button",2),C.bIt("click",function(){return M.toggleDropdown()}),C.j41(3,"span",3),C.EFF(4),C.k0s(),C.j41(5,"span",4),C.EFF(6),C.k0s(),C.j41(7,"span",5),C.EFF(8),C.k0s()(),C.j41(9,"ul",6),C.DNE(10,T,8,5,"li",7),C.k0s()()()),2&y){let R,L;C.R7$(2),C.BMQ("aria-expanded",M.isDropdownOpen),C.R7$(2),C.JRh((null==(R=M.getCurrentLanguageInfo())?null:R.flag)||"\u{1f310}"),C.R7$(2),C.JRh((null==(L=M.getCurrentLanguageInfo())?null:L.name)||"Language"),C.R7$(2),C.JRh(M.currentLanguage.toUpperCase()),C.R7$(1),C.AVh("show",M.isDropdownOpen),C.R7$(1),C.Y8G("ngForOf",M.supportedLanguages)}},dependencies:[v.Sq],styles:[".language-selector[_ngcontent-%COMP%]{position:relative}.language-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;border:1px solid #dee2e6;background:white;color:#495057;padding:.375rem .75rem;border-radius:.375rem;font-size:.875rem;transition:all .15s ease-in-out}.language-btn[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;border-color:#adb5bd}.language-btn[_ngcontent-%COMP%]:focus{outline:0;box-shadow:0 0 0 .2rem #007bff40}.flag[_ngcontent-%COMP%]{font-size:1.1em;line-height:1}.language-name[_ngcontent-%COMP%]{font-weight:500}.language-code[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d}.dropdown-menu[_ngcontent-%COMP%]{min-width:200px;border:1px solid #dee2e6;border-radius:.375rem;box-shadow:0 .5rem 1rem #00000026;z-index:1050}.dropdown-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.5rem 1rem;color:#212529;text-decoration:none;transition:background-color .15s ease-in-out}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;color:#16181b}.dropdown-item.active[_ngcontent-%COMP%]{background-color:#007bff;color:#fff}.dropdown-item.active[_ngcontent-%COMP%]   .language-code[_ngcontent-%COMP%]{color:#fffc}.dropdown-item[_ngcontent-%COMP%]   .language-name[_ngcontent-%COMP%]{flex:1;font-weight:500}.dropdown-item[_ngcontent-%COMP%]   .language-code[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d}@media (max-width: 768px){.language-btn[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.8rem}.dropdown-menu[_ngcontent-%COMP%]{min-width:150px}.dropdown-item[_ngcontent-%COMP%]{padding:.4rem .8rem;font-size:.85rem}}.dropdown-menu[_ngcontent-%COMP%]{opacity:0;transform:translateY(-10px);transition:opacity .15s ease,transform .15s ease;pointer-events:none}.dropdown-menu.show[_ngcontent-%COMP%]{opacity:1;transform:translateY(0);pointer-events:auto}"]})}}return _})();function I(_,a){1&_&&(C.qex(0),C.j41(1,"li",11)(2,"a",35),C.nrm(3,"i",36),C.EFF(4,"Appointments "),C.k0s()(),C.j41(5,"li",11)(6,"a",37),C.nrm(7,"i",38),C.EFF(8,"Find Doctors "),C.k0s()(),C.j41(9,"li",11)(10,"a",39),C.nrm(11,"i",40),C.EFF(12,"Health Assistant "),C.k0s()(),C.bVm())}function k(_,a){1&_&&(C.qex(0),C.j41(1,"li",11)(2,"a",41),C.nrm(3,"i",42),C.EFF(4,"Patients "),C.k0s()(),C.j41(5,"li",11)(6,"a",35),C.nrm(7,"i",36),C.EFF(8,"Schedule "),C.k0s()(),C.bVm())}function ie(_,a){if(1&_){const l=C.RV6();C.j41(0,"nav",3)(1,"div",4)(2,"a",5),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.navigateToDashboard())}),C.nrm(3,"i",6),C.EFF(4,"HealthConnect "),C.k0s(),C.j41(5,"button",7),C.nrm(6,"span",8),C.k0s(),C.j41(7,"div",9)(8,"ul",10)(9,"li",11)(10,"a",12),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.navigateToDashboard())}),C.nrm(11,"i",13),C.EFF(12,"Dashboard "),C.k0s()(),C.DNE(13,I,13,0,"ng-container",14),C.DNE(14,k,9,0,"ng-container",14),C.j41(15,"li",11)(16,"a",15),C.nrm(17,"i",16),C.EFF(18,"Messages "),C.k0s()()(),C.j41(19,"ul",17)(20,"li",18),C.nrm(21,"app-language-selector"),C.k0s(),C.j41(22,"li",11),C.nrm(23,"app-notification-bell"),C.k0s(),C.j41(24,"li",19)(25,"a",20)(26,"div",21),C.nrm(27,"i",22),C.k0s(),C.j41(28,"span",23),C.EFF(29),C.k0s()(),C.j41(30,"ul",24)(31,"li")(32,"h6",25),C.EFF(33),C.nrm(34,"br"),C.j41(35,"small",26),C.EFF(36),C.nI1(37,"titlecase"),C.k0s()()(),C.j41(38,"li"),C.nrm(39,"hr",27),C.k0s(),C.j41(40,"li")(41,"a",28),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.navigateToProfile())}),C.nrm(42,"i",29),C.EFF(43,"Profile Settings "),C.k0s()(),C.j41(44,"li")(45,"a",30),C.nrm(46,"i",31),C.EFF(47,"Notifications "),C.k0s()(),C.j41(48,"li")(49,"a",30),C.nrm(50,"i",32),C.EFF(51,"Help & Support "),C.k0s()(),C.j41(52,"li"),C.nrm(53,"hr",27),C.k0s(),C.j41(54,"li")(55,"a",33),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.logout())}),C.nrm(56,"i",34),C.EFF(57,"Sign Out "),C.k0s()()()()()()()()}if(2&_){const l=C.XpG();C.R7$(13),C.Y8G("ngIf","PATIENT"===l.currentUser.role),C.R7$(1),C.Y8G("ngIf","DOCTOR"===l.currentUser.role),C.R7$(15),C.JRh(l.currentUser.fullName),C.R7$(4),C.SpI(" ",l.currentUser.fullName," "),C.R7$(3),C.JRh(C.bMT(37,5,l.currentUser.role))}}function ve(_,a){1&_&&(C.j41(0,"footer",43)(1,"div",44)(2,"small",26),C.EFF(3," \xa9 2024 HealthConnect. All rights reserved. | "),C.j41(4,"a",45),C.EFF(5,"Privacy Policy"),C.k0s(),C.EFF(6," | "),C.j41(7,"a",45),C.EFF(8,"Terms of Service"),C.k0s()()()())}let Ze=(()=>{class _{constructor(l,y){this.authService=l,this.router=y,this.title="HealthConnect",this.currentUser=null,this.showNavigation=!1}ngOnInit(){this.authService.currentUser$.subscribe(l=>{this.currentUser=l,this.updateNavigationVisibility()}),this.router.events.pipe((0,Go.p)(l=>l instanceof Zt.wF)).subscribe(l=>{this.updateNavigationVisibility()})}updateNavigationVisibility(){const l=this.router.url.includes("/auth"),y=this.authService.isAuthenticated();this.showNavigation=!l&&y&&!!this.currentUser}logout(){this.authService.logout()}navigateToProfile(){this.router.navigate(["/profile"])}navigateToDashboard(){"DOCTOR"===this.currentUser?.role?this.router.navigate(["/doctor/dashboard"]):"PATIENT"===this.currentUser?.role&&this.router.navigate(["/patient/dashboard"])}static{this.\u0275fac=function(y){return new(y||_)(C.rXU(so.u),C.rXU(Zt.Ix))}}static{this.\u0275cmp=C.VBU({type:_,selectors:[["app-root"]],decls:4,vars:4,consts:[["class","navbar navbar-expand-lg navbar-dark bg-primary",4,"ngIf"],[1,"main-content"],["class","bg-light text-center py-3 mt-auto",4,"ngIf"],[1,"navbar","navbar-expand-lg","navbar-dark","bg-primary"],[1,"container-fluid"],[1,"navbar-brand","fw-bold",2,"cursor","pointer",3,"click"],[1,"bi","bi-heart-pulse","me-2"],["type","button","data-bs-toggle","collapse","data-bs-target","#navbarNav",1,"navbar-toggler"],[1,"navbar-toggler-icon"],["id","navbarNav",1,"collapse","navbar-collapse"],[1,"navbar-nav","me-auto"],[1,"nav-item"],[1,"nav-link",2,"cursor","pointer",3,"click"],[1,"bi","bi-house","me-1"],[4,"ngIf"],["routerLink","/chat","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-chat-dots","me-1"],[1,"navbar-nav"],[1,"nav-item","me-2"],[1,"nav-item","dropdown"],["href","#","id","navbarDropdown","role","button","data-bs-toggle","dropdown",1,"nav-link","dropdown-toggle","d-flex","align-items-center"],[1,"rounded-circle","bg-light","text-primary","d-flex","align-items-center","justify-content-center","me-2",2,"width","32px","height","32px"],[1,"bi","bi-person"],[1,"d-none","d-md-inline"],[1,"dropdown-menu","dropdown-menu-end"],[1,"dropdown-header"],[1,"text-muted"],[1,"dropdown-divider"],[1,"dropdown-item",2,"cursor","pointer",3,"click"],[1,"bi","bi-person-gear","me-2"],["href","#",1,"dropdown-item",2,"cursor","pointer"],[1,"bi","bi-bell","me-2"],[1,"bi","bi-question-circle","me-2"],[1,"dropdown-item","text-danger",2,"cursor","pointer",3,"click"],[1,"bi","bi-box-arrow-right","me-2"],["routerLink","/appointments","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-calendar","me-1"],["routerLink","/doctors","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-search","me-1"],["routerLink","/ai-health-bot","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-robot","me-1"],["routerLink","/patients","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-people","me-1"],[1,"bg-light","text-center","py-3","mt-auto"],[1,"container"],["href","#",1,"text-decoration-none"]],template:function(y,M){1&y&&(C.DNE(0,ie,58,7,"nav",0),C.j41(1,"main",1),C.nrm(2,"router-outlet"),C.k0s(),C.DNE(3,ve,9,0,"footer",2)),2&y&&(C.Y8G("ngIf",M.showNavigation&&M.currentUser),C.R7$(1),C.AVh("with-navbar",M.showNavigation&&M.currentUser),C.R7$(2),C.Y8G("ngIf",M.showNavigation))},dependencies:[v.bT,Zt.n3,Zt.Wk,Zt.wQ,cs,D,v.PV],styles:[".navbar[_ngcontent-%COMP%]{box-shadow:0 2px 4px #0000001a;z-index:1030}.navbar-brand[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700}.nav-link[_ngcontent-%COMP%]{font-weight:500;transition:all .3s ease}.nav-link[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;border-radius:.375rem}.nav-link.active[_ngcontent-%COMP%]{background-color:#fff3;border-radius:.375rem}.dropdown-menu[_ngcontent-%COMP%]{border:none;box-shadow:0 4px 6px #0000001a;border-radius:.5rem;min-width:200px}.dropdown-item[_ngcontent-%COMP%]{padding:.5rem 1rem;transition:all .3s ease}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.dropdown-header[_ngcontent-%COMP%]{font-weight:600;color:#495057}.main-content[_ngcontent-%COMP%]{min-height:calc(100vh - 60px);display:flex;flex-direction:column}.main-content.with-navbar[_ngcontent-%COMP%]{min-height:calc(100vh - 116px)}footer[_ngcontent-%COMP%]{margin-top:auto;border-top:1px solid #e9ecef}@media (max-width: 991px){.navbar-nav[_ngcontent-%COMP%]{padding-top:1rem}.navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{padding:.5rem 1rem}.dropdown-menu[_ngcontent-%COMP%]{position:static!important;transform:none!important;border:none;box-shadow:none;background-color:#ffffff1a;margin-top:.5rem}.dropdown-item[_ngcontent-%COMP%]{color:#fffc}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;color:#fff}.dropdown-header[_ngcontent-%COMP%]{color:#ffffffe6}.dropdown-divider[_ngcontent-%COMP%]{border-color:#fff3}}"]})}}return _})();var ln=O(3443);let un=(()=>{class _{constructor(l){this.authService=l}intercept(l,y){const M=this.authService.getToken();if(M){const R=l.clone({headers:l.headers.set("Authorization",`Bearer ${M}`)});return y.handle(R)}return y.handle(l)}static{this.\u0275fac=function(y){return new(y||_)(C.KVO(so.u))}}static{this.\u0275prov=C.jDH({token:_,factory:_.\u0275fac})}}return _})(),Ht=(()=>{class _{constructor(l){if(l)throw new Error("CoreModule is already loaded. Import it in the AppModule only")}static{this.\u0275fac=function(y){return new(y||_)(C.KVO(_,12))}}static{this.\u0275mod=C.$C({type:_})}static{this.\u0275inj=C.G2t({providers:[so.u,ln.D,Xr.q,{provide:ko.a7,useClass:un,multi:!0}],imports:[v.MD]})}}return _})();var St=O(3887);let sn=(()=>{class _{static{this.\u0275fac=function(y){return new(y||_)}}static{this.\u0275mod=C.$C({type:_,bootstrap:[Ze]})}static{this.\u0275inj=C.G2t({imports:[c.Bb,Ss,ko.q1,ss.X1,ss.YN,as,Ht,St.G]})}}return _})();window.global=window,c.sG().bootstrapModule(sn).catch(_=>console.error(_))},4412:(Je,me,O)=>{O.d(me,{t:()=>C});var c=O(3794);class C extends c.B{constructor(se){super(),this._value=se}get value(){return this.getValue()}_subscribe(se){const Q=super._subscribe(se);return!Q.closed&&se.next(this._value),Q}getValue(){const{hasError:se,thrownError:Q,_value:oe}=this;if(se)throw Q;return this._throwIfClosed(),oe}next(se){super.next(this._value=se)}}},1985:(Je,me,O)=>{O.d(me,{c:()=>fe});var c=O(7707),C=O(8359),ue=O(3494),se=O(1203),Q=O(1026),oe=O(8071),ne=O(9786);let fe=(()=>{class G{constructor(we){we&&(this._subscribe=we)}lift(we){const Fe=new G;return Fe.source=this,Fe.operator=we,Fe}subscribe(we,Fe,kt){const Mt=function z(G){return G&&G instanceof c.vU||function pe(G){return G&&(0,oe.T)(G.next)&&(0,oe.T)(G.error)&&(0,oe.T)(G.complete)}(G)&&(0,C.Uv)(G)}(we)?we:new c.Ms(we,Fe,kt);return(0,ne.Y)(()=>{const{operator:Lt,source:Vt}=this;Mt.add(Lt?Lt.call(Mt,Vt):Vt?this._subscribe(Mt):this._trySubscribe(Mt))}),Mt}_trySubscribe(we){try{return this._subscribe(we)}catch(Fe){we.error(Fe)}}forEach(we,Fe){return new(Fe=ae(Fe))((kt,Mt)=>{const Lt=new c.Ms({next:Vt=>{try{we(Vt)}catch(Qt){Mt(Qt),Lt.unsubscribe()}},error:Mt,complete:kt});this.subscribe(Lt)})}_subscribe(we){var Fe;return null===(Fe=this.source)||void 0===Fe?void 0:Fe.subscribe(we)}[ue.s](){return this}pipe(...we){return(0,se.m)(we)(this)}toPromise(we){return new(we=ae(we))((Fe,kt)=>{let Mt;this.subscribe(Lt=>Mt=Lt,Lt=>kt(Lt),()=>Fe(Mt))})}}return G.create=De=>new G(De),G})();function ae(G){var De;return null!==(De=G??Q.$.Promise)&&void 0!==De?De:Promise}},3794:(Je,me,O)=>{O.d(me,{B:()=>ne});var c=O(1985),C=O(8359);const se=(0,O(1853).L)(ae=>function(){ae(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Q=O(7908),oe=O(9786);let ne=(()=>{class ae extends c.c{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(z){const G=new fe(this,this);return G.operator=z,G}_throwIfClosed(){if(this.closed)throw new se}next(z){(0,oe.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(const G of this.currentObservers)G.next(z)}})}error(z){(0,oe.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=z;const{observers:G}=this;for(;G.length;)G.shift().error(z)}})}complete(){(0,oe.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:z}=this;for(;z.length;)z.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var z;return(null===(z=this.observers)||void 0===z?void 0:z.length)>0}_trySubscribe(z){return this._throwIfClosed(),super._trySubscribe(z)}_subscribe(z){return this._throwIfClosed(),this._checkFinalizedStatuses(z),this._innerSubscribe(z)}_innerSubscribe(z){const{hasError:G,isStopped:De,observers:we}=this;return G||De?C.Kn:(this.currentObservers=null,we.push(z),new C.yU(()=>{this.currentObservers=null,(0,Q.o)(we,z)}))}_checkFinalizedStatuses(z){const{hasError:G,thrownError:De,isStopped:we}=this;G?z.error(De):we&&z.complete()}asObservable(){const z=new c.c;return z.source=this,z}}return ae.create=(pe,z)=>new fe(pe,z),ae})();class fe extends ne{constructor(pe,z){super(),this.destination=pe,this.source=z}next(pe){var z,G;null===(G=null===(z=this.destination)||void 0===z?void 0:z.next)||void 0===G||G.call(z,pe)}error(pe){var z,G;null===(G=null===(z=this.destination)||void 0===z?void 0:z.error)||void 0===G||G.call(z,pe)}complete(){var pe,z;null===(z=null===(pe=this.destination)||void 0===pe?void 0:pe.complete)||void 0===z||z.call(pe)}_subscribe(pe){var z,G;return null!==(G=null===(z=this.source)||void 0===z?void 0:z.subscribe(pe))&&void 0!==G?G:C.Kn}}},7707:(Je,me,O)=>{O.d(me,{Ms:()=>kt,vU:()=>G});var c=O(8071),C=O(8359),ue=O(1026),se=O(5334),Q=O(5343);const oe=ae("C",void 0,void 0);function ae(Ce,Ae,Ve){return{kind:Ce,value:Ae,error:Ve}}var pe=O(9270),z=O(9786);class G extends C.yU{constructor(Ae){super(),this.isStopped=!1,Ae?(this.destination=Ae,(0,C.Uv)(Ae)&&Ae.add(this)):this.destination=Qt}static create(Ae,Ve,tt){return new kt(Ae,Ve,tt)}next(Ae){this.isStopped?Vt(function fe(Ce){return ae("N",Ce,void 0)}(Ae),this):this._next(Ae)}error(Ae){this.isStopped?Vt(function ne(Ce){return ae("E",void 0,Ce)}(Ae),this):(this.isStopped=!0,this._error(Ae))}complete(){this.isStopped?Vt(oe,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(Ae){this.destination.next(Ae)}_error(Ae){try{this.destination.error(Ae)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}const De=Function.prototype.bind;function we(Ce,Ae){return De.call(Ce,Ae)}class Fe{constructor(Ae){this.partialObserver=Ae}next(Ae){const{partialObserver:Ve}=this;if(Ve.next)try{Ve.next(Ae)}catch(tt){Mt(tt)}}error(Ae){const{partialObserver:Ve}=this;if(Ve.error)try{Ve.error(Ae)}catch(tt){Mt(tt)}else Mt(Ae)}complete(){const{partialObserver:Ae}=this;if(Ae.complete)try{Ae.complete()}catch(Ve){Mt(Ve)}}}class kt extends G{constructor(Ae,Ve,tt){let Pe;if(super(),(0,c.T)(Ae)||!Ae)Pe={next:Ae??void 0,error:Ve??void 0,complete:tt??void 0};else{let Oe;this&&ue.$.useDeprecatedNextContext?(Oe=Object.create(Ae),Oe.unsubscribe=()=>this.unsubscribe(),Pe={next:Ae.next&&we(Ae.next,Oe),error:Ae.error&&we(Ae.error,Oe),complete:Ae.complete&&we(Ae.complete,Oe)}):Pe=Ae}this.destination=new Fe(Pe)}}function Mt(Ce){ue.$.useDeprecatedSynchronousErrorHandling?(0,z.l)(Ce):(0,se.m)(Ce)}function Vt(Ce,Ae){const{onStoppedNotification:Ve}=ue.$;Ve&&pe.f.setTimeout(()=>Ve(Ce,Ae))}const Qt={closed:!0,next:Q.l,error:function Lt(Ce){throw Ce},complete:Q.l}},8359:(Je,me,O)=>{O.d(me,{Kn:()=>oe,yU:()=>Q,Uv:()=>ne});var c=O(8071);const ue=(0,O(1853).L)(ae=>function(z){ae(this),this.message=z?`${z.length} errors occurred during unsubscription:\n${z.map((G,De)=>`${De+1}) ${G.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=z});var se=O(7908);class Q{constructor(pe){this.initialTeardown=pe,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let pe;if(!this.closed){this.closed=!0;const{_parentage:z}=this;if(z)if(this._parentage=null,Array.isArray(z))for(const we of z)we.remove(this);else z.remove(this);const{initialTeardown:G}=this;if((0,c.T)(G))try{G()}catch(we){pe=we instanceof ue?we.errors:[we]}const{_finalizers:De}=this;if(De){this._finalizers=null;for(const we of De)try{fe(we)}catch(Fe){pe=pe??[],Fe instanceof ue?pe=[...pe,...Fe.errors]:pe.push(Fe)}}if(pe)throw new ue(pe)}}add(pe){var z;if(pe&&pe!==this)if(this.closed)fe(pe);else{if(pe instanceof Q){if(pe.closed||pe._hasParent(this))return;pe._addParent(this)}(this._finalizers=null!==(z=this._finalizers)&&void 0!==z?z:[]).push(pe)}}_hasParent(pe){const{_parentage:z}=this;return z===pe||Array.isArray(z)&&z.includes(pe)}_addParent(pe){const{_parentage:z}=this;this._parentage=Array.isArray(z)?(z.push(pe),z):z?[z,pe]:pe}_removeParent(pe){const{_parentage:z}=this;z===pe?this._parentage=null:Array.isArray(z)&&(0,se.o)(z,pe)}remove(pe){const{_finalizers:z}=this;z&&(0,se.o)(z,pe),pe instanceof Q&&pe._removeParent(this)}}Q.EMPTY=(()=>{const ae=new Q;return ae.closed=!0,ae})();const oe=Q.EMPTY;function ne(ae){return ae instanceof Q||ae&&"closed"in ae&&(0,c.T)(ae.remove)&&(0,c.T)(ae.add)&&(0,c.T)(ae.unsubscribe)}function fe(ae){(0,c.T)(ae)?ae():ae.unsubscribe()}},1026:(Je,me,O)=>{O.d(me,{$:()=>c});const c={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1}},983:(Je,me,O)=>{O.d(me,{w:()=>C});const C=new(O(1985).c)(Q=>Q.complete())},6648:(Je,me,O)=>{O.d(me,{H:()=>tt});var c=O(8750),C=O(5225),ue=O(9974),se=O(4360);function Q(Pe,Oe=0){return(0,ue.N)((be,nt)=>{be.subscribe((0,se._)(nt,jt=>(0,C.N)(nt,Pe,()=>nt.next(jt),Oe),()=>(0,C.N)(nt,Pe,()=>nt.complete(),Oe),jt=>(0,C.N)(nt,Pe,()=>nt.error(jt),Oe)))})}function oe(Pe,Oe=0){return(0,ue.N)((be,nt)=>{nt.add(Pe.schedule(()=>be.subscribe(nt),Oe))})}var ae=O(1985),z=O(4761),G=O(8071);function we(Pe,Oe){if(!Pe)throw new Error("Iterable cannot be null");return new ae.c(be=>{(0,C.N)(be,Oe,()=>{const nt=Pe[Symbol.asyncIterator]();(0,C.N)(be,Oe,()=>{nt.next().then(jt=>{jt.done?be.complete():be.next(jt.value)})},0,!0)})})}var Fe=O(5055),kt=O(9858),Mt=O(7441),Lt=O(5397),Vt=O(7953),Qt=O(591),Ce=O(5196);function tt(Pe,Oe){return Oe?function Ve(Pe,Oe){if(null!=Pe){if((0,Fe.l)(Pe))return function ne(Pe,Oe){return(0,c.Tg)(Pe).pipe(oe(Oe),Q(Oe))}(Pe,Oe);if((0,Mt.X)(Pe))return function pe(Pe,Oe){return new ae.c(be=>{let nt=0;return Oe.schedule(function(){nt===Pe.length?be.complete():(be.next(Pe[nt++]),be.closed||this.schedule())})})}(Pe,Oe);if((0,kt.y)(Pe))return function fe(Pe,Oe){return(0,c.Tg)(Pe).pipe(oe(Oe),Q(Oe))}(Pe,Oe);if((0,Vt.T)(Pe))return we(Pe,Oe);if((0,Lt.x)(Pe))return function De(Pe,Oe){return new ae.c(be=>{let nt;return(0,C.N)(be,Oe,()=>{nt=Pe[z.l](),(0,C.N)(be,Oe,()=>{let jt,Mn;try{({value:jt,done:Mn}=nt.next())}catch(Hn){return void be.error(Hn)}Mn?be.complete():be.next(jt)},0,!0)}),()=>(0,G.T)(nt?.return)&&nt.return()})}(Pe,Oe);if((0,Ce.U)(Pe))return function Ae(Pe,Oe){return we((0,Ce.C)(Pe),Oe)}(Pe,Oe)}throw(0,Qt.L)(Pe)}(Pe,Oe):(0,c.Tg)(Pe)}},8750:(Je,me,O)=>{O.d(me,{Tg:()=>De});var c=O(1635),C=O(7441),ue=O(9858),se=O(1985),Q=O(5055),oe=O(7953),ne=O(591),fe=O(5397),ae=O(5196),pe=O(8071),z=O(5334),G=O(3494);function De(Ce){if(Ce instanceof se.c)return Ce;if(null!=Ce){if((0,Q.l)(Ce))return function we(Ce){return new se.c(Ae=>{const Ve=Ce[G.s]();if((0,pe.T)(Ve.subscribe))return Ve.subscribe(Ae);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(Ce);if((0,C.X)(Ce))return function Fe(Ce){return new se.c(Ae=>{for(let Ve=0;Ve<Ce.length&&!Ae.closed;Ve++)Ae.next(Ce[Ve]);Ae.complete()})}(Ce);if((0,ue.y)(Ce))return function kt(Ce){return new se.c(Ae=>{Ce.then(Ve=>{Ae.closed||(Ae.next(Ve),Ae.complete())},Ve=>Ae.error(Ve)).then(null,z.m)})}(Ce);if((0,oe.T)(Ce))return Lt(Ce);if((0,fe.x)(Ce))return function Mt(Ce){return new se.c(Ae=>{for(const Ve of Ce)if(Ae.next(Ve),Ae.closed)return;Ae.complete()})}(Ce);if((0,ae.U)(Ce))return function Vt(Ce){return Lt((0,ae.C)(Ce))}(Ce)}throw(0,ne.L)(Ce)}function Lt(Ce){return new se.c(Ae=>{(function Qt(Ce,Ae){var Ve,tt,Pe,Oe;return(0,c.sH)(this,void 0,void 0,function*(){try{for(Ve=(0,c.xN)(Ce);!(tt=yield Ve.next()).done;)if(Ae.next(tt.value),Ae.closed)return}catch(be){Pe={error:be}}finally{try{tt&&!tt.done&&(Oe=Ve.return)&&(yield Oe.call(Ve))}finally{if(Pe)throw Pe.error}}Ae.complete()})})(Ce,Ae).catch(Ve=>Ae.error(Ve))})}},7673:(Je,me,O)=>{O.d(me,{of:()=>ue});var c=O(9326),C=O(6648);function ue(...se){const Q=(0,c.lI)(se);return(0,C.H)(se,Q)}},8810:(Je,me,O)=>{O.d(me,{$:()=>ue});var c=O(1985),C=O(8071);function ue(se,Q){const oe=(0,C.T)(se)?se:()=>se,ne=fe=>fe.error(oe());return new c.c(Q?fe=>Q.schedule(ne,0,fe):ne)}},4360:(Je,me,O)=>{O.d(me,{_:()=>C});var c=O(7707);function C(se,Q,oe,ne,fe){return new ue(se,Q,oe,ne,fe)}class ue extends c.vU{constructor(Q,oe,ne,fe,ae,pe){super(Q),this.onFinalize=ae,this.shouldUnsubscribe=pe,this._next=oe?function(z){try{oe(z)}catch(G){Q.error(G)}}:super._next,this._error=fe?function(z){try{fe(z)}catch(G){Q.error(G)}finally{this.unsubscribe()}}:super._error,this._complete=ne?function(){try{ne()}catch(z){Q.error(z)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var Q;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){const{closed:oe}=this;super.unsubscribe(),!oe&&(null===(Q=this.onFinalize)||void 0===Q||Q.call(this))}}}},9437:(Je,me,O)=>{O.d(me,{W:()=>se});var c=O(8750),C=O(4360),ue=O(9974);function se(Q){return(0,ue.N)((oe,ne)=>{let pe,fe=null,ae=!1;fe=oe.subscribe((0,C._)(ne,void 0,void 0,z=>{pe=(0,c.Tg)(Q(z,se(Q)(oe))),fe?(fe.unsubscribe(),fe=null,pe.subscribe(ne)):ae=!0})),ae&&(fe.unsubscribe(),fe=null,pe.subscribe(ne))})}},274:(Je,me,O)=>{O.d(me,{H:()=>ue});var c=O(1397),C=O(8071);function ue(se,Q){return(0,C.T)(Q)?(0,c.Z)(se,Q,1):(0,c.Z)(se,1)}},5964:(Je,me,O)=>{O.d(me,{p:()=>ue});var c=O(9974),C=O(4360);function ue(se,Q){return(0,c.N)((oe,ne)=>{let fe=0;oe.subscribe((0,C._)(ne,ae=>se.call(Q,ae,fe++)&&ne.next(ae)))})}},980:(Je,me,O)=>{O.d(me,{j:()=>C});var c=O(9974);function C(ue){return(0,c.N)((se,Q)=>{try{se.subscribe(Q)}finally{Q.add(ue)}})}},6354:(Je,me,O)=>{O.d(me,{T:()=>ue});var c=O(9974),C=O(4360);function ue(se,Q){return(0,c.N)((oe,ne)=>{let fe=0;oe.subscribe((0,C._)(ne,ae=>{ne.next(se.call(Q,ae,fe++))}))})}},6365:(Je,me,O)=>{O.d(me,{U:()=>ue});var c=O(1397),C=O(3669);function ue(se=1/0){return(0,c.Z)(C.D,se)}},1397:(Je,me,O)=>{O.d(me,{Z:()=>fe});var c=O(6354),C=O(8750),ue=O(9974),se=O(5225),Q=O(4360),ne=O(8071);function fe(ae,pe,z=1/0){return(0,ne.T)(pe)?fe((G,De)=>(0,c.T)((we,Fe)=>pe(G,we,De,Fe))((0,C.Tg)(ae(G,De))),z):("number"==typeof pe&&(z=pe),(0,ue.N)((G,De)=>function oe(ae,pe,z,G,De,we,Fe,kt){const Mt=[];let Lt=0,Vt=0,Qt=!1;const Ce=()=>{Qt&&!Mt.length&&!Lt&&pe.complete()},Ae=tt=>Lt<G?Ve(tt):Mt.push(tt),Ve=tt=>{we&&pe.next(tt),Lt++;let Pe=!1;(0,C.Tg)(z(tt,Vt++)).subscribe((0,Q._)(pe,Oe=>{De?.(Oe),we?Ae(Oe):pe.next(Oe)},()=>{Pe=!0},void 0,()=>{if(Pe)try{for(Lt--;Mt.length&&Lt<G;){const Oe=Mt.shift();Fe?(0,se.N)(pe,Fe,()=>Ve(Oe)):Ve(Oe)}Ce()}catch(Oe){pe.error(Oe)}}))};return ae.subscribe((0,Q._)(pe,Ae,()=>{Qt=!0,Ce()})),()=>{kt?.()}}(G,De,ae,z)))}},5558:(Je,me,O)=>{O.d(me,{n:()=>se});var c=O(8750),C=O(9974),ue=O(4360);function se(Q,oe){return(0,C.N)((ne,fe)=>{let ae=null,pe=0,z=!1;const G=()=>z&&!ae&&fe.complete();ne.subscribe((0,ue._)(fe,De=>{ae?.unsubscribe();let we=0;const Fe=pe++;(0,c.Tg)(Q(De,Fe)).subscribe(ae=(0,ue._)(fe,kt=>fe.next(oe?oe(De,kt,Fe,we++):kt),()=>{ae=null,G()}))},()=>{z=!0,G()}))})}},6977:(Je,me,O)=>{O.d(me,{Q:()=>Q});var c=O(9974),C=O(4360),ue=O(8750),se=O(5343);function Q(oe){return(0,c.N)((ne,fe)=>{(0,ue.Tg)(oe).subscribe((0,C._)(fe,()=>fe.complete(),se.l)),!fe.closed&&ne.subscribe(fe)})}},8141:(Je,me,O)=>{O.d(me,{M:()=>Q});var c=O(8071),C=O(9974),ue=O(4360),se=O(3669);function Q(oe,ne,fe){const ae=(0,c.T)(oe)||ne||fe?{next:oe,error:ne,complete:fe}:oe;return ae?(0,C.N)((pe,z)=>{var G;null===(G=ae.subscribe)||void 0===G||G.call(ae);let De=!0;pe.subscribe((0,ue._)(z,we=>{var Fe;null===(Fe=ae.next)||void 0===Fe||Fe.call(ae,we),z.next(we)},()=>{var we;De=!1,null===(we=ae.complete)||void 0===we||we.call(ae),z.complete()},we=>{var Fe;De=!1,null===(Fe=ae.error)||void 0===Fe||Fe.call(ae,we),z.error(we)},()=>{var we,Fe;De&&(null===(we=ae.unsubscribe)||void 0===we||we.call(ae)),null===(Fe=ae.finalize)||void 0===Fe||Fe.call(ae)}))}):se.D}},9270:(Je,me,O)=>{O.d(me,{f:()=>c});const c={setTimeout(C,ue,...se){const{delegate:Q}=c;return Q?.setTimeout?Q.setTimeout(C,ue,...se):setTimeout(C,ue,...se)},clearTimeout(C){const{delegate:ue}=c;return(ue?.clearTimeout||clearTimeout)(C)},delegate:void 0}},4761:(Je,me,O)=>{O.d(me,{l:()=>C});const C=function c(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}()},3494:(Je,me,O)=>{O.d(me,{s:()=>c});const c="function"==typeof Symbol&&Symbol.observable||"@@observable"},9326:(Je,me,O)=>{O.d(me,{R0:()=>oe,lI:()=>Q,ms:()=>se});var c=O(8071),C=O(9470);function ue(ne){return ne[ne.length-1]}function se(ne){return(0,c.T)(ue(ne))?ne.pop():void 0}function Q(ne){return(0,C.m)(ue(ne))?ne.pop():void 0}function oe(ne,fe){return"number"==typeof ue(ne)?ne.pop():fe}},3073:(Je,me,O)=>{O.d(me,{D:()=>Q});const{isArray:c}=Array,{getPrototypeOf:C,prototype:ue,keys:se}=Object;function Q(ne){if(1===ne.length){const fe=ne[0];if(c(fe))return{args:fe,keys:null};if(function oe(ne){return ne&&"object"==typeof ne&&C(ne)===ue}(fe)){const ae=se(fe);return{args:ae.map(pe=>fe[pe]),keys:ae}}}return{args:ne,keys:null}}},7908:(Je,me,O)=>{function c(C,ue){if(C){const se=C.indexOf(ue);0<=se&&C.splice(se,1)}}O.d(me,{o:()=>c})},1853:(Je,me,O)=>{function c(C){const se=C(Q=>{Error.call(Q),Q.stack=(new Error).stack});return se.prototype=Object.create(Error.prototype),se.prototype.constructor=se,se}O.d(me,{L:()=>c})},8496:(Je,me,O)=>{function c(C,ue){return C.reduce((se,Q,oe)=>(se[Q]=ue[oe],se),{})}O.d(me,{e:()=>c})},9786:(Je,me,O)=>{O.d(me,{Y:()=>ue,l:()=>se});var c=O(1026);let C=null;function ue(Q){if(c.$.useDeprecatedSynchronousErrorHandling){const oe=!C;if(oe&&(C={errorThrown:!1,error:null}),Q(),oe){const{errorThrown:ne,error:fe}=C;if(C=null,ne)throw fe}}else Q()}function se(Q){c.$.useDeprecatedSynchronousErrorHandling&&C&&(C.errorThrown=!0,C.error=Q)}},5225:(Je,me,O)=>{function c(C,ue,se,Q=0,oe=!1){const ne=ue.schedule(function(){se(),oe?C.add(this.schedule(null,Q)):this.unsubscribe()},Q);if(C.add(ne),!oe)return ne}O.d(me,{N:()=>c})},3669:(Je,me,O)=>{function c(C){return C}O.d(me,{D:()=>c})},7441:(Je,me,O)=>{O.d(me,{X:()=>c});const c=C=>C&&"number"==typeof C.length&&"function"!=typeof C},7953:(Je,me,O)=>{O.d(me,{T:()=>C});var c=O(8071);function C(ue){return Symbol.asyncIterator&&(0,c.T)(ue?.[Symbol.asyncIterator])}},8071:(Je,me,O)=>{function c(C){return"function"==typeof C}O.d(me,{T:()=>c})},5055:(Je,me,O)=>{O.d(me,{l:()=>ue});var c=O(3494),C=O(8071);function ue(se){return(0,C.T)(se[c.s])}},5397:(Je,me,O)=>{O.d(me,{x:()=>ue});var c=O(4761),C=O(8071);function ue(se){return(0,C.T)(se?.[c.l])}},9858:(Je,me,O)=>{O.d(me,{y:()=>C});var c=O(8071);function C(ue){return(0,c.T)(ue?.then)}},5196:(Je,me,O)=>{O.d(me,{C:()=>ue,U:()=>se});var c=O(1635),C=O(8071);function ue(Q){return(0,c.AQ)(this,arguments,function*(){const ne=Q.getReader();try{for(;;){const{value:fe,done:ae}=yield(0,c.N3)(ne.read());if(ae)return yield(0,c.N3)(void 0);yield yield(0,c.N3)(fe)}}finally{ne.releaseLock()}})}function se(Q){return(0,C.T)(Q?.getReader)}},9470:(Je,me,O)=>{O.d(me,{m:()=>C});var c=O(8071);function C(ue){return ue&&(0,c.T)(ue.schedule)}},9974:(Je,me,O)=>{O.d(me,{N:()=>ue,S:()=>C});var c=O(8071);function C(se){return(0,c.T)(se?.lift)}function ue(se){return Q=>{if(C(Q))return Q.lift(function(oe){try{return se(oe,this)}catch(ne){this.error(ne)}});throw new TypeError("Unable to lift unknown Observable type")}}},6450:(Je,me,O)=>{O.d(me,{I:()=>se});var c=O(6354);const{isArray:C}=Array;function se(Q){return(0,c.T)(oe=>function ue(Q,oe){return C(oe)?Q(...oe):Q(oe)}(Q,oe))}},5343:(Je,me,O)=>{function c(){}O.d(me,{l:()=>c})},1203:(Je,me,O)=>{O.d(me,{F:()=>C,m:()=>ue});var c=O(3669);function C(...se){return ue(se)}function ue(se){return 0===se.length?c.D:1===se.length?se[0]:function(oe){return se.reduce((ne,fe)=>fe(ne),oe)}}},5334:(Je,me,O)=>{O.d(me,{m:()=>ue});var c=O(1026),C=O(9270);function ue(se){C.f.setTimeout(()=>{const{onUnhandledError:Q}=c.$;if(!Q)throw se;Q(se)})}},591:(Je,me,O)=>{function c(C){return new TypeError(`You provided ${null!==C&&"object"==typeof C?"an invalid object":`'${C}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}O.d(me,{L:()=>c})},177:(Je,me,O)=>{O.d(me,{AJ:()=>pi,MD:()=>Ui,N0:()=>ri,P9:()=>Pn,PV:()=>to,QT:()=>ue,Sm:()=>Fe,Sq:()=>Xn,VF:()=>Q,Vy:()=>mt,Xr:()=>Tn,YU:()=>Ut,ZD:()=>se,_b:()=>Yr,aZ:()=>Mt,bT:()=>Or,fw:()=>kt,hb:()=>De,hj:()=>fe,qQ:()=>oe,vh:()=>ei});var c=O(540);let C=null;function ue(){return C}function se(h){C||(C=h)}class Q{}const oe=new c.nKC("DocumentToken");let ne=(()=>{class h{historyGo(D){throw new Error("Not implemented")}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=c.jDH({token:h,factory:function(){return(0,c.WQX)(ae)},providedIn:"platform"})}}return h})();const fe=new c.nKC("Location Initialized");let ae=(()=>{class h extends ne{constructor(){super(),this._doc=(0,c.WQX)(oe),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return ue().getBaseHref(this._doc)}onPopState(D){const I=ue().getGlobalEventTarget(this._doc,"window");return I.addEventListener("popstate",D,!1),()=>I.removeEventListener("popstate",D)}onHashChange(D){const I=ue().getGlobalEventTarget(this._doc,"window");return I.addEventListener("hashchange",D,!1),()=>I.removeEventListener("hashchange",D)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(D){this._location.pathname=D}pushState(D,I,k){this._history.pushState(D,I,k)}replaceState(D,I,k){this._history.replaceState(D,I,k)}forward(){this._history.forward()}back(){this._history.back()}historyGo(D=0){this._history.go(D)}getState(){return this._history.state}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=c.jDH({token:h,factory:function(){return new h},providedIn:"platform"})}}return h})();function pe(h,T){if(0==h.length)return T;if(0==T.length)return h;let D=0;return h.endsWith("/")&&D++,T.startsWith("/")&&D++,2==D?h+T.substring(1):1==D?h+T:h+"/"+T}function z(h){const T=h.match(/#|\?|$/),D=T&&T.index||h.length;return h.slice(0,D-("/"===h[D-1]?1:0))+h.slice(D)}function G(h){return h&&"?"!==h[0]?"?"+h:h}let De=(()=>{class h{historyGo(D){throw new Error("Not implemented")}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=c.jDH({token:h,factory:function(){return(0,c.WQX)(Fe)},providedIn:"root"})}}return h})();const we=new c.nKC("appBaseHref");let Fe=(()=>{class h extends De{constructor(D,I){super(),this._platformLocation=D,this._removeListenerFns=[],this._baseHref=I??this._platformLocation.getBaseHrefFromDOM()??(0,c.WQX)(oe).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(D){this._removeListenerFns.push(this._platformLocation.onPopState(D),this._platformLocation.onHashChange(D))}getBaseHref(){return this._baseHref}prepareExternalUrl(D){return pe(this._baseHref,D)}path(D=!1){const I=this._platformLocation.pathname+G(this._platformLocation.search),k=this._platformLocation.hash;return k&&D?`${I}${k}`:I}pushState(D,I,k,ie){const ve=this.prepareExternalUrl(k+G(ie));this._platformLocation.pushState(D,I,ve)}replaceState(D,I,k,ie){const ve=this.prepareExternalUrl(k+G(ie));this._platformLocation.replaceState(D,I,ve)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(D=0){this._platformLocation.historyGo?.(D)}static{this.\u0275fac=function(I){return new(I||h)(c.KVO(ne),c.KVO(we,8))}}static{this.\u0275prov=c.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})(),kt=(()=>{class h extends De{constructor(D,I){super(),this._platformLocation=D,this._baseHref="",this._removeListenerFns=[],null!=I&&(this._baseHref=I)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(D){this._removeListenerFns.push(this._platformLocation.onPopState(D),this._platformLocation.onHashChange(D))}getBaseHref(){return this._baseHref}path(D=!1){let I=this._platformLocation.hash;return null==I&&(I="#"),I.length>0?I.substring(1):I}prepareExternalUrl(D){const I=pe(this._baseHref,D);return I.length>0?"#"+I:I}pushState(D,I,k,ie){let ve=this.prepareExternalUrl(k+G(ie));0==ve.length&&(ve=this._platformLocation.pathname),this._platformLocation.pushState(D,I,ve)}replaceState(D,I,k,ie){let ve=this.prepareExternalUrl(k+G(ie));0==ve.length&&(ve=this._platformLocation.pathname),this._platformLocation.replaceState(D,I,ve)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(D=0){this._platformLocation.historyGo?.(D)}static{this.\u0275fac=function(I){return new(I||h)(c.KVO(ne),c.KVO(we,8))}}static{this.\u0275prov=c.jDH({token:h,factory:h.\u0275fac})}}return h})(),Mt=(()=>{class h{constructor(D){this._subject=new c.bkB,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=D;const I=this._locationStrategy.getBaseHref();this._basePath=function Ce(h){if(new RegExp("^(https?:)?//").test(h)){const[,D]=h.split(/\/\/[^\/]+/);return D}return h}(z(Qt(I))),this._locationStrategy.onPopState(k=>{this._subject.emit({url:this.path(!0),pop:!0,state:k.state,type:k.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(D=!1){return this.normalize(this._locationStrategy.path(D))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(D,I=""){return this.path()==this.normalize(D+G(I))}normalize(D){return h.stripTrailingSlash(function Vt(h,T){if(!h||!T.startsWith(h))return T;const D=T.substring(h.length);return""===D||["/",";","?","#"].includes(D[0])?D:T}(this._basePath,Qt(D)))}prepareExternalUrl(D){return D&&"/"!==D[0]&&(D="/"+D),this._locationStrategy.prepareExternalUrl(D)}go(D,I="",k=null){this._locationStrategy.pushState(k,"",D,I),this._notifyUrlChangeListeners(this.prepareExternalUrl(D+G(I)),k)}replaceState(D,I="",k=null){this._locationStrategy.replaceState(k,"",D,I),this._notifyUrlChangeListeners(this.prepareExternalUrl(D+G(I)),k)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(D=0){this._locationStrategy.historyGo?.(D)}onUrlChange(D){return this._urlChangeListeners.push(D),this._urlChangeSubscription||(this._urlChangeSubscription=this.subscribe(I=>{this._notifyUrlChangeListeners(I.url,I.state)})),()=>{const I=this._urlChangeListeners.indexOf(D);this._urlChangeListeners.splice(I,1),0===this._urlChangeListeners.length&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(D="",I){this._urlChangeListeners.forEach(k=>k(D,I))}subscribe(D,I,k){return this._subject.subscribe({next:D,error:I,complete:k})}static{this.normalizeQueryParams=G}static{this.joinWithSlash=pe}static{this.stripTrailingSlash=z}static{this.\u0275fac=function(I){return new(I||h)(c.KVO(De))}}static{this.\u0275prov=c.jDH({token:h,factory:function(){return function Lt(){return new Mt((0,c.KVO)(De))}()},providedIn:"root"})}}return h})();function Qt(h){return h.replace(/\/index.html$/,"")}var Pe=function(h){return h[h.Format=0]="Format",h[h.Standalone=1]="Standalone",h}(Pe||{}),Oe=function(h){return h[h.Narrow=0]="Narrow",h[h.Abbreviated=1]="Abbreviated",h[h.Wide=2]="Wide",h[h.Short=3]="Short",h}(Oe||{}),be=function(h){return h[h.Short=0]="Short",h[h.Medium=1]="Medium",h[h.Long=2]="Long",h[h.Full=3]="Full",h}(be||{}),nt=function(h){return h[h.Decimal=0]="Decimal",h[h.Group=1]="Group",h[h.List=2]="List",h[h.PercentSign=3]="PercentSign",h[h.PlusSign=4]="PlusSign",h[h.MinusSign=5]="MinusSign",h[h.Exponential=6]="Exponential",h[h.SuperscriptingExponent=7]="SuperscriptingExponent",h[h.PerMille=8]="PerMille",h[h.Infinity=9]="Infinity",h[h.NaN=10]="NaN",h[h.TimeSeparator=11]="TimeSeparator",h[h.CurrencyDecimal=12]="CurrencyDecimal",h[h.CurrencyGroup=13]="CurrencyGroup",h}(nt||{});function jr(h,T){return yt((0,c.H5H)(h)[c.KH2.DateFormat],T)}function Z(h,T){return yt((0,c.H5H)(h)[c.KH2.TimeFormat],T)}function te(h,T){return yt((0,c.H5H)(h)[c.KH2.DateTimeFormat],T)}function Y(h,T){const D=(0,c.H5H)(h),I=D[c.KH2.NumberSymbols][T];if(typeof I>"u"){if(T===nt.CurrencyDecimal)return D[c.KH2.NumberSymbols][nt.Decimal];if(T===nt.CurrencyGroup)return D[c.KH2.NumberSymbols][nt.Group]}return I}function lt(h){if(!h[c.KH2.ExtraData])throw new Error(`Missing extra locale data for the locale "${h[c.KH2.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function yt(h,T){for(let D=T;D>-1;D--)if(typeof h[D]<"u")return h[D];throw new Error("Locale data API: locale data undefined")}function yn(h){const[T,D]=h.split(":");return{hours:+T,minutes:+D}}const Vn=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Ur={},Nt=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;var fr=function(h){return h[h.Short=0]="Short",h[h.ShortGMT=1]="ShortGMT",h[h.Long=2]="Long",h[h.Extended=3]="Extended",h}(fr||{}),ht=function(h){return h[h.FullYear=0]="FullYear",h[h.Month=1]="Month",h[h.Date=2]="Date",h[h.Hours=3]="Hours",h[h.Minutes=4]="Minutes",h[h.Seconds=5]="Seconds",h[h.FractionalSeconds=6]="FractionalSeconds",h[h.Day=7]="Day",h}(ht||{}),st=function(h){return h[h.DayPeriods=0]="DayPeriods",h[h.Days=1]="Days",h[h.Months=2]="Months",h[h.Eras=3]="Eras",h}(st||{});function wr(h,T,D,I){let k=function ce(h){if(V(h))return h;if("number"==typeof h&&!isNaN(h))return new Date(h);if("string"==typeof h){if(h=h.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(h)){const[k,ie=1,ve=1]=h.split("-").map(Ze=>+Ze);return zn(k,ie-1,ve)}const D=parseFloat(h);if(!isNaN(h-D))return new Date(D);let I;if(I=h.match(Vn))return function q(h){const T=new Date(0);let D=0,I=0;const k=h[8]?T.setUTCFullYear:T.setFullYear,ie=h[8]?T.setUTCHours:T.setHours;h[9]&&(D=Number(h[9]+h[10]),I=Number(h[9]+h[11])),k.call(T,Number(h[1]),Number(h[2])-1,Number(h[3]));const ve=Number(h[4]||0)-D,Ze=Number(h[5]||0)-I,ln=Number(h[6]||0),un=Math.floor(1e3*parseFloat("0."+(h[7]||0)));return ie.call(T,ve,Ze,ln,un),T}(I)}const T=new Date(h);if(!V(T))throw new Error(`Unable to convert "${h}" into a date`);return T}(h);T=vn(D,T)||T;let Ze,ve=[];for(;T;){if(Ze=Nt.exec(T),!Ze){ve.push(T);break}{ve=ve.concat(Ze.slice(1));const Ht=ve.pop();if(!Ht)break;T=Ht}}let ln=k.getTimezoneOffset();I&&(ln=Gn(I,ln),k=function or(h,T,D){const I=D?-1:1,k=h.getTimezoneOffset();return function Hr(h,T){return(h=new Date(h.getTime())).setMinutes(h.getMinutes()+T),h}(h,I*(Gn(T,k)-k))}(k,I,!0));let un="";return ve.forEach(Ht=>{const St=function er(h){if(vt[h])return vt[h];let T;switch(h){case"G":case"GG":case"GGG":T=ze(st.Eras,Oe.Abbreviated);break;case"GGGG":T=ze(st.Eras,Oe.Wide);break;case"GGGGG":T=ze(st.Eras,Oe.Narrow);break;case"y":T=Yt(ht.FullYear,1,0,!1,!0);break;case"yy":T=Yt(ht.FullYear,2,0,!0,!0);break;case"yyy":T=Yt(ht.FullYear,3,0,!1,!0);break;case"yyyy":T=Yt(ht.FullYear,4,0,!1,!0);break;case"Y":T=hr(1);break;case"YY":T=hr(2,!0);break;case"YYY":T=hr(3);break;case"YYYY":T=hr(4);break;case"M":case"L":T=Yt(ht.Month,1,1);break;case"MM":case"LL":T=Yt(ht.Month,2,1);break;case"MMM":T=ze(st.Months,Oe.Abbreviated);break;case"MMMM":T=ze(st.Months,Oe.Wide);break;case"MMMMM":T=ze(st.Months,Oe.Narrow);break;case"LLL":T=ze(st.Months,Oe.Abbreviated,Pe.Standalone);break;case"LLLL":T=ze(st.Months,Oe.Wide,Pe.Standalone);break;case"LLLLL":T=ze(st.Months,Oe.Narrow,Pe.Standalone);break;case"w":T=Ji(1);break;case"ww":T=Ji(2);break;case"W":T=Ji(1,!0);break;case"d":T=Yt(ht.Date,1);break;case"dd":T=Yt(ht.Date,2);break;case"c":case"cc":T=Yt(ht.Day,1);break;case"ccc":T=ze(st.Days,Oe.Abbreviated,Pe.Standalone);break;case"cccc":T=ze(st.Days,Oe.Wide,Pe.Standalone);break;case"ccccc":T=ze(st.Days,Oe.Narrow,Pe.Standalone);break;case"cccccc":T=ze(st.Days,Oe.Short,Pe.Standalone);break;case"E":case"EE":case"EEE":T=ze(st.Days,Oe.Abbreviated);break;case"EEEE":T=ze(st.Days,Oe.Wide);break;case"EEEEE":T=ze(st.Days,Oe.Narrow);break;case"EEEEEE":T=ze(st.Days,Oe.Short);break;case"a":case"aa":case"aaa":T=ze(st.DayPeriods,Oe.Abbreviated);break;case"aaaa":T=ze(st.DayPeriods,Oe.Wide);break;case"aaaaa":T=ze(st.DayPeriods,Oe.Narrow);break;case"b":case"bb":case"bbb":T=ze(st.DayPeriods,Oe.Abbreviated,Pe.Standalone,!0);break;case"bbbb":T=ze(st.DayPeriods,Oe.Wide,Pe.Standalone,!0);break;case"bbbbb":T=ze(st.DayPeriods,Oe.Narrow,Pe.Standalone,!0);break;case"B":case"BB":case"BBB":T=ze(st.DayPeriods,Oe.Abbreviated,Pe.Format,!0);break;case"BBBB":T=ze(st.DayPeriods,Oe.Wide,Pe.Format,!0);break;case"BBBBB":T=ze(st.DayPeriods,Oe.Narrow,Pe.Format,!0);break;case"h":T=Yt(ht.Hours,1,-12);break;case"hh":T=Yt(ht.Hours,2,-12);break;case"H":T=Yt(ht.Hours,1);break;case"HH":T=Yt(ht.Hours,2);break;case"m":T=Yt(ht.Minutes,1);break;case"mm":T=Yt(ht.Minutes,2);break;case"s":T=Yt(ht.Seconds,1);break;case"ss":T=Yt(ht.Seconds,2);break;case"S":T=Yt(ht.FractionalSeconds,1);break;case"SS":T=Yt(ht.FractionalSeconds,2);break;case"SSS":T=Yt(ht.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":T=$r(fr.Short);break;case"ZZZZZ":T=$r(fr.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":T=$r(fr.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":T=$r(fr.Long);break;default:return null}return vt[h]=T,T}(Ht);un+=St?St(k,D,ln):"''"===Ht?"'":Ht.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),un}function zn(h,T,D){const I=new Date(0);return I.setFullYear(h,T,D),I.setHours(0,0,0),I}function vn(h,T){const D=function Mn(h){return(0,c.H5H)(h)[c.KH2.LocaleId]}(h);if(Ur[D]=Ur[D]||{},Ur[D][T])return Ur[D][T];let I="";switch(T){case"shortDate":I=jr(h,be.Short);break;case"mediumDate":I=jr(h,be.Medium);break;case"longDate":I=jr(h,be.Long);break;case"fullDate":I=jr(h,be.Full);break;case"shortTime":I=Z(h,be.Short);break;case"mediumTime":I=Z(h,be.Medium);break;case"longTime":I=Z(h,be.Long);break;case"fullTime":I=Z(h,be.Full);break;case"short":const k=vn(h,"shortTime"),ie=vn(h,"shortDate");I=rn(te(h,be.Short),[k,ie]);break;case"medium":const ve=vn(h,"mediumTime"),Ze=vn(h,"mediumDate");I=rn(te(h,be.Medium),[ve,Ze]);break;case"long":const ln=vn(h,"longTime"),un=vn(h,"longDate");I=rn(te(h,be.Long),[ln,un]);break;case"full":const Ht=vn(h,"fullTime"),St=vn(h,"fullDate");I=rn(te(h,be.Full),[Ht,St])}return I&&(Ur[D][T]=I),I}function rn(h,T){return T&&(h=h.replace(/\{([^}]+)}/g,function(D,I){return null!=T&&I in T?T[I]:D})),h}function pn(h,T,D="-",I,k){let ie="";(h<0||k&&h<=0)&&(k?h=1-h:(h=-h,ie=D));let ve=String(h);for(;ve.length<T;)ve="0"+ve;return I&&(ve=ve.slice(ve.length-T)),ie+ve}function Yt(h,T,D=0,I=!1,k=!1){return function(ie,ve){let Ze=function xi(h,T){switch(h){case ht.FullYear:return T.getFullYear();case ht.Month:return T.getMonth();case ht.Date:return T.getDate();case ht.Hours:return T.getHours();case ht.Minutes:return T.getMinutes();case ht.Seconds:return T.getSeconds();case ht.FractionalSeconds:return T.getMilliseconds();case ht.Day:return T.getDay();default:throw new Error(`Unknown DateType value "${h}".`)}}(h,ie);if((D>0||Ze>-D)&&(Ze+=D),h===ht.Hours)0===Ze&&-12===D&&(Ze=12);else if(h===ht.FractionalSeconds)return function ui(h,T){return pn(h,3).substring(0,T)}(Ze,T);const ln=Y(ve,nt.MinusSign);return pn(Ze,T,ln,I,k)}}function ze(h,T,D=Pe.Format,I=!1){return function(k,ie){return function Br(h,T,D,I,k,ie){switch(D){case st.Months:return function nn(h,T,D){const I=(0,c.H5H)(h),ie=yt([I[c.KH2.MonthsFormat],I[c.KH2.MonthsStandalone]],T);return yt(ie,D)}(T,k,I)[h.getMonth()];case st.Days:return function Re(h,T,D){const I=(0,c.H5H)(h),ie=yt([I[c.KH2.DaysFormat],I[c.KH2.DaysStandalone]],T);return yt(ie,D)}(T,k,I)[h.getDay()];case st.DayPeriods:const ve=h.getHours(),Ze=h.getMinutes();if(ie){const un=function xt(h){const T=(0,c.H5H)(h);return lt(T),(T[c.KH2.ExtraData][2]||[]).map(I=>"string"==typeof I?yn(I):[yn(I[0]),yn(I[1])])}(T),Ht=function qt(h,T,D){const I=(0,c.H5H)(h);lt(I);const ie=yt([I[c.KH2.ExtraData][0],I[c.KH2.ExtraData][1]],T)||[];return yt(ie,D)||[]}(T,k,I),St=un.findIndex(sn=>{if(Array.isArray(sn)){const[_,a]=sn,l=ve>=_.hours&&Ze>=_.minutes,y=ve<a.hours||ve===a.hours&&Ze<a.minutes;if(_.hours<a.hours){if(l&&y)return!0}else if(l||y)return!0}else if(sn.hours===ve&&sn.minutes===Ze)return!0;return!1});if(-1!==St)return Ht[St]}return function Hn(h,T,D){const I=(0,c.H5H)(h),ie=yt([I[c.KH2.DayPeriodsFormat],I[c.KH2.DayPeriodsStandalone]],T);return yt(ie,D)}(T,k,I)[ve<12?0:1];case st.Eras:return function je(h,T){return yt((0,c.H5H)(h)[c.KH2.Eras],T)}(T,I)[h.getFullYear()<=0?0:1];default:throw new Error(`unexpected translation type ${D}`)}}(k,ie,h,T,D,I)}}function $r(h){return function(T,D,I){const k=-1*I,ie=Y(D,nt.MinusSign),ve=k>0?Math.floor(k/60):Math.ceil(k/60);switch(h){case fr.Short:return(k>=0?"+":"")+pn(ve,2,ie)+pn(Math.abs(k%60),2,ie);case fr.ShortGMT:return"GMT"+(k>=0?"+":"")+pn(ve,1,ie);case fr.Long:return"GMT"+(k>=0?"+":"")+pn(ve,2,ie)+":"+pn(Math.abs(k%60),2,ie);case fr.Extended:return 0===I?"Z":(k>=0?"+":"")+pn(ve,2,ie)+":"+pn(Math.abs(k%60),2,ie);default:throw new Error(`Unknown zone width "${h}"`)}}}const Sn=0,ir=4;function At(h){return zn(h.getFullYear(),h.getMonth(),h.getDate()+(ir-h.getDay()))}function Ji(h,T=!1){return function(D,I){let k;if(T){const ie=new Date(D.getFullYear(),D.getMonth(),1).getDay()-1,ve=D.getDate();k=1+Math.floor((ve+ie)/7)}else{const ie=At(D),ve=function _t(h){const T=zn(h,Sn,1).getDay();return zn(h,0,1+(T<=ir?ir:ir+7)-T)}(ie.getFullYear()),Ze=ie.getTime()-ve.getTime();k=1+Math.round(Ze/6048e5)}return pn(k,h,Y(I,nt.MinusSign))}}function hr(h,T=!1){return function(D,I){return pn(At(D).getFullYear(),h,Y(I,nt.MinusSign),T)}}const vt={};function Gn(h,T){h=h.replace(/:/g,"");const D=Date.parse("Jan 01, 1970 00:00:00 "+h)/6e4;return isNaN(D)?T:D}function V(h){return h instanceof Date&&!isNaN(h.valueOf())}function Yr(h,T){T=encodeURIComponent(T);for(const D of h.split(";")){const I=D.indexOf("="),[k,ie]=-1==I?[D,""]:[D.slice(0,I),D.slice(I+1)];if(k.trim()===T)return decodeURIComponent(ie)}return null}const sr=/\s+/,ct=[];let Ut=(()=>{class h{constructor(D,I,k,ie){this._iterableDiffers=D,this._keyValueDiffers=I,this._ngEl=k,this._renderer=ie,this.initialClasses=ct,this.stateMap=new Map}set klass(D){this.initialClasses=null!=D?D.trim().split(sr):ct}set ngClass(D){this.rawClass="string"==typeof D?D.trim().split(sr):D}ngDoCheck(){for(const I of this.initialClasses)this._updateState(I,!0);const D=this.rawClass;if(Array.isArray(D)||D instanceof Set)for(const I of D)this._updateState(I,!0);else if(null!=D)for(const I of Object.keys(D))this._updateState(I,!!D[I]);this._applyStateDiff()}_updateState(D,I){const k=this.stateMap.get(D);void 0!==k?(k.enabled!==I&&(k.changed=!0,k.enabled=I),k.touched=!0):this.stateMap.set(D,{enabled:I,changed:!0,touched:!0})}_applyStateDiff(){for(const D of this.stateMap){const I=D[0],k=D[1];k.changed?(this._toggleClass(I,k.enabled),k.changed=!1):k.touched||(k.enabled&&this._toggleClass(I,!1),this.stateMap.delete(I)),k.touched=!1}}_toggleClass(D,I){(D=D.trim()).length>0&&D.split(sr).forEach(k=>{I?this._renderer.addClass(this._ngEl.nativeElement,k):this._renderer.removeClass(this._ngEl.nativeElement,k)})}static{this.\u0275fac=function(I){return new(I||h)(c.rXU(c._q3),c.rXU(c.MKu),c.rXU(c.aKT),c.rXU(c.sFG))}}static{this.\u0275dir=c.FsC({type:h,selectors:[["","ngClass",""]],inputs:{klass:["class","klass"],ngClass:"ngClass"},standalone:!0})}}return h})();class gt{constructor(T,D,I,k){this.$implicit=T,this.ngForOf=D,this.index=I,this.count=k}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let Xn=(()=>{class h{set ngForOf(D){this._ngForOf=D,this._ngForOfDirty=!0}set ngForTrackBy(D){this._trackByFn=D}get ngForTrackBy(){return this._trackByFn}constructor(D,I,k){this._viewContainer=D,this._template=I,this._differs=k,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(D){D&&(this._template=D)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const D=this._ngForOf;!this._differ&&D&&(this._differ=this._differs.find(D).create(this.ngForTrackBy))}if(this._differ){const D=this._differ.diff(this._ngForOf);D&&this._applyChanges(D)}}_applyChanges(D){const I=this._viewContainer;D.forEachOperation((k,ie,ve)=>{if(null==k.previousIndex)I.createEmbeddedView(this._template,new gt(k.item,this._ngForOf,-1,-1),null===ve?void 0:ve);else if(null==ve)I.remove(null===ie?void 0:ie);else if(null!==ie){const Ze=I.get(ie);I.move(Ze,ve),ci(Ze,k)}});for(let k=0,ie=I.length;k<ie;k++){const Ze=I.get(k).context;Ze.index=k,Ze.count=ie,Ze.ngForOf=this._ngForOf}D.forEachIdentityChange(k=>{ci(I.get(k.currentIndex),k)})}static ngTemplateContextGuard(D,I){return!0}static{this.\u0275fac=function(I){return new(I||h)(c.rXU(c.c1b),c.rXU(c.C4Q),c.rXU(c._q3))}}static{this.\u0275dir=c.FsC({type:h,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return h})();function ci(h,T){h.context.$implicit=T.item}let Or=(()=>{class h{constructor(D,I){this._viewContainer=D,this._context=new gr,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=I}set ngIf(D){this._context.$implicit=this._context.ngIf=D,this._updateView()}set ngIfThen(D){Nr("ngIfThen",D),this._thenTemplateRef=D,this._thenViewRef=null,this._updateView()}set ngIfElse(D){Nr("ngIfElse",D),this._elseTemplateRef=D,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(D,I){return!0}static{this.\u0275fac=function(I){return new(I||h)(c.rXU(c.c1b),c.rXU(c.C4Q))}}static{this.\u0275dir=c.FsC({type:h,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return h})();class gr{constructor(){this.$implicit=null,this.ngIf=null}}function Nr(h,T){if(T&&!T.createEmbeddedView)throw new Error(`${h} must be a TemplateRef, but received '${(0,c.Tbb)(T)}'.`)}function jn(h,T){return new c.wOt(2100,!1)}const fi=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g;let to=(()=>{class h{transform(D){if(null==D)return null;if("string"!=typeof D)throw jn();return D.replace(fi,I=>I[0].toUpperCase()+I.slice(1).toLowerCase())}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275pipe=c.EJ8({name:"titlecase",type:h,pure:!0,standalone:!0})}}return h})();const Po=new c.nKC("DATE_PIPE_DEFAULT_TIMEZONE"),Ho=new c.nKC("DATE_PIPE_DEFAULT_OPTIONS");let ei=(()=>{class h{constructor(D,I,k){this.locale=D,this.defaultTimezone=I,this.defaultOptions=k}transform(D,I,k,ie){if(null==D||""===D||D!=D)return null;try{return wr(D,I??this.defaultOptions?.dateFormat??"mediumDate",ie||this.locale,k??this.defaultOptions?.timezone??this.defaultTimezone??void 0)}catch(ve){throw jn()}}static{this.\u0275fac=function(I){return new(I||h)(c.rXU(c.xe9,16),c.rXU(Po,24),c.rXU(Ho,24))}}static{this.\u0275pipe=c.EJ8({name:"date",type:h,pure:!0,standalone:!0})}}return h})(),Pn=(()=>{class h{transform(D,I,k){if(null==D)return null;if(!this.supports(D))throw jn();return D.slice(I,k)}supports(D){return"string"==typeof D||Array.isArray(D)}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275pipe=c.EJ8({name:"slice",type:h,pure:!1,standalone:!0})}}return h})(),Ui=(()=>{class h{static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275mod=c.$C({type:h})}static{this.\u0275inj=c.G2t({})}}return h})();const pi="browser",Bi="server";function mt(h){return h===Bi}let Tn=(()=>{class h{static{this.\u0275prov=(0,c.jDH)({token:h,providedIn:"root",factory:()=>new Mr((0,c.KVO)(oe),window)})}}return h})();class Mr{constructor(T,D){this.document=T,this.window=D,this.offset=()=>[0,0]}setOffset(T){this.offset=Array.isArray(T)?()=>T:T}getScrollPosition(){return this.supportsScrolling()?[this.window.pageXOffset,this.window.pageYOffset]:[0,0]}scrollToPosition(T){this.supportsScrolling()&&this.window.scrollTo(T[0],T[1])}scrollToAnchor(T){if(!this.supportsScrolling())return;const D=function gn(h,T){const D=h.getElementById(T)||h.getElementsByName(T)[0];if(D)return D;if("function"==typeof h.createTreeWalker&&h.body&&"function"==typeof h.body.attachShadow){const I=h.createTreeWalker(h.body,NodeFilter.SHOW_ELEMENT);let k=I.currentNode;for(;k;){const ie=k.shadowRoot;if(ie){const ve=ie.getElementById(T)||ie.querySelector(`[name="${T}"]`);if(ve)return ve}k=I.nextNode()}}return null}(this.document,T);D&&(this.scrollToElement(D),D.focus())}setHistoryScrollRestoration(T){this.supportsScrolling()&&(this.window.history.scrollRestoration=T)}scrollToElement(T){const D=T.getBoundingClientRect(),I=D.left+this.window.pageXOffset,k=D.top+this.window.pageYOffset,ie=this.offset();this.window.scrollTo(I-ie[0],k-ie[1])}supportsScrolling(){try{return!!this.window&&!!this.window.scrollTo&&"pageXOffset"in this.window}catch{return!1}}}class ri{}},1626:(Je,me,O)=>{O.d(me,{Lr:()=>De,Nl:()=>Ce,Qq:()=>Ln,a7:()=>lt,q1:()=>W});var c=O(540),C=O(7673),ue=O(6648),se=O(1985),Q=O(274),oe=O(5964),ne=O(6354),fe=O(980),ae=O(5558),pe=O(177);class z{}class G{}class De{constructor(j){this.normalizedNames=new Map,this.lazyUpdate=null,j?"string"==typeof j?this.lazyInit=()=>{this.headers=new Map,j.split("\n").forEach($=>{const he=$.indexOf(":");if(he>0){const Ie=$.slice(0,he),Ue=Ie.toLowerCase(),Ne=$.slice(he+1).trim();this.maybeSetNormalizedName(Ie,Ue),this.headers.has(Ue)?this.headers.get(Ue).push(Ne):this.headers.set(Ue,[Ne])}})}:typeof Headers<"u"&&j instanceof Headers?(this.headers=new Map,j.forEach(($,he)=>{this.setHeaderEntries(he,$)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(j).forEach(([$,he])=>{this.setHeaderEntries($,he)})}:this.headers=new Map}has(j){return this.init(),this.headers.has(j.toLowerCase())}get(j){this.init();const $=this.headers.get(j.toLowerCase());return $&&$.length>0?$[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(j){return this.init(),this.headers.get(j.toLowerCase())||null}append(j,$){return this.clone({name:j,value:$,op:"a"})}set(j,$){return this.clone({name:j,value:$,op:"s"})}delete(j,$){return this.clone({name:j,value:$,op:"d"})}maybeSetNormalizedName(j,$){this.normalizedNames.has($)||this.normalizedNames.set($,j)}init(){this.lazyInit&&(this.lazyInit instanceof De?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(j=>this.applyUpdate(j)),this.lazyUpdate=null))}copyFrom(j){j.init(),Array.from(j.headers.keys()).forEach($=>{this.headers.set($,j.headers.get($)),this.normalizedNames.set($,j.normalizedNames.get($))})}clone(j){const $=new De;return $.lazyInit=this.lazyInit&&this.lazyInit instanceof De?this.lazyInit:this,$.lazyUpdate=(this.lazyUpdate||[]).concat([j]),$}applyUpdate(j){const $=j.name.toLowerCase();switch(j.op){case"a":case"s":let he=j.value;if("string"==typeof he&&(he=[he]),0===he.length)return;this.maybeSetNormalizedName(j.name,$);const Ie=("a"===j.op?this.headers.get($):void 0)||[];Ie.push(...he),this.headers.set($,Ie);break;case"d":const Ue=j.value;if(Ue){let Ne=this.headers.get($);if(!Ne)return;Ne=Ne.filter($t=>-1===Ue.indexOf($t)),0===Ne.length?(this.headers.delete($),this.normalizedNames.delete($)):this.headers.set($,Ne)}else this.headers.delete($),this.normalizedNames.delete($)}}setHeaderEntries(j,$){const he=(Array.isArray($)?$:[$]).map(Ue=>Ue.toString()),Ie=j.toLowerCase();this.headers.set(Ie,he),this.maybeSetNormalizedName(j,Ie)}forEach(j){this.init(),Array.from(this.normalizedNames.keys()).forEach($=>j(this.normalizedNames.get($),this.headers.get($)))}}class Fe{encodeKey(j){return Vt(j)}encodeValue(j){return Vt(j)}decodeKey(j){return decodeURIComponent(j)}decodeValue(j){return decodeURIComponent(j)}}const Mt=/%(\d[a-f0-9])/gi,Lt={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Vt(X){return encodeURIComponent(X).replace(Mt,(j,$)=>Lt[$]??j)}function Qt(X){return`${X}`}class Ce{constructor(j={}){if(this.updates=null,this.cloneFrom=null,this.encoder=j.encoder||new Fe,j.fromString){if(j.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=function kt(X,j){const $=new Map;return X.length>0&&X.replace(/^\?/,"").split("&").forEach(Ie=>{const Ue=Ie.indexOf("="),[Ne,$t]=-1==Ue?[j.decodeKey(Ie),""]:[j.decodeKey(Ie.slice(0,Ue)),j.decodeValue(Ie.slice(Ue+1))],ke=$.get(Ne)||[];ke.push($t),$.set(Ne,ke)}),$}(j.fromString,this.encoder)}else j.fromObject?(this.map=new Map,Object.keys(j.fromObject).forEach($=>{const he=j.fromObject[$],Ie=Array.isArray(he)?he.map(Qt):[Qt(he)];this.map.set($,Ie)})):this.map=null}has(j){return this.init(),this.map.has(j)}get(j){this.init();const $=this.map.get(j);return $?$[0]:null}getAll(j){return this.init(),this.map.get(j)||null}keys(){return this.init(),Array.from(this.map.keys())}append(j,$){return this.clone({param:j,value:$,op:"a"})}appendAll(j){const $=[];return Object.keys(j).forEach(he=>{const Ie=j[he];Array.isArray(Ie)?Ie.forEach(Ue=>{$.push({param:he,value:Ue,op:"a"})}):$.push({param:he,value:Ie,op:"a"})}),this.clone($)}set(j,$){return this.clone({param:j,value:$,op:"s"})}delete(j,$){return this.clone({param:j,value:$,op:"d"})}toString(){return this.init(),this.keys().map(j=>{const $=this.encoder.encodeKey(j);return this.map.get(j).map(he=>$+"="+this.encoder.encodeValue(he)).join("&")}).filter(j=>""!==j).join("&")}clone(j){const $=new Ce({encoder:this.encoder});return $.cloneFrom=this.cloneFrom||this,$.updates=(this.updates||[]).concat(j),$}init(){null===this.map&&(this.map=new Map),null!==this.cloneFrom&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(j=>this.map.set(j,this.cloneFrom.map.get(j))),this.updates.forEach(j=>{switch(j.op){case"a":case"s":const $=("a"===j.op?this.map.get(j.param):void 0)||[];$.push(Qt(j.value)),this.map.set(j.param,$);break;case"d":if(void 0===j.value){this.map.delete(j.param);break}{let he=this.map.get(j.param)||[];const Ie=he.indexOf(Qt(j.value));-1!==Ie&&he.splice(Ie,1),he.length>0?this.map.set(j.param,he):this.map.delete(j.param)}}}),this.cloneFrom=this.updates=null)}}class Ve{constructor(){this.map=new Map}set(j,$){return this.map.set(j,$),this}get(j){return this.map.has(j)||this.map.set(j,j.defaultValue()),this.map.get(j)}delete(j){return this.map.delete(j),this}has(j){return this.map.has(j)}keys(){return this.map.keys()}}function Pe(X){return typeof ArrayBuffer<"u"&&X instanceof ArrayBuffer}function Oe(X){return typeof Blob<"u"&&X instanceof Blob}function be(X){return typeof FormData<"u"&&X instanceof FormData}class jt{constructor(j,$,he,Ie){let Ue;if(this.url=$,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=j.toUpperCase(),function tt(X){switch(X){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}(this.method)||Ie?(this.body=void 0!==he?he:null,Ue=Ie):Ue=he,Ue&&(this.reportProgress=!!Ue.reportProgress,this.withCredentials=!!Ue.withCredentials,Ue.responseType&&(this.responseType=Ue.responseType),Ue.headers&&(this.headers=Ue.headers),Ue.context&&(this.context=Ue.context),Ue.params&&(this.params=Ue.params)),this.headers||(this.headers=new De),this.context||(this.context=new Ve),this.params){const Ne=this.params.toString();if(0===Ne.length)this.urlWithParams=$;else{const $t=$.indexOf("?");this.urlWithParams=$+(-1===$t?"?":$t<$.length-1?"&":"")+Ne}}else this.params=new Ce,this.urlWithParams=$}serializeBody(){return null===this.body?null:Pe(this.body)||Oe(this.body)||be(this.body)||function nt(X){return typeof URLSearchParams<"u"&&X instanceof URLSearchParams}(this.body)||"string"==typeof this.body?this.body:this.body instanceof Ce?this.body.toString():"object"==typeof this.body||"boolean"==typeof this.body||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return null===this.body||be(this.body)?null:Oe(this.body)?this.body.type||null:Pe(this.body)?null:"string"==typeof this.body?"text/plain":this.body instanceof Ce?"application/x-www-form-urlencoded;charset=UTF-8":"object"==typeof this.body||"number"==typeof this.body||"boolean"==typeof this.body?"application/json":null}clone(j={}){const $=j.method||this.method,he=j.url||this.url,Ie=j.responseType||this.responseType,Ue=void 0!==j.body?j.body:this.body,Ne=void 0!==j.withCredentials?j.withCredentials:this.withCredentials,$t=void 0!==j.reportProgress?j.reportProgress:this.reportProgress;let ke=j.headers||this.headers,dn=j.params||this.params;const Nn=j.context??this.context;return void 0!==j.setHeaders&&(ke=Object.keys(j.setHeaders).reduce((Dn,tr)=>Dn.set(tr,j.setHeaders[tr]),ke)),j.setParams&&(dn=Object.keys(j.setParams).reduce((Dn,tr)=>Dn.set(tr,j.setParams[tr]),dn)),new jt($,he,Ue,{params:dn,headers:ke,context:Nn,reportProgress:$t,responseType:Ie,withCredentials:Ne})}}var Mn=function(X){return X[X.Sent=0]="Sent",X[X.UploadProgress=1]="UploadProgress",X[X.ResponseHeader=2]="ResponseHeader",X[X.DownloadProgress=3]="DownloadProgress",X[X.Response=4]="Response",X[X.User=5]="User",X}(Mn||{});class Hn{constructor(j,$=200,he="OK"){this.headers=j.headers||new De,this.status=void 0!==j.status?j.status:$,this.statusText=j.statusText||he,this.url=j.url||null,this.ok=this.status>=200&&this.status<300}}class Re extends Hn{constructor(j={}){super(j),this.type=Mn.ResponseHeader}clone(j={}){return new Re({headers:j.headers||this.headers,status:void 0!==j.status?j.status:this.status,statusText:j.statusText||this.statusText,url:j.url||this.url||void 0})}}class nn extends Hn{constructor(j={}){super(j),this.type=Mn.Response,this.body=void 0!==j.body?j.body:null}clone(j={}){return new nn({body:void 0!==j.body?j.body:this.body,headers:j.headers||this.headers,status:void 0!==j.status?j.status:this.status,statusText:j.statusText||this.statusText,url:j.url||this.url||void 0})}}class je extends Hn{constructor(j){super(j,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.message=this.status>=200&&this.status<300?`Http failure during parsing for ${j.url||"(unknown url)"}`:`Http failure response for ${j.url||"(unknown url)"}: ${j.status} ${j.statusText}`,this.error=j.error||null}}function ft(X,j){return{body:j,headers:X.headers,context:X.context,observe:X.observe,params:X.params,reportProgress:X.reportProgress,responseType:X.responseType,withCredentials:X.withCredentials}}let Ln=(()=>{class X{constructor($){this.handler=$}request($,he,Ie={}){let Ue;if($ instanceof jt)Ue=$;else{let ke,dn;ke=Ie.headers instanceof De?Ie.headers:new De(Ie.headers),Ie.params&&(dn=Ie.params instanceof Ce?Ie.params:new Ce({fromObject:Ie.params})),Ue=new jt($,he,void 0!==Ie.body?Ie.body:null,{headers:ke,context:Ie.context,params:dn,reportProgress:Ie.reportProgress,responseType:Ie.responseType||"json",withCredentials:Ie.withCredentials})}const Ne=(0,C.of)(Ue).pipe((0,Q.H)(ke=>this.handler.handle(ke)));if($ instanceof jt||"events"===Ie.observe)return Ne;const $t=Ne.pipe((0,oe.p)(ke=>ke instanceof nn));switch(Ie.observe||"body"){case"body":switch(Ue.responseType){case"arraybuffer":return $t.pipe((0,ne.T)(ke=>{if(null!==ke.body&&!(ke.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return ke.body}));case"blob":return $t.pipe((0,ne.T)(ke=>{if(null!==ke.body&&!(ke.body instanceof Blob))throw new Error("Response is not a Blob.");return ke.body}));case"text":return $t.pipe((0,ne.T)(ke=>{if(null!==ke.body&&"string"!=typeof ke.body)throw new Error("Response is not a string.");return ke.body}));default:return $t.pipe((0,ne.T)(ke=>ke.body))}case"response":return $t;default:throw new Error(`Unreachable: unhandled observe type ${Ie.observe}}`)}}delete($,he={}){return this.request("DELETE",$,he)}get($,he={}){return this.request("GET",$,he)}head($,he={}){return this.request("HEAD",$,he)}jsonp($,he){return this.request("JSONP",$,{params:(new Ce).append(he,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options($,he={}){return this.request("OPTIONS",$,he)}patch($,he,Ie={}){return this.request("PATCH",$,ft(Ie,he))}post($,he,Ie={}){return this.request("POST",$,ft(Ie,he))}put($,he,Ie={}){return this.request("PUT",$,ft(Ie,he))}static{this.\u0275fac=function(he){return new(he||X)(c.KVO(z))}}static{this.\u0275prov=c.jDH({token:X,factory:X.\u0275fac})}}return X})();function Ge(X,j){return j(X)}function dt(X,j){return($,he)=>j.intercept($,{handle:Ie=>X(Ie,he)})}const lt=new c.nKC(""),xt=new c.nKC(""),qt=new c.nKC("");function rr(){let X=null;return(j,$)=>{null===X&&(X=((0,c.WQX)(lt,{optional:!0})??[]).reduceRight(dt,Ge));const he=(0,c.WQX)(c.$K3),Ie=he.add();return X(j,$).pipe((0,fe.j)(()=>he.remove(Ie)))}}let yt=(()=>{class X extends z{constructor($,he){super(),this.backend=$,this.injector=he,this.chain=null,this.pendingTasks=(0,c.WQX)(c.$K3)}handle($){if(null===this.chain){const Ie=Array.from(new Set([...this.injector.get(xt),...this.injector.get(qt,[])]));this.chain=Ie.reduceRight((Ue,Ne)=>function zt(X,j,$){return(he,Ie)=>$.runInContext(()=>j(he,Ue=>X(Ue,Ie)))}(Ue,Ne,this.injector),Ge)}const he=this.pendingTasks.add();return this.chain($,Ie=>this.backend.handle(Ie)).pipe((0,fe.j)(()=>this.pendingTasks.remove(he)))}static{this.\u0275fac=function(he){return new(he||X)(c.KVO(G),c.KVO(c.uvJ))}}static{this.\u0275prov=c.jDH({token:X,factory:X.\u0275fac})}}return X})();const zn=/^\)\]\}',?\n/;let rn=(()=>{class X{constructor($){this.xhrFactory=$}handle($){if("JSONP"===$.method)throw new c.wOt(-2800,!1);const he=this.xhrFactory;return(he.\u0275loadImpl?(0,ue.H)(he.\u0275loadImpl()):(0,C.of)(null)).pipe((0,ae.n)(()=>new se.c(Ue=>{const Ne=he.build();if(Ne.open($.method,$.urlWithParams),$.withCredentials&&(Ne.withCredentials=!0),$.headers.forEach((ct,Ut)=>Ne.setRequestHeader(ct,Ut.join(","))),$.headers.has("Accept")||Ne.setRequestHeader("Accept","application/json, text/plain, */*"),!$.headers.has("Content-Type")){const ct=$.detectContentTypeHeader();null!==ct&&Ne.setRequestHeader("Content-Type",ct)}if($.responseType){const ct=$.responseType.toLowerCase();Ne.responseType="json"!==ct?ct:"text"}const $t=$.serializeBody();let ke=null;const dn=()=>{if(null!==ke)return ke;const ct=Ne.statusText||"OK",Ut=new De(Ne.getAllResponseHeaders()),In=function vn(X){return"responseURL"in X&&X.responseURL?X.responseURL:/^X-Request-URL:/m.test(X.getAllResponseHeaders())?X.getResponseHeader("X-Request-URL"):null}(Ne)||$.url;return ke=new Re({headers:Ut,status:Ne.status,statusText:ct,url:In}),ke},Nn=()=>{let{headers:ct,status:Ut,statusText:In,url:Kn}=dn(),gt=null;204!==Ut&&(gt=typeof Ne.response>"u"?Ne.responseText:Ne.response),0===Ut&&(Ut=gt?200:0);let Xn=Ut>=200&&Ut<300;if("json"===$.responseType&&"string"==typeof gt){const ci=gt;gt=gt.replace(zn,"");try{gt=""!==gt?JSON.parse(gt):null}catch(di){gt=ci,Xn&&(Xn=!1,gt={error:di,text:gt})}}Xn?(Ue.next(new nn({body:gt,headers:ct,status:Ut,statusText:In,url:Kn||void 0})),Ue.complete()):Ue.error(new je({error:gt,headers:ct,status:Ut,statusText:In,url:Kn||void 0}))},Dn=ct=>{const{url:Ut}=dn(),In=new je({error:ct,status:Ne.status||0,statusText:Ne.statusText||"Unknown Error",url:Ut||void 0});Ue.error(In)};let tr=!1;const Yr=ct=>{tr||(Ue.next(dn()),tr=!0);let Ut={type:Mn.DownloadProgress,loaded:ct.loaded};ct.lengthComputable&&(Ut.total=ct.total),"text"===$.responseType&&Ne.responseText&&(Ut.partialText=Ne.responseText),Ue.next(Ut)},sr=ct=>{let Ut={type:Mn.UploadProgress,loaded:ct.loaded};ct.lengthComputable&&(Ut.total=ct.total),Ue.next(Ut)};return Ne.addEventListener("load",Nn),Ne.addEventListener("error",Dn),Ne.addEventListener("timeout",Dn),Ne.addEventListener("abort",Dn),$.reportProgress&&(Ne.addEventListener("progress",Yr),null!==$t&&Ne.upload&&Ne.upload.addEventListener("progress",sr)),Ne.send($t),Ue.next({type:Mn.Sent}),()=>{Ne.removeEventListener("error",Dn),Ne.removeEventListener("abort",Dn),Ne.removeEventListener("load",Nn),Ne.removeEventListener("timeout",Dn),$.reportProgress&&(Ne.removeEventListener("progress",Yr),null!==$t&&Ne.upload&&Ne.upload.removeEventListener("progress",sr)),Ne.readyState!==Ne.DONE&&Ne.abort()}})))}static{this.\u0275fac=function(he){return new(he||X)(c.KVO(pe.N0))}}static{this.\u0275prov=c.jDH({token:X,factory:X.\u0275fac})}}return X})();const pn=new c.nKC("XSRF_ENABLED"),Yt=new c.nKC("XSRF_COOKIE_NAME",{providedIn:"root",factory:()=>"XSRF-TOKEN"}),ze=new c.nKC("XSRF_HEADER_NAME",{providedIn:"root",factory:()=>"X-XSRF-TOKEN"});class Br{}let $r=(()=>{class X{constructor($,he,Ie){this.doc=$,this.platform=he,this.cookieName=Ie,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if("server"===this.platform)return null;const $=this.doc.cookie||"";return $!==this.lastCookieString&&(this.parseCount++,this.lastToken=(0,pe._b)($,this.cookieName),this.lastCookieString=$),this.lastToken}static{this.\u0275fac=function(he){return new(he||X)(c.KVO(pe.qQ),c.KVO(c.Agw),c.KVO(Yt))}}static{this.\u0275prov=c.jDH({token:X,factory:X.\u0275fac})}}return X})();function Sn(X,j){const $=X.url.toLowerCase();if(!(0,c.WQX)(pn)||"GET"===X.method||"HEAD"===X.method||$.startsWith("http://")||$.startsWith("https://"))return j(X);const he=(0,c.WQX)(Br).getToken(),Ie=(0,c.WQX)(ze);return null!=he&&!X.headers.has(Ie)&&(X=X.clone({headers:X.headers.set(Ie,he)})),j(X)}var _t=function(X){return X[X.Interceptors=0]="Interceptors",X[X.LegacyInterceptors=1]="LegacyInterceptors",X[X.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",X[X.NoXsrfProtection=3]="NoXsrfProtection",X[X.JsonpSupport=4]="JsonpSupport",X[X.RequestsMadeViaParent=5]="RequestsMadeViaParent",X[X.Fetch=6]="Fetch",X}(_t||{});function At(X,j){return{\u0275kind:X,\u0275providers:j}}function Ji(...X){const j=[Ln,rn,yt,{provide:z,useExisting:yt},{provide:G,useExisting:rn},{provide:xt,useValue:Sn,multi:!0},{provide:pn,useValue:!0},{provide:Br,useClass:$r}];for(const $ of X)j.push(...$.\u0275providers);return(0,c.EmA)(j)}const vt=new c.nKC("LEGACY_INTERCEPTOR_FN");let W=(()=>{class X{static{this.\u0275fac=function(he){return new(he||X)}}static{this.\u0275mod=c.$C({type:X})}static{this.\u0275inj=c.G2t({providers:[Ji(At(_t.LegacyInterceptors,[{provide:vt,useFactory:rr},{provide:xt,useExisting:vt,multi:!0}]))]})}}return X})()},540:(Je,me,O)=>{O.d(me,{bc$:()=>pC,iLQ:()=>$d,sZ2:()=>Eh,hnV:()=>My,Hbi:()=>AM,o8S:()=>pa,BIS:()=>gC,gRc:()=>pM,Ql9:()=>xb,Ocv:()=>Nb,Z63:()=>Fa,aKT:()=>Va,uvJ:()=>Mo,zcH:()=>ns,bkB:()=>So,$GK:()=>ze,nKC:()=>vt,zZn:()=>co,_q3:()=>Xd,MKu:()=>Qd,xe9:()=>Du,Co$:()=>_m,Vns:()=>ws,NEm:()=>sM,SKi:()=>Ar,Xx1:()=>vl,Agw:()=>vc,PLl:()=>wh,sFG:()=>OC,_9s:()=>Ah,czy:()=>Sl,kdw:()=>Dl,C4Q:()=>nl,NYb:()=>nM,giA:()=>Ry,RxE:()=>Oh,c1b:()=>yu,gXe:()=>In,L39:()=>UM,Ol2:()=>wm,w6W:()=>E0,oH4:()=>Ly,Rfq:()=>Oe,WQX:()=>ke,QuC:()=>xr,EmA:()=>dc,fpN:()=>TM,HJs:()=>$M,O8t:()=>y,H3F:()=>Sy,H8p:()=>pc,$K3:()=>Iy,KH2:()=>ca,wOt:()=>Re,WHO:()=>Ny,e01:()=>Py,H5H:()=>gd,mq5:()=>Wg,JZv:()=>At,LfX:()=>wr,plB:()=>Pd,jNT:()=>id,zjR:()=>Qp,TL$:()=>FD,Tbb:()=>Ve,Vt3:()=>Kc,GFd:()=>Ap,OA$:()=>de,Jv_:()=>Cm,aNF:()=>bm,R7$:()=>ep,BMQ:()=>Qc,HbH:()=>yg,ZvI:()=>Rg,STu:()=>xg,AVh:()=>dd,wni:()=>iy,VBU:()=>ei,FsC:()=>hi,jDH:()=>Nt,G2t:()=>ht,$C:()=>ti,EJ8:()=>io,rXU:()=>Zs,nrm:()=>rd,bVm:()=>au,qex:()=>su,k0s:()=>ou,j41:()=>iu,RV6:()=>Xp,xGo:()=>mf,KVO:()=>Ne,kS0:()=>Lu,QTQ:()=>np,bIt:()=>od,lsd:()=>oy,XpG:()=>eg,nI1:()=>Bm,bMT:()=>$m,i5U:()=>Hm,brH:()=>zm,Y8G:()=>td,lJ4:()=>Nm,eq3:()=>Pm,l_i:()=>Rm,sMw:()=>xm,ziG:()=>Fm,mGM:()=>ny,Njj:()=>sl,eBV:()=>ol,B4B:()=>uc,n$t:()=>ph,xc7:()=>cd,DNE:()=>$p,EFF:()=>bg,JRh:()=>hd,SpI:()=>cu,Lme:()=>pd,GBs:()=>ry});var c=O(3794),C=O(8359),ue=O(1985),se=O(6365),Q=O(8750),oe=O(983),ne=O(9326),fe=O(6648),pe=O(4412),z=O(7673),G=O(7707),De=O(9974);function we(e={}){const{connector:t=(()=>new c.B),resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:i=!0}=e;return o=>{let s,f,p,E=0,A=!1,x=!1;const H=()=>{f?.unsubscribe(),f=void 0},K=()=>{H(),s=p=void 0,A=x=!1},ge=()=>{const Se=s;K(),Se?.unsubscribe()};return(0,De.N)((Se,$e)=>{E++,!x&&!A&&H();const et=p=p??t();$e.add(()=>{E--,0===E&&!x&&!A&&(f=Fe(ge,i))}),et.subscribe($e),!s&&E>0&&(s=new G.Ms({next:_e=>et.next(_e),error:_e=>{x=!0,H(),f=Fe(K,n,_e),et.error(_e)},complete:()=>{A=!0,H(),f=Fe(K,r),et.complete()}}),(0,Q.Tg)(Se).subscribe(s))})(o)}}function Fe(e,t,...n){if(!0===t)return void e();if(!1===t)return;const r=new G.Ms({next:()=>{r.unsubscribe(),e()}});return(0,Q.Tg)(t(...n)).subscribe(r)}var kt=O(5558),Mt=O(3669),Lt=O(4360);function Qt(e,t){return e===t}function Ce(e){for(let t in e)if(e[t]===Ce)return t;throw Error("Could not find renamed property on target object.")}function Ae(e,t){for(const n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Ve(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(Ve).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const t=e.toString();if(null==t)return""+t;const n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function tt(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}const Pe=Ce({__forward_ref__:Ce});function Oe(e){return e.__forward_ref__=Oe,e.toString=function(){return Ve(this())},e}function be(e){return nt(e)?e():e}function nt(e){return"function"==typeof e&&e.hasOwnProperty(Pe)&&e.__forward_ref__===Oe}function jt(e){return e&&!!e.\u0275providers}const Hn="https://g.co/ng/security#xss";class Re extends Error{constructor(t,n){super(function nn(e,t){return`NG0${Math.abs(e)}${t?": "+t:""}`}(t,n)),this.code=t}}function je(e){return"string"==typeof e?e:null==e?"":String(e)}function te(e,t){throw new Re(-201,!1)}function Gt(e,t){null==e&&function qe(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(null==r?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}(t,e,null,"!=")}function Nt(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ht(e){return{providers:e.providers||[],imports:e.imports||[]}}function st(e){return zn(e,pn)||zn(e,Yt)}function wr(e){return null!==st(e)}function zn(e,t){return e.hasOwnProperty(t)?e[t]:null}function rn(e){return e&&(e.hasOwnProperty(ui)||e.hasOwnProperty(xi))?e[ui]:null}const pn=Ce({\u0275prov:Ce}),ui=Ce({\u0275inj:Ce}),Yt=Ce({ngInjectableDef:Ce}),xi=Ce({ngInjectorDef:Ce});var ze=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(ze||{});let Br;function Sn(e){const t=Br;return Br=e,t}function ir(e,t,n){const r=st(e);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&ze.Optional?null:void 0!==t?t:void te(Ve(e))}const At=globalThis;class vt{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof n?this.__NG_ELEMENT_ID__=n:void 0!==n&&(this.\u0275prov=Nt({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}const Et={},Ft="__NG_DI_FLAG__",en="ngTempTokenPath",pr=/\n/gm,j="__source";let $;function Ie(e){const t=$;return $=e,t}function Ue(e,t=ze.Default){if(void 0===$)throw new Re(-203,!1);return null===$?ir(e,void 0,t):$.get(e,t&ze.Optional?null:void 0,t)}function Ne(e,t=ze.Default){return(function $r(){return Br}()||Ue)(be(e),t)}function ke(e,t=ze.Default){return Ne(e,dn(t))}function dn(e){return typeof e>"u"||"number"==typeof e?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Nn(e){const t=[];for(let n=0;n<e.length;n++){const r=be(e[n]);if(Array.isArray(r)){if(0===r.length)throw new Re(900,!1);let i,o=ze.Default;for(let s=0;s<r.length;s++){const f=r[s],p=tr(f);"number"==typeof p?-1===p?i=f.token:o|=p:i=f}t.push(Ne(i,o))}else t.push(Ne(r))}return t}function Dn(e,t){return e[Ft]=t,e.prototype[Ft]=t,e}function tr(e){return e[Ft]}function ct(e){return{toString:e}.toString()}var Ut=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Ut||{}),In=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(In||{});const Kn={},gt=[],Xn=Ce({\u0275cmp:Ce}),ci=Ce({\u0275dir:Ce}),di=Ce({\u0275pipe:Ce}),Or=Ce({\u0275mod:Ce}),gr=Ce({\u0275fac:Ce}),Nr=Ce({__NG_ELEMENT_ID__:Ce}),Fi=Ce({__NG_ENV_ID__:Ce});function nr(e,t,n){let r=e.length;for(;;){const i=e.indexOf(t,n);if(-1===i)return i;if(0===i||e.charCodeAt(i-1)<=32){const o=t.length;if(i+o===r||e.charCodeAt(i+o)<=32)return i}n=i+1}}function zr(e,t,n){let r=0;for(;r<n.length;){const i=n[r];if("number"==typeof i){if(0!==i)break;r++;const o=n[r++],s=n[r++],f=n[r++];e.setAttribute(t,s,f,o)}else{const o=i,s=n[++r];ki(o)?e.setProperty(t,o,s):e.setAttribute(t,o,s),r++}}return r}function wi(e){return 3===e||4===e||6===e}function ki(e){return 64===e.charCodeAt(0)}function Qn(e,t){if(null!==t&&0!==t.length)if(null===e||0===e.length)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){const i=t[r];"number"==typeof i?n=i:0===n||mr(e,n,i,null,-1===n||2===n?t[++r]:null)}}return e}function mr(e,t,n,r,i){let o=0,s=e.length;if(-1===t)s=-1;else for(;o<e.length;){const f=e[o++];if("number"==typeof f){if(f===t){s=-1;break}if(f>t){s=o-1;break}}}for(;o<e.length;){const f=e[o];if("number"==typeof f)break;if(f===n){if(null===r)return void(null!==i&&(e[o+1]=i));if(r===e[o+1])return void(e[o+2]=i)}o++,null!==r&&o++,null!==i&&o++}-1!==s&&(e.splice(s,0,t),o=s+1),e.splice(o++,0,n),null!==r&&e.splice(o++,0,r),null!==i&&e.splice(o++,0,i)}const bi="ng-template";function Pr(e,t,n){let r=0,i=!0;for(;r<e.length;){let o=e[r++];if("string"==typeof o&&i){const s=e[r++];if(n&&"class"===o&&-1!==nr(s.toLowerCase(),t,0))return!0}else{if(1===o){for(;r<e.length&&"string"==typeof(o=e[r++]);)if(o.toLowerCase()===t)return!0;return!1}"number"==typeof o&&(i=!1)}}return!1}function Li(e){return 4===e.type&&e.value!==bi}function jn(e,t,n){return t===(4!==e.type||n?e.value:bi)}function Yi(e,t,n){let r=4;const i=e.attrs||[],o=function Vi(e){for(let t=0;t<e.length;t++)if(wi(e[t]))return t;return e.length}(i);let s=!1;for(let f=0;f<t.length;f++){const p=t[f];if("number"!=typeof p){if(!s)if(4&r){if(r=2|1&r,""!==p&&!jn(e,p,n)||""===p&&1===t.length){if(yr(r))return!1;s=!0}}else{const E=8&r?p:t[++f];if(8&r&&null!==e.attrs){if(!Pr(e.attrs,E,n)){if(yr(r))return!1;s=!0}continue}const x=No(8&r?"class":p,i,Li(e),n);if(-1===x){if(yr(r))return!1;s=!0;continue}if(""!==E){let H;H=x>o?"":i[x+1].toLowerCase();const K=8&r?H:null;if(K&&-1!==nr(K,E,0)||2&r&&E!==H){if(yr(r))return!1;s=!0}}}}else{if(!s&&!yr(r)&&!yr(p))return!1;if(s&&yr(p))continue;s=!1,r=p|1&r}}return yr(r)||s}function yr(e){return 0==(1&e)}function No(e,t,n,r){if(null===t)return-1;let i=0;if(r||!n){let o=!1;for(;i<t.length;){const s=t[i];if(s===e)return i;if(3===s||6===s)o=!0;else{if(1===s||2===s){let f=t[++i];for(;"string"==typeof f;)f=t[++i];continue}if(4===s)break;if(0===s){i+=4;continue}}i+=o?1:2}return-1}return function fi(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){const r=e[n];if("number"==typeof r)return-1;if(r===t)return n;n++}return-1}(t,e)}function Rr(e,t,n=!1){for(let r=0;r<t.length;r++)if(Yi(e,t[r],n))return!0;return!1}function po(e,t){return e?":not("+t.trim()+")":t}function no(e){let t=e[0],n=1,r=2,i="",o=!1;for(;n<e.length;){let s=e[n];if("string"==typeof s)if(2&r){const f=e[++n];i+="["+s+(f.length>0?'="'+f+'"':"")+"]"}else 8&r?i+="."+s:4&r&&(i+=" "+s);else""!==i&&!yr(s)&&(t+=po(o,i),i=""),r=s,o=o||!yr(r);n++}return""!==i&&(t+=po(o,i)),t}function ei(e){return ct(()=>{const t=br(e),n={...t,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Ut.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||In.Emulated,styles:e.styles||gt,_:null,schemas:e.schemas||null,tView:null,id:""};Ui(n);const r=e.dependencies;return n.directiveDefs=pi(r,!1),n.pipeDefs=pi(r,!0),n.id=function oo(e){let t=0;const n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(const i of n)t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}(n),n})}function Gr(e){return pt(e)||Cn(e)}function go(e){return null!==e}function ti(e){return ct(()=>({type:e.type,bootstrap:e.bootstrap||gt,declarations:e.declarations||gt,imports:e.imports||gt,exports:e.exports||gt,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function ji(e,t){if(null==e)return Kn;const n={};for(const r in e)if(e.hasOwnProperty(r)){let i=e[r],o=i;Array.isArray(i)&&(o=i[1],i=i[0]),n[i]=r,t&&(t[i]=o)}return n}function hi(e){return ct(()=>{const t=br(e);return Ui(t),t})}function io(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,standalone:!0===e.standalone,onDestroy:e.type.prototype.ngOnDestroy||null}}function pt(e){return e[Xn]||null}function Cn(e){return e[ci]||null}function wn(e){return e[di]||null}function xr(e){const t=pt(e)||Cn(e)||wn(e);return null!==t&&t.standalone}function Pn(e,t){const n=e[Or]||null;if(!n&&!0===t)throw new Error(`Type ${Ve(e)} does not have '\u0275mod' property.`);return n}function br(e){const t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Kn,exportAs:e.exportAs||null,standalone:!0===e.standalone,signals:!0===e.signals,selectors:e.selectors||gt,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:ji(e.inputs,t),outputs:ji(e.outputs)}}function Ui(e){e.features?.forEach(t=>t(e))}function pi(e,t){if(!e)return null;const n=t?wn:Gr;return()=>("function"==typeof e?e():e).map(r=>n(r)).filter(go)}const an=0,Le=1,mt=2,on=3,qn=4,$i=5,Tn=6,Mr=7,gn=8,Zn=9,ri=10,ut=11,Fr=12,Hi=13,Wr=14,fn=15,xn=16,ar=17,Fn=18,gi=19,Ro=20,Kr=21,kr=22,Mi=23,zi=24,wt=25,mi=1,vr=2,Sr=7,Dr=9,Un=11;function On(e){return Array.isArray(e)&&"object"==typeof e[mi]}function kn(e){return Array.isArray(e)&&!0===e[mi]}function Gi(e){return 0!=(4&e.flags)}function Ir(e){return e.componentOffset>-1}function Wt(e){return 1==(1&e.flags)}function lr(e){return!!e.template}function mo(e){return 0!=(512&e[mt])}function Wi(e,t){return e.hasOwnProperty(gr)?e[gr]:null}let Zt=null,Xr=!1;function Lr(e){const t=Zt;return Zt=e,t}const as={version:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{}};function so(e){if(!Co(e)||e.dirty){if(!e.producerMustRecompute(e)&&!ls(e))return void(e.dirty=!1);e.producerRecomputeValue(e),e.dirty=!1}}function Ko(e){e.dirty=!0,function Wo(e){if(void 0===e.liveConsumerNode)return;const t=Xr;Xr=!0;try{for(const n of e.liveConsumerNode)n.dirty||Ko(n)}finally{Xr=t}}(e),e.consumerMarkedDirty?.(e)}function Xo(e){return e&&(e.nextProducerIndex=0),Lr(e)}function Qo(e,t){if(Lr(t),e&&void 0!==e.producerNode&&void 0!==e.producerIndexOfThis&&void 0!==e.producerLastReadVersion){if(Co(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Lo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function ls(e){lo(e);for(let t=0;t<e.producerNode.length;t++){const n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(so(n),r!==n.version))return!0}return!1}function us(e){if(lo(e),Co(e))for(let t=0;t<e.producerNode.length;t++)Lo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Lo(e,t){if(function cs(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}(e),lo(e),1===e.liveConsumerNode.length)for(let r=0;r<e.producerNode.length;r++)Lo(e.producerNode[r],e.producerIndexOfThis[r]);const n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){const r=e.liveConsumerIndexOfThis[t],i=e.liveConsumerNode[t];lo(i),i.producerIndexOfThis[r]=t}}function Co(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function lo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}let k=null;function y(e){const t=Lr(null);try{return e()}finally{Lr(t)}}const R=()=>{},L=(()=>({...as,consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!1,consumerMarkedDirty:e=>{e.schedule(e.ref)},hasRun:!1,cleanupFn:R}))();class J{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}}function de(){return Be}function Be(e){return e.type.prototype.ngOnChanges&&(e.setInput=Pt),Ke}function Ke(){const e=rt(this),t=e?.current;if(t){const n=e.previous;if(n===Kn)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Pt(e,t,n,r){const i=this.declaredInputs[n],o=rt(e)||function Ct(e,t){return e[It]=t}(e,{previous:Kn,current:null}),s=o.current||(o.current={}),f=o.previous,p=f[i];s[i]=new J(p&&p.currentValue,t,f===Kn),e[r]=t}de.ngInherit=!0;const It="__ngSimpleChanges__";function rt(e){return e[It]||null}const hn=function(e,t,n){};function Jt(e){for(;Array.isArray(e);)e=e[an];return e}function Rn(e,t){return Jt(t[e])}function bn(e,t){return Jt(t[e.index])}function oi(e,t){return e.data[t]}function _o(e,t){return e[t]}function cr(e,t){const n=t[e];return On(n)?n:n[an]}function Qe(e,t){return null==t?null:e[t]}function at(e){e[ar]=0}function it(e){1024&e[mt]||(e[mt]|=1024,Yn(e,1))}function Bt(e){1024&e[mt]&&(e[mt]&=-1025,Yn(e,-1))}function Yn(e,t){let n=e[on];if(null===n)return;n[$i]+=t;let r=n;for(n=n[on];null!==n&&(1===t&&1===r[$i]||-1===t&&0===r[$i]);)n[$i]+=t,r=n,n=n[on]}const ot={lFrame:Qi(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function Tr(){return ot.bindingsEnabled}function Ee(){return ot.lFrame.lView}function Kt(){return ot.lFrame.tView}function ol(e){return ot.lFrame.contextLView=e,e[gn]}function sl(e){return ot.lFrame.contextLView=null,e}function Cr(){let e=al();for(;null!==e&&64===e.type;)e=e.parent;return e}function al(){return ot.lFrame.currentTNode}function Xi(e,t){const n=ot.lFrame;n.currentTNode=e,n.isParent=t}function Ca(){return ot.lFrame.isParent}function As(){ot.lFrame.isParent=!1}function qr(){const e=ot.lFrame;let t=e.bindingRootIndex;return-1===t&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function ps(){return ot.lFrame.bindingIndex++}function d(e){const t=ot.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function m(e,t){const n=ot.lFrame;n.bindingIndex=n.bindingRootIndex=e,N(t)}function N(e){ot.lFrame.currentDirectiveIndex=e}function re(){return ot.lFrame.currentQueryIndex}function ee(e){ot.lFrame.currentQueryIndex=e}function He(e){const t=e[Le];return 2===t.type?t.declTNode:1===t.type?e[Tn]:null}function bt(e,t,n){if(n&ze.SkipSelf){let i=t,o=e;for(;!(i=i.parent,null!==i||n&ze.Host||(i=He(o),null===i||(o=o[Wr],10&i.type))););if(null===i)return!1;t=i,e=o}const r=ot.lFrame=Xt();return r.currentTNode=t,r.lView=e,!0}function cn(e){const t=Xt(),n=e[Le];ot.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Xt(){const e=ot.lFrame,t=null===e?null:e.child;return null===t?Qi(e):t}function Qi(e){const t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=t),t}function Zo(){const e=ot.lFrame;return ot.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const Jo=Zo;function gs(){const e=Zo();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function si(){return ot.lFrame.selectedIndex}function ms(e){ot.lFrame.selectedIndex=e}function $n(){const e=ot.lFrame;return oi(e.tView,e.selectedIndex)}let nf=!0;function ll(){return nf}function es(e){nf=e}function ul(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){const o=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:f,ngAfterViewInit:p,ngAfterViewChecked:E,ngOnDestroy:A}=o;s&&(e.contentHooks??=[]).push(-n,s),f&&((e.contentHooks??=[]).push(n,f),(e.contentCheckHooks??=[]).push(n,f)),p&&(e.viewHooks??=[]).push(-n,p),E&&((e.viewHooks??=[]).push(n,E),(e.viewCheckHooks??=[]).push(n,E)),null!=A&&(e.destroyHooks??=[]).push(n,A)}}function cl(e,t,n){rf(e,t,3,n)}function dl(e,t,n,r){(3&e[mt])===n&&rf(e,t,n,r)}function Ou(e,t){let n=e[mt];(3&n)===t&&(n&=8191,n+=1,e[mt]=n)}function rf(e,t,n,r){const o=r??-1,s=t.length-1;let f=0;for(let p=void 0!==r?65535&e[ar]:0;p<s;p++)if("number"==typeof t[p+1]){if(f=t[p],null!=r&&f>=r)break}else t[p]<0&&(e[ar]+=65536),(f<o||-1==o)&&(mv(e,n,t,p),e[ar]=(**********&e[ar])+p+2),p++}function sf(e,t){hn(4,e,t);const n=Lr(null);try{t.call(e)}finally{Lr(n),hn(5,e,t)}}function mv(e,t,n,r){const i=n[r]<0,o=n[r+1],f=e[i?-n[r]:n[r]];i?e[mt]>>13<e[ar]>>16&&(3&e[mt])===t&&(e[mt]+=8192,sf(f,o)):sf(f,o)}const Os=-1;class _a{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}}function Pu(e){return e!==Os}function Ea(e){return 32767&e}function wa(e,t){let n=function Cv(e){return e>>16}(e),r=t;for(;n>0;)r=r[Wr],n--;return r}let Ru=!0;function fl(e){const t=Ru;return Ru=e,t}const af=255,lf=5;let _v=0;const bo={};function hl(e,t){const n=uf(e,t);if(-1!==n)return n;const r=t[Le];r.firstCreatePass&&(e.injectorIndex=t.length,xu(r.data,e),xu(t,null),xu(r.blueprint,null));const i=pl(e,t),o=e.injectorIndex;if(Pu(i)){const s=Ea(i),f=wa(i,t),p=f[Le].data;for(let E=0;E<8;E++)t[o+E]=f[s+E]|p[s+E]}return t[o+8]=i,o}function xu(e,t){e.push(0,0,0,0,0,0,0,0,t)}function uf(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===t[e.injectorIndex+8]?-1:e.injectorIndex}function pl(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let n=0,r=null,i=t;for(;null!==i;){if(r=yf(i),null===r)return Os;if(n++,i=i[Wr],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return Os}function Fu(e,t,n){!function Ev(e,t,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(Nr)&&(r=n[Nr]),null==r&&(r=n[Nr]=_v++);const i=r&af;t.data[e+(i>>lf)]|=1<<i}(e,t,n)}function cf(e,t,n){if(n&ze.Optional||void 0!==e)return e;te()}function df(e,t,n,r){if(n&ze.Optional&&void 0===r&&(r=null),!(n&(ze.Self|ze.Host))){const i=e[Zn],o=Sn(void 0);try{return i?i.get(t,r,n&ze.Optional):ir(t,r,n&ze.Optional)}finally{Sn(o)}}return cf(r,0,n)}function ff(e,t,n,r=ze.Default,i){if(null!==e){if(2048&t[mt]&&!(r&ze.Self)){const s=function Tv(e,t,n,r,i){let o=e,s=t;for(;null!==o&&null!==s&&2048&s[mt]&&!(512&s[mt]);){const f=hf(o,s,n,r|ze.Self,bo);if(f!==bo)return f;let p=o.parent;if(!p){const E=s[Ro];if(E){const A=E.get(n,bo,r);if(A!==bo)return A}p=yf(s),s=s[Wr]}o=p}return i}(e,t,n,r,bo);if(s!==bo)return s}const o=hf(e,t,n,r,bo);if(o!==bo)return o}return df(t,n,r,i)}function hf(e,t,n,r,i){const o=function Mv(e){if("string"==typeof e)return e.charCodeAt(0)||0;const t=e.hasOwnProperty(Nr)?e[Nr]:void 0;return"number"==typeof t?t>=0?t&af:Iv:t}(n);if("function"==typeof o){if(!bt(t,e,r))return r&ze.Host?cf(i,0,r):df(t,n,r,i);try{let s;if(s=o(r),null!=s||r&ze.Optional)return s;te()}finally{Jo()}}else if("number"==typeof o){let s=null,f=uf(e,t),p=Os,E=r&ze.Host?t[fn][Tn]:null;for((-1===f||r&ze.SkipSelf)&&(p=-1===f?pl(e,t):t[f+8],p!==Os&&gf(r,!1)?(s=t[Le],f=Ea(p),t=wa(p,t)):f=-1);-1!==f;){const A=t[Le];if(pf(o,f,A.data)){const x=bv(f,t,n,s,r,E);if(x!==bo)return x}p=t[f+8],p!==Os&&gf(r,t[Le].data[f+8]===E)&&pf(o,f,t)?(s=A,f=Ea(p),t=wa(p,t)):f=-1}}return i}function bv(e,t,n,r,i,o){const s=t[Le],f=s.data[e+8],A=gl(f,s,n,null==r?Ir(f)&&Ru:r!=s&&0!=(3&f.type),i&ze.Host&&o===f);return null!==A?ys(t,s,A,f):bo}function gl(e,t,n,r,i){const o=e.providerIndexes,s=t.data,f=1048575&o,p=e.directiveStart,A=o>>20,H=i?f+A:e.directiveEnd;for(let K=r?f:f+A;K<H;K++){const ge=s[K];if(K<p&&n===ge||K>=p&&ge.type===n)return K}if(i){const K=s[p];if(K&&lr(K)&&K.type===n)return p}return null}function ys(e,t,n,r){let i=e[n];const o=t.data;if(function yv(e){return e instanceof _a}(i)){const s=i;s.resolving&&function Ln(e,t){const n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new Re(-200,`Circular dependency in DI detected for ${e}${n}`)}(function ft(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():je(e)}(o[n]));const f=fl(s.canSeeViewProviders);s.resolving=!0;const E=s.injectImpl?Sn(s.injectImpl):null;bt(e,r,ze.Default);try{i=e[n]=s.factory(void 0,o,e,r),t.firstCreatePass&&n>=r.directiveStart&&function gv(e,t,n){const{ngOnChanges:r,ngOnInit:i,ngDoCheck:o}=t.type.prototype;if(r){const s=Be(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}i&&(n.preOrderHooks??=[]).push(0-e,i),o&&((n.preOrderHooks??=[]).push(e,o),(n.preOrderCheckHooks??=[]).push(e,o))}(n,o[n],t)}finally{null!==E&&Sn(E),fl(f),s.resolving=!1,Jo()}}return i}function pf(e,t,n){return!!(n[t+(e>>lf)]&1<<e)}function gf(e,t){return!(e&ze.Self||e&ze.Host&&t)}class ai{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return ff(this._tNode,this._lView,t,dn(r),n)}}function Iv(){return new ai(Cr(),Ee())}function mf(e){return ct(()=>{const t=e.prototype.constructor,n=t[gr]||ku(t),r=Object.prototype;let i=Object.getPrototypeOf(e.prototype).constructor;for(;i&&i!==r;){const o=i[gr]||ku(i);if(o&&o!==n)return o;i=Object.getPrototypeOf(i)}return o=>new o})}function ku(e){return nt(e)?()=>{const t=ku(be(e));return t&&t()}:Wi(e)}function yf(e){const t=e[Le],n=t.type;return 2===n?t.declTNode:1===n?e[Tn]:null}function Lu(e){return function wv(e,t){if("class"===t)return e.classes;if("style"===t)return e.styles;const n=e.attrs;if(n){const r=n.length;let i=0;for(;i<r;){const o=n[i];if(wi(o))break;if(0===o)i+=2;else if("number"==typeof o)for(i++;i<r&&"string"==typeof n[i];)i++;else{if(o===t)return n[i+1];i+=2}}}return null}(Cr(),e)}const Ps="__parameters__";function xs(e,t,n){return ct(()=>{const r=function Vu(e){return function(...n){if(e){const r=e(...n);for(const i in r)this[i]=r[i]}}}(t);function i(...o){if(this instanceof i)return r.apply(this,o),this;const s=new i(...o);return f.annotation=s,f;function f(p,E,A){const x=p.hasOwnProperty(Ps)?p[Ps]:Object.defineProperty(p,Ps,{value:[]})[Ps];for(;x.length<=A;)x.push(null);return(x[A]=x[A]||[]).push(s),p}}return n&&(i.prototype=Object.create(n.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i})}function ks(e,t){e.forEach(n=>Array.isArray(n)?ks(n,t):t(n))}function Df(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function ml(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Pi(e,t,n){let r=Ls(e,t);return r>=0?e[1|r]=n:(r=~r,function Fv(e,t,n,r){let i=e.length;if(i==t)e.push(n,r);else if(1===i)e.push(r,e[0]),e[0]=n;else{for(i--,e.push(e[i-1],e[i]);i>t;)e[i]=e[i-2],i--;e[t]=n,e[t+1]=r}}(e,r,t,n)),r}function ju(e,t){const n=Ls(e,t);if(n>=0)return e[1|n]}function Ls(e,t){return function Cf(e,t,n){let r=0,i=e.length>>n;for(;i!==r;){const o=r+(i-r>>1),s=e[o<<n];if(t===s)return o<<n;s>t?i=o:r=o+1}return~(i<<n)}(e,t,1)}const vl=Dn(xs("Optional"),8),Dl=Dn(xs("SkipSelf"),4);function bl(e){return 128==(128&e.flags)}var Sl=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Sl||{});const nD=/^>|^->|<!--|-->|--!>|<!-$/g,rD=/(<|>)/g,iD="\u200b$1\u200b";const zu=new Map;let oD=0;const Wu="__ngContext__";function Zr(e,t){On(t)?(e[Wu]=t[gi],function aD(e){zu.set(e[gi],e)}(t)):e[Wu]=t}let Ku;function Xu(e,t){return Ku(e,t)}function Aa(e){const t=e[on];return kn(t)?t[on]:t}function Uf(e){return $f(e[Fr])}function Bf(e){return $f(e[qn])}function $f(e){for(;null!==e&&!kn(e);)e=e[qn];return e}function Us(e,t,n,r,i){if(null!=r){let o,s=!1;kn(r)?o=r:On(r)&&(s=!0,r=r[an]);const f=Jt(r);0===e&&null!==n?null==i?Wf(t,n,f):vs(t,n,f,i||null,!0):1===e&&null!==n?vs(t,n,f,i||null,!0):2===e?function Pl(e,t,n){const r=Ol(e,t);r&&function SD(e,t,n,r){e.removeChild(t,n,r)}(e,r,t,n)}(t,f,s):3===e&&t.destroyNode(f),null!=o&&function AD(e,t,n,r,i){const o=n[Sr];o!==Jt(n)&&Us(t,e,r,o,i);for(let f=Un;f<n.length;f++){const p=n[f];Na(p[Le],p,e,t,r,o)}}(t,e,o,n,i)}}function Qu(e,t){return e.createComment(function Pf(e){return e.replace(nD,t=>t.replace(rD,iD))}(t))}function Tl(e,t,n){return e.createElement(t,n)}function zf(e,t){const n=e[Dr],r=n.indexOf(t);Bt(t),n.splice(r,1)}function Al(e,t){if(e.length<=Un)return;const n=Un+t,r=e[n];if(r){const i=r[xn];null!==i&&i!==e&&zf(i,r),t>0&&(e[n-1][qn]=r[qn]);const o=ml(e,Un+t);!function vD(e,t){Na(e,t,t[ut],2,null,null),t[an]=null,t[Tn]=null}(r[Le],r);const s=o[Fn];null!==s&&s.detachView(o[Le]),r[on]=null,r[qn]=null,r[mt]&=-129}return r}function qu(e,t){if(!(256&t[mt])){const n=t[ut];t[Mi]&&us(t[Mi]),t[zi]&&us(t[zi]),n.destroyNode&&Na(e,t,n,3,null,null),function _D(e){let t=e[Fr];if(!t)return Zu(e[Le],e);for(;t;){let n=null;if(On(t))n=t[Fr];else{const r=t[Un];r&&(n=r)}if(!n){for(;t&&!t[qn]&&t!==e;)On(t)&&Zu(t[Le],t),t=t[on];null===t&&(t=e),On(t)&&Zu(t[Le],t),n=t&&t[qn]}t=n}}(t)}}function Zu(e,t){if(!(256&t[mt])){t[mt]&=-129,t[mt]|=256,function MD(e,t){let n;if(null!=e&&null!=(n=e.destroyHooks))for(let r=0;r<n.length;r+=2){const i=t[n[r]];if(!(i instanceof _a)){const o=n[r+1];if(Array.isArray(o))for(let s=0;s<o.length;s+=2){const f=i[o[s]],p=o[s+1];hn(4,f,p);try{p.call(f)}finally{hn(5,f,p)}}else{hn(4,i,o);try{o.call(i)}finally{hn(5,i,o)}}}}}(e,t),function bD(e,t){const n=e.cleanup,r=t[Mr];if(null!==n)for(let o=0;o<n.length-1;o+=2)if("string"==typeof n[o]){const s=n[o+3];s>=0?r[s]():r[-s].unsubscribe(),o+=2}else n[o].call(r[n[o+1]]);null!==r&&(t[Mr]=null);const i=t[Kr];if(null!==i){t[Kr]=null;for(let o=0;o<i.length;o++)(0,i[o])()}}(e,t),1===t[Le].type&&t[ut].destroy();const n=t[xn];if(null!==n&&kn(t[on])){n!==t[on]&&zf(n,t);const r=t[Fn];null!==r&&r.detachView(e)}!function lD(e){zu.delete(e[gi])}(t)}}function Ju(e,t,n){return function Gf(e,t,n){let r=t;for(;null!==r&&40&r.type;)r=(t=r).parent;if(null===r)return n[an];{const{componentOffset:i}=r;if(i>-1){const{encapsulation:o}=e.data[r.directiveStart+i];if(o===In.None||o===In.Emulated)return null}return bn(r,n)}}(e,t.parent,n)}function vs(e,t,n,r,i){e.insertBefore(t,n,r,i)}function Wf(e,t,n){e.appendChild(t,n)}function Kf(e,t,n,r,i){null!==r?vs(e,t,n,r,i):Wf(e,t,n)}function Ol(e,t){return e.parentNode(t)}let Yu,rc,xl,qf=function Qf(e,t,n){return 40&e.type?bn(e,n):null};function Nl(e,t,n,r){const i=Ju(e,r,t),o=t[ut],f=function Xf(e,t,n){return qf(e,t,n)}(r.parent||t[Tn],r,t);if(null!=i)if(Array.isArray(n))for(let p=0;p<n.length;p++)Kf(o,i,n[p],f,!1);else Kf(o,i,n,f,!1);void 0!==Yu&&Yu(o,r,t,n,i)}function Oa(e,t){if(null!==t){const n=t.type;if(3&n)return bn(t,e);if(4&n)return ec(-1,e[t.index]);if(8&n){const r=t.child;if(null!==r)return Oa(e,r);{const i=e[t.index];return kn(i)?ec(-1,i):Jt(i)}}if(32&n)return Xu(t,e)()||Jt(e[t.index]);{const r=Jf(e,t);return null!==r?Array.isArray(r)?r[0]:Oa(Aa(e[fn]),r):Oa(e,t.next)}}return null}function Jf(e,t){return null!==t?e[fn][Tn].projection[t.projection]:null}function ec(e,t){const n=Un+e+1;if(n<t.length){const r=t[n],i=r[Le].firstChild;if(null!==i)return Oa(r,i)}return t[Sr]}function tc(e,t,n,r,i,o,s){for(;null!=n;){const f=r[n.index],p=n.type;if(s&&0===t&&(f&&Zr(Jt(f),r),n.flags|=2),32!=(32&n.flags))if(8&p)tc(e,t,n.child,r,i,o,!1),Us(t,e,i,f,o);else if(32&p){const E=Xu(n,r);let A;for(;A=E();)Us(t,e,i,A,o);Us(t,e,i,f,o)}else 16&p?eh(e,t,r,n,i,o):Us(t,e,i,f,o);n=s?n.projectionNext:n.next}}function Na(e,t,n,r,i,o){tc(n,r,e.firstChild,t,i,o,!1)}function eh(e,t,n,r,i,o){const s=n[fn],p=s[Tn].projection[r.projection];if(Array.isArray(p))for(let E=0;E<p.length;E++)Us(t,e,i,p[E],o);else{let E=p;const A=s[on];bl(r)&&(E.flags|=128),tc(e,t,E,A,i,o,!0)}}function th(e,t,n){""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function nh(e,t,n){const{mergedAttrs:r,classes:i,styles:o}=n;null!==r&&zr(e,t,r),null!==i&&th(e,t,i),null!==o&&function ND(e,t,n){e.setAttribute(t,"style",n)}(e,t,o)}function FD(e){rc=e}function oh(e){return function ic(){if(void 0===xl&&(xl=null,At.trustedTypes))try{xl=At.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return xl}()?.createScriptURL(e)||e}class sh{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Hn})`}}function ts(e){return e instanceof sh?e.changingThisBreaksApplicationSecurity:e}function Pa(e,t){const n=function BD(e){return e instanceof sh&&e.getTypeName()||null}(e);if(null!=n&&n!==t){if("ResourceURL"===n&&"URL"===t)return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Hn})`)}return n===t}const GD=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;var Hs=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Hs||{});function uc(e){const t=xa();return t?t.sanitize(Hs.URL,e)||"":Pa(e,"URL")?ts(e):function oc(e){return(e=String(e)).match(GD)?e:"unsafe:"+e}(je(e))}function hh(e){const t=xa();if(t)return oh(t.sanitize(Hs.RESOURCE_URL,e)||"");if(Pa(e,"ResourceURL"))return oh(ts(e));throw new Re(904,!1)}function ph(e,t,n){return function rC(e,t){return"src"===t&&("embed"===e||"frame"===e||"iframe"===e||"media"===e||"script"===e)||"href"===t&&("base"===e||"link"===e)?hh:uc}(t,n)(e)}function xa(){const e=Ee();return e&&e[ri].sanitizer}const Fa=new vt("ENVIRONMENT_INITIALIZER"),gh=new vt("INJECTOR",-1),mh=new vt("INJECTOR_DEF_TYPES");class cc{get(t,n=Et){if(n===Et){const r=new Error(`NullInjectorError: No provider for ${Ve(t)}!`);throw r.name="NullInjectorError",r}return n}}function dc(e){return{\u0275providers:e}}function iC(...e){return{\u0275providers:yh(0,e),\u0275fromNgModule:!0}}function yh(e,...t){const n=[],r=new Set;let i;const o=s=>{n.push(s)};return ks(t,s=>{const f=s;kl(f,o,[],r)&&(i||=[],i.push(f))}),void 0!==i&&vh(i,o),n}function vh(e,t){for(let n=0;n<e.length;n++){const{ngModule:r,providers:i}=e[n];fc(i,o=>{t(o,r)})}}function kl(e,t,n,r){if(!(e=be(e)))return!1;let i=null,o=rn(e);const s=!o&&pt(e);if(o||s){if(s&&!s.standalone)return!1;i=e}else{const p=e.ngModule;if(o=rn(p),!o)return!1;i=p}const f=r.has(i);if(s){if(f)return!1;if(r.add(i),s.dependencies){const p="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const E of p)kl(E,t,n,r)}}else{if(!o)return!1;{if(null!=o.imports&&!f){let E;r.add(i);try{ks(o.imports,A=>{kl(A,t,n,r)&&(E||=[],E.push(A))})}finally{}void 0!==E&&vh(E,t)}if(!f){const E=Wi(i)||(()=>new i);t({provide:i,useFactory:E,deps:gt},i),t({provide:mh,useValue:i,multi:!0},i),t({provide:Fa,useValue:()=>Ne(i),multi:!0},i)}const p=o.providers;if(null!=p&&!f){const E=e;fc(p,A=>{t(A,E)})}}}return i!==e&&void 0!==e.providers}function fc(e,t){for(let n of e)jt(n)&&(n=n.\u0275providers),Array.isArray(n)?fc(n,t):t(n)}const oC=Ce({provide:String,useValue:Ce});function hc(e){return null!==e&&"object"==typeof e&&oC in e}function Ds(e){return"function"==typeof e}const pc=new vt("Set Injector scope."),Ll={},aC={};let gc;function Vl(){return void 0===gc&&(gc=new cc),gc}class Mo{}class zs extends Mo{get destroyed(){return this._destroyed}constructor(t,n,r,i){super(),this.parent=n,this.source=r,this.scopes=i,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,yc(t,s=>this.processProvider(s)),this.records.set(gh,Gs(void 0,this)),i.has("environment")&&this.records.set(Mo,Gs(void 0,this));const o=this.records.get(pc);null!=o&&"string"==typeof o.value&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(mh.multi,gt,ze.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{for(const n of this._ngOnDestroyHooks)n.ngOnDestroy();const t=this._onDestroyHooks;this._onDestroyHooks=[];for(const n of t)n()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear()}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();const n=Ie(this),r=Sn(void 0);try{return t()}finally{Ie(n),Sn(r)}}get(t,n=Et,r=ze.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Fi))return t[Fi](this);r=dn(r);const o=Ie(this),s=Sn(void 0);try{if(!(r&ze.SkipSelf)){let p=this.records.get(t);if(void 0===p){const E=function fC(e){return"function"==typeof e||"object"==typeof e&&e instanceof vt}(t)&&st(t);p=E&&this.injectableDefInScope(E)?Gs(mc(t),Ll):null,this.records.set(t,p)}if(null!=p)return this.hydrate(t,p)}return(r&ze.Self?Vl():this.parent).get(t,n=r&ze.Optional&&n===Et?null:n)}catch(f){if("NullInjectorError"===f.name){if((f[en]=f[en]||[]).unshift(Ve(t)),o)throw f;return function Yr(e,t,n,r){const i=e[en];throw t[j]&&i.unshift(t[j]),e.message=function sr(e,t,n,r=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.slice(2):e;let i=Ve(t);if(Array.isArray(t))i=t.map(Ve).join(" -> ");else if("object"==typeof t){let o=[];for(let s in t)if(t.hasOwnProperty(s)){let f=t[s];o.push(s+":"+("string"==typeof f?JSON.stringify(f):Ve(f)))}i=`{${o.join(", ")}}`}return`${n}${r?"("+r+")":""}[${i}]: ${e.replace(pr,"\n  ")}`}("\n"+e.message,i,n,r),e.ngTokenPath=i,e[en]=null,e}(f,t,"R3InjectorError",this.source)}throw f}finally{Sn(s),Ie(o)}}resolveInjectorInitializers(){const t=Ie(this),n=Sn(void 0);try{const i=this.get(Fa.multi,gt,ze.Self);for(const o of i)o()}finally{Ie(t),Sn(n)}}toString(){const t=[],n=this.records;for(const r of n.keys())t.push(Ve(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new Re(205,!1)}processProvider(t){let n=Ds(t=be(t))?t:be(t&&t.provide);const r=function uC(e){return hc(e)?Gs(void 0,e.useValue):Gs(_h(e),Ll)}(t);if(Ds(t)||!0!==t.multi)this.records.get(n);else{let i=this.records.get(n);i||(i=Gs(void 0,Ll,!0),i.factory=()=>Nn(i.multi),this.records.set(n,i)),n=t,i.multi.push(t)}this.records.set(n,r)}hydrate(t,n){return n.value===Ll&&(n.value=aC,n.value=n.factory()),"object"==typeof n.value&&n.value&&function dC(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}injectableDefInScope(t){if(!t.providedIn)return!1;const n=be(t.providedIn);return"string"==typeof n?"any"===n||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){const n=this._onDestroyHooks.indexOf(t);-1!==n&&this._onDestroyHooks.splice(n,1)}}function mc(e){const t=st(e),n=null!==t?t.factory:Wi(e);if(null!==n)return n;if(e instanceof vt)throw new Re(204,!1);if(e instanceof Function)return function lC(e){const t=e.length;if(t>0)throw function Sa(e,t){const n=[];for(let r=0;r<e;r++)n.push(t);return n}(t,"?"),new Re(204,!1);const n=function vn(e){return e&&(e[pn]||e[Yt])||null}(e);return null!==n?()=>n.factory(e):()=>new e}(e);throw new Re(204,!1)}function _h(e,t,n){let r;if(Ds(e)){const i=be(e);return Wi(i)||mc(i)}if(hc(e))r=()=>be(e.useValue);else if(function Ch(e){return!(!e||!e.useFactory)}(e))r=()=>e.useFactory(...Nn(e.deps||[]));else if(function Dh(e){return!(!e||!e.useExisting)}(e))r=()=>Ne(be(e.useExisting));else{const i=be(e&&(e.useClass||e.provide));if(!function cC(e){return!!e.deps}(e))return Wi(i)||mc(i);r=()=>new i(...Nn(e.deps))}return r}function Gs(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function yc(e,t){for(const n of e)Array.isArray(n)?yc(n,t):n&&jt(n)?yc(n.\u0275providers,t):t(n)}const Eh=new vt("AppId",{providedIn:"root",factory:()=>hC}),hC="ng",wh=new vt("Platform Initializer"),vc=new vt("Platform ID",{providedIn:"platform",factory:()=>"unknown"}),pC=new vt("AnimationModuleType"),gC=new vt("CSP nonce",{providedIn:"root",factory:()=>function $s(){if(void 0!==rc)return rc;if(typeof document<"u")return document;throw new Re(210,!1)}().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});let bh=(e,t,n)=>null;function Sc(e,t,n=!1){return bh(e,t,n)}class MC{}class Ih{}class IC{resolveComponentFactory(t){throw function SC(e){const t=Error(`No component factory found for ${Ve(e)}.`);return t.ngComponent=e,t}(t)}}let zl=(()=>{class e{static{this.NULL=new IC}}return e})();function TC(){return Xs(Cr(),Ee())}function Xs(e,t){return new Va(bn(e,t))}let Va=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=TC}}return e})();function AC(e){return e instanceof Va?e.nativeElement:e}class Ah{}let OC=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>function NC(){const e=Ee(),n=cr(Cr().index,e);return(On(n)?n:e)[ut]}()}}return e})(),PC=(()=>{class e{static{this.\u0275prov=Nt({token:e,providedIn:"root",factory:()=>null})}}return e})();class Oh{constructor(t){this.full=t,this.major=t.split(".")[0],this.minor=t.split(".")[1],this.patch=t.split(".").slice(2).join(".")}}const RC=new Oh("16.2.12"),Ac={};function xh(e,t=null,n=null,r){const i=Fh(e,t,n,r);return i.resolveInjectorInitializers(),i}function Fh(e,t=null,n=null,r,i=new Set){const o=[n||gt,iC(e)];return r=r||("object"==typeof e?void 0:Ve(e)),new zs(o,t||Vl(),r||null,i)}let co=(()=>{class e{static{this.THROW_IF_NOT_FOUND=Et}static{this.NULL=new cc}static create(n,r){if(Array.isArray(n))return xh({name:""},r,n,"");{const i=n.name??"";return xh({name:i},n.parent,n.providers,i)}}static{this.\u0275prov=Nt({token:e,providedIn:"any",factory:()=>Ne(gh)})}static{this.__NG_ELEMENT_ID__=-1}}return e})();function Nc(e){return e.ngOriginalError}class ns{constructor(){this._console=console}handleError(t){const n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&Nc(t);for(;n&&Nc(n);)n=Nc(n);return n||null}}function Pc(e){return t=>{setTimeout(e,void 0,t)}}const So=class BC extends c.B{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,n,r){let i=t,o=n||(()=>null),s=r;if(t&&"object"==typeof t){const p=t;i=p.next?.bind(p),o=p.error?.bind(p),s=p.complete?.bind(p)}this.__isAsync&&(o=Pc(o),i&&(i=Pc(i)),s&&(s=Pc(s)));const f=super.subscribe({next:i,error:o,complete:s});return t instanceof C.yU&&t.add(f),f}};function Lh(...e){}class Ar{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new So(!1),this.onMicrotaskEmpty=new So(!1),this.onStable=new So(!1),this.onError=new So(!1),typeof Zone>"u")throw new Re(908,!1);Zone.assertZonePatched();const i=this;i._nesting=0,i._outer=i._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(i._inner=i._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(i._inner=i._inner.fork(Zone.longStackTraceZoneSpec)),i.shouldCoalesceEventChangeDetection=!r&&n,i.shouldCoalesceRunChangeDetection=r,i.lastRequestAnimationFrameId=-1,i.nativeRequestAnimationFrame=function $C(){const e="function"==typeof At.requestAnimationFrame;let t=At[e?"requestAnimationFrame":"setTimeout"],n=At[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){const r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);const i=n[Zone.__symbol__("OriginalDelegate")];i&&(n=i)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}().nativeRequestAnimationFrame,function GC(e){const t=()=>{!function zC(e){e.isCheckStableRunning||-1!==e.lastRequestAnimationFrameId||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(At,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,xc(e),e.isCheckStableRunning=!0,Rc(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),xc(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,i,o,s,f)=>{if(function KC(e){return!(!Array.isArray(e)||1!==e.length)&&!0===e[0].data?.__ignore_ng_zone__}(f))return n.invokeTask(i,o,s,f);try{return Vh(e),n.invokeTask(i,o,s,f)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===o.type||e.shouldCoalesceRunChangeDetection)&&t(),jh(e)}},onInvoke:(n,r,i,o,s,f,p)=>{try{return Vh(e),n.invoke(i,o,s,f,p)}finally{e.shouldCoalesceRunChangeDetection&&t(),jh(e)}},onHasTask:(n,r,i,o)=>{n.hasTask(i,o),r===i&&("microTask"==o.change?(e._hasPendingMicrotasks=o.microTask,xc(e),Rc(e)):"macroTask"==o.change&&(e.hasPendingMacrotasks=o.macroTask))},onHandleError:(n,r,i,o)=>(n.handleError(i,o),e.runOutsideAngular(()=>e.onError.emit(o)),!1)})}(i)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!Ar.isInAngularZone())throw new Re(909,!1)}static assertNotInAngularZone(){if(Ar.isInAngularZone())throw new Re(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,i){const o=this._inner,s=o.scheduleEventTask("NgZoneEvent: "+i,t,HC,Lh,Lh);try{return o.runTask(s,n,r)}finally{o.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}}const HC={};function Rc(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function xc(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&-1!==e.lastRequestAnimationFrameId)}function Vh(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function jh(e){e._nesting--,Rc(e)}class WC{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new So,this.onMicrotaskEmpty=new So,this.onStable=new So,this.onError=new So}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,i){return t.apply(n,r)}}const Uh=new vt("",{providedIn:"root",factory:Bh});function Bh(){const e=ke(Ar);let t=!0;return function ae(...e){const t=(0,ne.lI)(e),n=(0,ne.R0)(e,1/0),r=e;return r.length?1===r.length?(0,Q.Tg)(r[0]):(0,se.U)(n)((0,fe.H)(r,t)):oe.w}(new ue.c(i=>{t=e.isStable&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks,e.runOutsideAngular(()=>{i.next(t),i.complete()})}),new ue.c(i=>{let o;e.runOutsideAngular(()=>{o=e.onStable.subscribe(()=>{Ar.assertNotInAngularZone(),queueMicrotask(()=>{!t&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks&&(t=!0,i.next(!0))})})});const s=e.onUnstable.subscribe(()=>{Ar.assertInAngularZone(),t&&(t=!1,e.runOutsideAngular(()=>{i.next(!1)}))});return()=>{o.unsubscribe(),s.unsubscribe()}}).pipe(we()))}function Uo(e){return e instanceof Function?e():e}let Fc=(()=>{class e{constructor(){this.renderDepth=0,this.handler=null}begin(){this.handler?.validateBegin(),this.renderDepth++}end(){this.renderDepth--,0===this.renderDepth&&this.handler?.execute()}ngOnDestroy(){this.handler?.destroy(),this.handler=null}static{this.\u0275prov=Nt({token:e,providedIn:"root",factory:()=>new e})}}return e})();function ja(e){for(;e;){e[mt]|=64;const t=Aa(e);if(mo(e)&&!t)return e;e=t}return null}const Wh=new vt("",{providedIn:"root",factory:()=>!1});let Kl=null;function qh(e,t){return e[t]??Yh()}function Zh(e,t){const n=Yh();n.producerNode?.length&&(e[t]=Kl,n.lView=e,Kl=Jh())}const r_={...as,consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{ja(e.lView)},lView:null};function Jh(){return Object.create(r_)}function Yh(){return Kl??=Jh(),Kl}const Ot={};function ep(e){tp(Kt(),Ee(),si()+e,!1)}function tp(e,t,n,r){if(!r)if(3==(3&t[mt])){const o=e.preOrderCheckHooks;null!==o&&cl(t,o,n)}else{const o=e.preOrderHooks;null!==o&&dl(t,o,0,n)}ms(n)}function Zs(e,t=ze.Default){const n=Ee();return null===n?Ne(e,t):ff(Cr(),n,be(e),t)}function np(){throw new Error("invalid")}function Xl(e,t,n,r,i,o,s,f,p,E,A){const x=t.blueprint.slice();return x[an]=i,x[mt]=140|r,(null!==E||e&&2048&e[mt])&&(x[mt]|=2048),at(x),x[on]=x[Wr]=e,x[gn]=n,x[ri]=s||e&&e[ri],x[ut]=f||e&&e[ut],x[Zn]=p||e&&e[Zn]||null,x[Tn]=o,x[gi]=function sD(){return oD++}(),x[kr]=A,x[Ro]=E,x[fn]=2==t.type?e[fn]:x,x}function Js(e,t,n,r,i){let o=e.data[t];if(null===o)o=function kc(e,t,n,r,i){const o=al(),s=Ca(),p=e.data[t]=function d_(e,t,n,r,i,o){let s=t?t.injectorIndex:-1,f=0;return function Ni(){return null!==ot.skipHydrationRootTNode}()&&(f|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:f,providerIndexes:0,value:i,attrs:o,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?o:o&&o.parent,n,t,r,i);return null===e.firstChild&&(e.firstChild=p),null!==o&&(s?null==o.child&&null!==p.parent&&(o.child=p):null===o.next&&(o.next=p,p.prev=o)),p}(e,t,n,r,i),function g(){return ot.lFrame.inI18n}()&&(o.flags|=32);else if(64&o.type){o.type=n,o.value=r,o.attrs=i;const s=function hs(){const e=ot.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}();o.injectorIndex=null===s?-1:s.injectorIndex}return Xi(o,!0),o}function Ua(e,t,n,r){if(0===n)return-1;const i=t.length;for(let o=0;o<n;o++)t.push(r),e.blueprint.push(r),e.data.push(null);return i}function rp(e,t,n,r,i){const o=qh(t,Mi),s=si(),f=2&r;try{ms(-1),f&&t.length>wt&&tp(e,t,wt,!1),hn(f?2:0,i);const E=f?o:null,A=Xo(E);try{null!==E&&(E.dirty=!1),n(r,i)}finally{Qo(E,A)}}finally{f&&null===t[Mi]&&Zh(t,Mi),ms(s),hn(f?3:1,i)}}function Lc(e,t,n){if(Gi(t)){const r=Lr(null);try{const o=t.directiveEnd;for(let s=t.directiveStart;s<o;s++){const f=e.data[s];f.contentQueries&&f.contentQueries(1,n[s],s)}}finally{Lr(r)}}}function Vc(e,t,n){Tr()&&(function v_(e,t,n,r){const i=n.directiveStart,o=n.directiveEnd;Ir(n)&&function M_(e,t,n){const r=bn(t,e),i=ip(n);let s=16;n.signals?s=4096:n.onPush&&(s=64);const f=Ql(e,Xl(e,i,null,s,r,t,null,e[ri].rendererFactory.createRenderer(r,n),null,null,null));e[t.index]=f}(t,n,e.data[i+n.componentOffset]),e.firstCreatePass||hl(n,t),Zr(r,t);const s=n.initialInputs;for(let f=i;f<o;f++){const p=e.data[f],E=ys(t,e,f,n);Zr(E,t),null!==s&&S_(0,f-i,E,p,0,s),lr(p)&&(cr(n.index,t)[gn]=ys(t,e,f,n))}}(e,t,n,bn(n,t)),64==(64&n.flags)&&up(e,t,n))}function jc(e,t,n=bn){const r=t.localNames;if(null!==r){let i=t.index+1;for(let o=0;o<r.length;o+=2){const s=r[o+1],f=-1===s?n(t,e):e[s];e[i++]=f}}}function ip(e){const t=e.tView;return null===t||t.incompleteFirstPass?e.tView=Uc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Uc(e,t,n,r,i,o,s,f,p,E,A){const x=wt+r,H=x+i,K=function o_(e,t){const n=[];for(let r=0;r<t;r++)n.push(r<e?null:Ot);return n}(x,H),ge="function"==typeof E?E():E;return K[Le]={type:e,blueprint:K,template:n,queries:null,viewQuery:f,declTNode:t,data:K.slice().fill(null,x),bindingStartIndex:x,expandoStartIndex:H,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof o?o():o,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:p,consts:ge,incompleteFirstPass:!1,ssrId:A}}let op=e=>null;function sp(e,t,n,r){for(let i in e)if(e.hasOwnProperty(i)){n=null===n?{}:n;const o=e[i];null===r?ap(n,t,i,o):r.hasOwnProperty(i)&&ap(n,t,r[i],o)}return n}function ap(e,t,n,r){e.hasOwnProperty(n)?e[n].push(t,r):e[n]=[t,r]}function Bc(e,t,n,r){if(Tr()){const i=null===r?null:{"":-1},o=function C_(e,t){const n=e.directiveRegistry;let r=null,i=null;if(n)for(let o=0;o<n.length;o++){const s=n[o];if(Rr(t,s.selectors,!1))if(r||(r=[]),lr(s))if(null!==s.findHostDirectiveDefs){const f=[];i=i||new Map,s.findHostDirectiveDefs(s,f,i),r.unshift(...f,s),$c(e,t,f.length)}else r.unshift(s),$c(e,t,0);else i=i||new Map,s.findHostDirectiveDefs?.(s,r,i),r.push(s)}return null===r?null:[r,i]}(e,n);let s,f;null===o?s=f=null:[s,f]=o,null!==s&&lp(e,t,n,s,i,f),i&&function __(e,t,n){if(t){const r=e.localNames=[];for(let i=0;i<t.length;i+=2){const o=n[t[i+1]];if(null==o)throw new Re(-301,!1);r.push(t[i],o)}}}(n,r,i)}n.mergedAttrs=Qn(n.mergedAttrs,n.attrs)}function lp(e,t,n,r,i,o){for(let E=0;E<r.length;E++)Fu(hl(n,t),e,r[E].type);!function w_(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}(n,e.data.length,r.length);for(let E=0;E<r.length;E++){const A=r[E];A.providersResolver&&A.providersResolver(A)}let s=!1,f=!1,p=Ua(e,t,r.length,null);for(let E=0;E<r.length;E++){const A=r[E];n.mergedAttrs=Qn(n.mergedAttrs,A.hostAttrs),b_(e,n,t,p,A),E_(p,A,i),null!==A.contentQueries&&(n.flags|=4),(null!==A.hostBindings||null!==A.hostAttrs||0!==A.hostVars)&&(n.flags|=64);const x=A.type.prototype;!s&&(x.ngOnChanges||x.ngOnInit||x.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!f&&(x.ngOnChanges||x.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),f=!0),p++}!function f_(e,t,n){const i=t.directiveEnd,o=e.data,s=t.attrs,f=[];let p=null,E=null;for(let A=t.directiveStart;A<i;A++){const x=o[A],H=n?n.get(x):null,ge=H?H.outputs:null;p=sp(x.inputs,A,p,H?H.inputs:null),E=sp(x.outputs,A,E,ge);const Se=null===p||null===s||Li(t)?null:I_(p,A,s);f.push(Se)}null!==p&&(p.hasOwnProperty("class")&&(t.flags|=8),p.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=f,t.inputs=p,t.outputs=E}(e,n,o)}function up(e,t,n){const r=n.directiveStart,i=n.directiveEnd,o=n.index,s=function w(){return ot.lFrame.currentDirectiveIndex}();try{ms(o);for(let f=r;f<i;f++){const p=e.data[f],E=t[f];N(f),(null!==p.hostBindings||0!==p.hostVars||null!==p.hostAttrs)&&D_(p,E)}}finally{ms(-1),N(s)}}function D_(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function $c(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function E_(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;lr(t)&&(n[""]=e)}}function b_(e,t,n,r,i){e.data[r]=i;const o=i.factory||(i.factory=Wi(i.type)),s=new _a(o,lr(i),Zs);e.blueprint[r]=s,n[r]=s,function m_(e,t,n,r,i){const o=i.hostBindings;if(o){let s=e.hostBindingOpCodes;null===s&&(s=e.hostBindingOpCodes=[]);const f=~t.index;(function y_(e){let t=e.length;for(;t>0;){const n=e[--t];if("number"==typeof n&&n<0)return n}return 0})(s)!=f&&s.push(f),s.push(n,r,o)}}(e,t,r,Ua(e,n,i.hostVars,Ot),i)}function Io(e,t,n,r,i,o){const s=bn(e,t);!function Hc(e,t,n,r,i,o,s){if(null==o)e.removeAttribute(t,i,n);else{const f=null==s?je(o):s(o,r||"",i);e.setAttribute(t,i,f,n)}}(t[ut],s,o,e.value,n,r,i)}function S_(e,t,n,r,i,o){const s=o[t];if(null!==s)for(let f=0;f<s.length;)cp(r,n,s[f++],s[f++],s[f++])}function cp(e,t,n,r,i){const o=Lr(null);try{const s=e.inputTransforms;null!==s&&s.hasOwnProperty(r)&&(i=s[r].call(t,i)),null!==e.setInput?e.setInput(t,i,n,r):t[r]=i}finally{Lr(o)}}function I_(e,t,n){let r=null,i=0;for(;i<n.length;){const o=n[i];if(0!==o)if(5!==o){if("number"==typeof o)break;if(e.hasOwnProperty(o)){null===r&&(r=[]);const s=e[o];for(let f=0;f<s.length;f+=2)if(s[f]===t){r.push(o,s[f+1],n[i+1]);break}}i+=2}else i+=2;else i+=4}return r}function dp(e,t,n,r){return[e,!0,!1,t,null,0,r,n,null,null,null]}function fp(e,t){const n=e.contentQueries;if(null!==n)for(let r=0;r<n.length;r+=2){const o=n[r+1];if(-1!==o){const s=e.data[o];ee(n[r]),s.contentQueries(2,t[o],o)}}}function Ql(e,t){return e[Fr]?e[Hi][qn]=t:e[Fr]=t,e[Hi]=t,t}function zc(e,t,n){ee(0);const r=Lr(null);try{t(e,n)}finally{Lr(r)}}function hp(e){return e[Mr]||(e[Mr]=[])}function pp(e){return e.cleanup||(e.cleanup=[])}function mp(e,t){const n=e[Zn],r=n?n.get(ns,null):null;r&&r.handleError(t)}function Gc(e,t,n,r,i){for(let o=0;o<n.length;){const s=n[o++],f=n[o++];cp(e.data[s],t[s],r,f,i)}}function Bo(e,t,n){const r=Rn(t,e);!function Hf(e,t,n){e.setValue(t,n)}(e[ut],r,n)}function T_(e,t){const n=cr(t,e),r=n[Le];!function A_(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}(r,n);const i=n[an];null!==i&&null===n[kr]&&(n[kr]=Sc(i,n[Zn])),Wc(r,n,n[gn])}function Wc(e,t,n){cn(t);try{const r=e.viewQuery;null!==r&&zc(1,r,n);const i=e.template;null!==i&&rp(e,t,i,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),e.staticContentQueries&&fp(e,t),e.staticViewQueries&&zc(2,e.viewQuery,n);const o=e.components;null!==o&&function O_(e,t){for(let n=0;n<t.length;n++)T_(e,t[n])}(t,o)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[mt]&=-5,gs()}}let yp=(()=>{class e{constructor(){this.all=new Set,this.queue=new Map}create(n,r,i){const o=typeof Zone>"u"?null:Zone.current,s=function M(e,t,n){const r=Object.create(L);n&&(r.consumerAllowSignalWrites=!0),r.fn=e,r.schedule=t;const i=s=>{r.cleanupFn=s};return r.ref={notify:()=>Ko(r),run:()=>{if(r.dirty=!1,r.hasRun&&!ls(r))return;r.hasRun=!0;const s=Xo(r);try{r.cleanupFn(),r.cleanupFn=R,r.fn(i)}finally{Qo(r,s)}},cleanup:()=>r.cleanupFn()},r.ref}(n,E=>{this.all.has(E)&&this.queue.set(E,o)},i);let f;this.all.add(s),s.notify();const p=()=>{s.cleanup(),f?.(),this.all.delete(s),this.queue.delete(s)};return f=r?.onDestroy(p),{destroy:p}}flush(){if(0!==this.queue.size)for(const[n,r]of this.queue)this.queue.delete(n),r?r.run(()=>n.run()):n.run()}get isQueueEmpty(){return 0===this.queue.size}static{this.\u0275prov=Nt({token:e,providedIn:"root",factory:()=>new e})}}return e})();function ql(e,t,n){let r=n?e.styles:null,i=n?e.classes:null,o=0;if(null!==t)for(let s=0;s<t.length;s++){const f=t[s];"number"==typeof f?o=f:1==o?i=tt(i,f):2==o&&(r=tt(r,f+": "+t[++s]+";"))}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=i:e.classesWithoutHost=i}function Ba(e,t,n,r,i=!1){for(;null!==n;){const o=t[n.index];null!==o&&r.push(Jt(o)),kn(o)&&vp(o,r);const s=n.type;if(8&s)Ba(e,t,n.child,r);else if(32&s){const f=Xu(n,t);let p;for(;p=f();)r.push(p)}else if(16&s){const f=Jf(t,n);if(Array.isArray(f))r.push(...f);else{const p=Aa(t[fn]);Ba(p[Le],p,f,r,!0)}}n=i?n.projectionNext:n.next}return r}function vp(e,t){for(let n=Un;n<e.length;n++){const r=e[n],i=r[Le].firstChild;null!==i&&Ba(r[Le],r,i,t)}e[Sr]!==e[an]&&t.push(e[Sr])}function Zl(e,t,n,r=!0){const i=t[ri],o=i.rendererFactory,s=i.afterRenderEventManager;o.begin?.(),s?.begin();try{Dp(e,t,e.template,n)}catch(p){throw r&&mp(t,p),p}finally{o.end?.(),i.effectManager?.flush(),s?.end()}}function Dp(e,t,n,r){const i=t[mt];if(256!=(256&i)){t[ri].effectManager?.flush(),cn(t);try{at(t),function Tu(e){return ot.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==n&&rp(e,t,n,2,r);const s=3==(3&i);if(s){const E=e.preOrderCheckHooks;null!==E&&cl(t,E,null)}else{const E=e.preOrderHooks;null!==E&&dl(t,E,0,null),Ou(t,0)}if(function R_(e){for(let t=Uf(e);null!==t;t=Bf(t)){if(!t[vr])continue;const n=t[Dr];for(let r=0;r<n.length;r++){it(n[r])}}}(t),Cp(t,2),null!==e.contentQueries&&fp(e,t),s){const E=e.contentCheckHooks;null!==E&&cl(t,E)}else{const E=e.contentHooks;null!==E&&dl(t,E,1),Ou(t,1)}!function i_(e,t){const n=e.hostBindingOpCodes;if(null===n)return;const r=qh(t,zi);try{for(let i=0;i<n.length;i++){const o=n[i];if(o<0)ms(~o);else{const s=o,f=n[++i],p=n[++i];m(f,s),r.dirty=!1;const E=Xo(r);try{p(2,t[s])}finally{Qo(r,E)}}}}finally{null===t[zi]&&Zh(t,zi),ms(-1)}}(e,t);const f=e.components;null!==f&&Ep(t,f,0);const p=e.viewQuery;if(null!==p&&zc(2,p,r),s){const E=e.viewCheckHooks;null!==E&&cl(t,E)}else{const E=e.viewHooks;null!==E&&dl(t,E,2),Ou(t,2)}!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),t[mt]&=-73,Bt(t)}finally{gs()}}}function Cp(e,t){for(let n=Uf(e);null!==n;n=Bf(n))for(let r=Un;r<n.length;r++)_p(n[r],t)}function x_(e,t,n){_p(cr(t,e),n)}function _p(e,t){if(!function qo(e){return 128==(128&e[mt])}(e))return;const n=e[Le],r=e[mt];if(80&r&&0===t||1024&r||2===t)Dp(n,e,n.template,e[gn]);else if(e[$i]>0){Cp(e,1);const i=n.components;null!==i&&Ep(e,i,1)}}function Ep(e,t,n){for(let r=0;r<t.length;r++)x_(e,t[r],n)}class $a{get rootNodes(){const t=this._lView,n=t[Le];return Ba(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[gn]}set context(t){this._lView[gn]=t}get destroyed(){return 256==(256&this._lView[mt])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[on];if(kn(t)){const n=t[8],r=n?n.indexOf(this):-1;r>-1&&(Al(t,r),ml(n,r))}this._attachedToViewContainer=!1}qu(this._lView[Le],this._lView)}onDestroy(t){!function vi(e,t){if(256==(256&e[mt]))throw new Re(911,!1);null===e[Kr]&&(e[Kr]=[]),e[Kr].push(t)}(this._lView,t)}markForCheck(){ja(this._cdRefInjectingView||this._lView)}detach(){this._lView[mt]&=-129}reattach(){this._lView[mt]|=128}detectChanges(){Zl(this._lView[Le],this._lView,this.context)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new Re(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,function CD(e,t){Na(e,t,t[ut],2,null,null)}(this._lView[Le],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new Re(902,!1);this._appRef=t}}class F_ extends $a{constructor(t){super(t),this._view=t}detectChanges(){const t=this._view;Zl(t[Le],t,t[gn],!1)}checkNoChanges(){}get context(){return null}}class wp extends zl{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const n=pt(t);return new Ha(n,this.ngModule)}}function bp(e){const t=[];for(let n in e)e.hasOwnProperty(n)&&t.push({propName:e[n],templateName:n});return t}class L_{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=dn(r);const i=this.injector.get(t,Ac,r);return i!==Ac||n===Ac?i:this.parentInjector.get(t,n,r)}}class Ha extends Ih{get inputs(){const t=this.componentDef,n=t.inputTransforms,r=bp(t.inputs);if(null!==n)for(const i of r)n.hasOwnProperty(i.propName)&&(i.transform=n[i.propName]);return r}get outputs(){return bp(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=function Po(e){return e.map(no).join(",")}(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,i){let o=(i=i||this.ngModule)instanceof Mo?i:i?.injector;o&&null!==this.componentDef.getStandaloneInjector&&(o=this.componentDef.getStandaloneInjector(o)||o);const s=o?new L_(t,o):t,f=s.get(Ah,null);if(null===f)throw new Re(407,!1);const x={rendererFactory:f,sanitizer:s.get(PC,null),effectManager:s.get(yp,null),afterRenderEventManager:s.get(Fc,null)},H=f.createRenderer(null,this.componentDef),K=this.componentDef.selectors[0][0]||"div",ge=r?function s_(e,t,n,r){const o=r.get(Wh,!1)||n===In.ShadowDom,s=e.selectRootElement(t,o);return function a_(e){op(e)}(s),s}(H,r,this.componentDef.encapsulation,s):Tl(H,K,function k_(e){const t=e.toLowerCase();return"svg"===t?"svg":"math"===t?"math":null}(K)),et=this.componentDef.signals?4608:this.componentDef.onPush?576:528;let _e=null;null!==ge&&(_e=Sc(ge,s,!0));const Tt=Uc(0,null,null,1,0,null,null,null,null,null,null),Rt=Xl(null,Tt,null,et,null,null,x,H,s,null,_e);let En,Ei;cn(Rt);try{const $o=this.componentDef;let ga,Zd=null;$o.findHostDirectiveDefs?(ga=[],Zd=new Map,$o.findHostDirectiveDefs($o,ga,Zd),ga.push($o)):ga=[$o];const HM=function j_(e,t){const n=e[Le],r=wt;return e[r]=t,Js(n,r,2,"#host",null)}(Rt,ge),zM=function U_(e,t,n,r,i,o,s){const f=i[Le];!function B_(e,t,n,r){for(const i of e)t.mergedAttrs=Qn(t.mergedAttrs,i.hostAttrs);null!==t.mergedAttrs&&(ql(t,t.mergedAttrs,!0),null!==n&&nh(r,n,t))}(r,e,t,s);let p=null;null!==t&&(p=Sc(t,i[Zn]));const E=o.rendererFactory.createRenderer(t,n);let A=16;n.signals?A=4096:n.onPush&&(A=64);const x=Xl(i,ip(n),null,A,i[e.index],e,o,E,null,null,p);return f.firstCreatePass&&$c(f,e,r.length-1),Ql(i,x),i[e.index]=x}(HM,ge,$o,ga,Rt,x,H);Ei=oi(Tt,wt),ge&&function H_(e,t,n,r){if(r)zr(e,n,["ng-version",RC.full]);else{const{attrs:i,classes:o}=function Ho(e){const t=[],n=[];let r=1,i=2;for(;r<e.length;){let o=e[r];if("string"==typeof o)2===i?""!==o&&t.push(o,e[++r]):8===i&&n.push(o);else{if(!yr(i))break;i=o}r++}return{attrs:t,classes:n}}(t.selectors[0]);i&&zr(e,n,i),o&&o.length>0&&th(e,n,o.join(" "))}}(H,$o,ge,r),void 0!==n&&function z_(e,t,n){const r=e.projection=[];for(let i=0;i<t.length;i++){const o=n[i];r.push(null!=o?Array.from(o):null)}}(Ei,this.ngContentSelectors,n),En=function $_(e,t,n,r,i,o){const s=Cr(),f=i[Le],p=bn(s,i);lp(f,i,s,n,null,r);for(let A=0;A<n.length;A++)Zr(ys(i,f,s.directiveStart+A,s),i);up(f,i,s),p&&Zr(p,i);const E=ys(i,f,s.directiveStart+s.componentOffset,s);if(e[gn]=i[gn]=E,null!==o)for(const A of o)A(E,t);return Lc(f,s,e),E}(zM,$o,ga,Zd,Rt,[G_]),Wc(Tt,Rt,null)}finally{gs()}return new V_(this.componentType,En,Xs(Ei,Rt),Rt,Ei)}}class V_ extends MC{constructor(t,n,r,i,o){super(),this.location=r,this._rootLView=i,this._tNode=o,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new F_(i),this.componentType=t}setInput(t,n){const r=this._tNode.inputs;let i;if(null!==r&&(i=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;const o=this._rootLView;Gc(o[Le],o,i,t,n),this.previousInputValues.set(t,n),ja(cr(this._tNode.index,o))}}get injector(){return new ai(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}function G_(){const e=Cr();ul(Ee()[Le],e)}function Kc(e){let t=function Mp(e){return Object.getPrototypeOf(e.prototype).constructor}(e.type),n=!0;const r=[e];for(;t;){let i;if(lr(e))i=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new Re(903,!1);i=t.\u0275dir}if(i){if(n){r.push(i);const s=e;s.inputs=Jl(e.inputs),s.inputTransforms=Jl(e.inputTransforms),s.declaredInputs=Jl(e.declaredInputs),s.outputs=Jl(e.outputs);const f=i.hostBindings;f&&Q_(e,f);const p=i.viewQuery,E=i.contentQueries;if(p&&K_(e,p),E&&X_(e,E),Ae(e.inputs,i.inputs),Ae(e.declaredInputs,i.declaredInputs),Ae(e.outputs,i.outputs),null!==i.inputTransforms&&(null===s.inputTransforms&&(s.inputTransforms={}),Ae(s.inputTransforms,i.inputTransforms)),lr(i)&&i.data.animation){const A=e.data;A.animation=(A.animation||[]).concat(i.data.animation)}}const o=i.features;if(o)for(let s=0;s<o.length;s++){const f=o[s];f&&f.ngInherit&&f(e),f===Kc&&(n=!1)}}t=Object.getPrototypeOf(t)}!function W_(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){const i=e[r];i.hostVars=t+=i.hostVars,i.hostAttrs=Qn(i.hostAttrs,n=Qn(n,i.hostAttrs))}}(r)}function Jl(e){return e===Kn?{}:e===gt?[]:e}function K_(e,t){const n=e.viewQuery;e.viewQuery=n?(r,i)=>{t(r,i),n(r,i)}:t}function X_(e,t){const n=e.contentQueries;e.contentQueries=n?(r,i,o)=>{t(r,i,o),n(r,i,o)}:t}function Q_(e,t){const n=e.hostBindings;e.hostBindings=n?(r,i)=>{t(r,i),n(r,i)}:t}function Ap(e){const t=e.inputConfig,n={};for(const r in t)if(t.hasOwnProperty(r)){const i=t[r];Array.isArray(i)&&i[2]&&(n[r]=i[2])}e.inputTransforms=n}function Yl(e){return!!Xc(e)&&(Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e)}function Xc(e){return null!==e&&("function"==typeof e||"object"==typeof e)}function To(e,t,n){return e[t]=n}function Jr(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function Cs(e,t,n,r){const i=Jr(e,t,n);return Jr(e,t+1,r)||i}function Qc(e,t,n,r){const i=Ee();return Jr(i,ps(),t)&&(Kt(),Io($n(),i,e,t,n,r)),Qc}function ea(e,t,n,r){return Jr(e,ps(),n)?t+je(n)+r:Ot}function ta(e,t,n,r,i,o){const f=Cs(e,function wo(){return ot.lFrame.bindingIndex}(),n,i);return d(2),f?t+je(n)+r+je(i)+o:Ot}function $p(e,t,n,r,i,o,s,f){const p=Ee(),E=Kt(),A=e+wt,x=E.firstCreatePass?function CE(e,t,n,r,i,o,s,f,p){const E=t.consts,A=Js(t,e,4,s||null,Qe(E,f));Bc(t,n,A,Qe(E,p)),ul(t,A);const x=A.tView=Uc(2,A,r,i,o,t.directiveRegistry,t.pipeRegistry,null,t.schemas,E,null);return null!==t.queries&&(t.queries.template(t,A),x.queries=t.queries.embeddedTView(A)),A}(A,E,p,t,n,r,i,o,s):E.data[A];Xi(x,!1);const H=Hp(E,p,x,e);ll()&&Nl(E,p,H,x),Zr(H,p),Ql(p,p[A]=dp(H,p,H,x)),Wt(x)&&Vc(E,p,x),null!=s&&jc(p,x,f)}let Hp=function zp(e,t,n,r){return es(!0),t[ut].createComment("")};function td(e,t,n){const r=Ee();return Jr(r,ps(),t)&&function Ri(e,t,n,r,i,o,s,f){const p=bn(t,n);let A,E=t.inputs;!f&&null!=E&&(A=E[r])?(Gc(e,n,A,r,i),Ir(t)&&function p_(e,t){const n=cr(t,e);16&n[mt]||(n[mt]|=64)}(n,t.index)):3&t.type&&(r=function h_(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r),i=null!=s?s(i,t.value||"",r):i,o.setProperty(p,r,i))}(Kt(),$n(),r,e,t,r[ut],n,!1),td}function nd(e,t,n,r,i){const s=i?"class":"style";Gc(e,n,t.inputs[s],s,r)}function iu(e,t,n,r){const i=Ee(),o=Kt(),s=wt+e,f=i[ut],p=o.firstCreatePass?function ME(e,t,n,r,i,o){const s=t.consts,p=Js(t,e,2,r,Qe(s,i));return Bc(t,n,p,Qe(s,o)),null!==p.attrs&&ql(p,p.attrs,!1),null!==p.mergedAttrs&&ql(p,p.mergedAttrs,!0),null!==t.queries&&t.queries.elementStart(t,p),p}(s,o,i,t,n,r):o.data[s],E=Gp(o,i,p,f,t,e);i[s]=E;const A=Wt(p);return Xi(p,!0),nh(f,E,p),32!=(32&p.flags)&&ll()&&Nl(o,i,E,p),0===function Da(){return ot.lFrame.elementDepthCount}()&&Zr(E,i),function dr(){ot.lFrame.elementDepthCount++}(),A&&(Vc(o,i,p),Lc(o,p,i)),null!==r&&jc(i,p),iu}function ou(){let e=Cr();Ca()?As():(e=e.parent,Xi(e,!1));const t=e;(function Ts(e){return ot.skipHydrationRootTNode===e})(t)&&function ef(){ot.skipHydrationRootTNode=null}(),function Eo(){ot.lFrame.elementDepthCount--}();const n=Kt();return n.firstCreatePass&&(ul(n,e),Gi(e)&&n.queries.elementEnd(e)),null!=t.classesWithoutHost&&function vv(e){return 0!=(8&e.flags)}(t)&&nd(n,t,Ee(),t.classesWithoutHost,!0),null!=t.stylesWithoutHost&&function Dv(e){return 0!=(16&e.flags)}(t)&&nd(n,t,Ee(),t.stylesWithoutHost,!1),ou}function rd(e,t,n,r){return iu(e,t,n,r),ou(),rd}let Gp=(e,t,n,r,i,o)=>(es(!0),Tl(r,i,function tf(){return ot.lFrame.currentNamespace}()));function su(e,t,n){const r=Ee(),i=Kt(),o=e+wt,s=i.firstCreatePass?function TE(e,t,n,r,i){const o=t.consts,s=Qe(o,r),f=Js(t,e,8,"ng-container",s);return null!==s&&ql(f,s,!0),Bc(t,n,f,Qe(o,i)),null!==t.queries&&t.queries.elementStart(t,f),f}(o,i,r,t,n):i.data[o];Xi(s,!0);const f=Kp(i,r,s,e);return r[o]=f,ll()&&Nl(i,r,f,s),Zr(f,r),Wt(s)&&(Vc(i,r,s),Lc(i,s,r)),null!=n&&jc(r,s),su}function au(){let e=Cr();const t=Kt();return Ca()?As():(e=e.parent,Xi(e,!1)),t.firstCreatePass&&(ul(t,e),Gi(e)&&t.queries.elementEnd(e)),au}let Kp=(e,t,n,r)=>(es(!0),Qu(t[ut],""));function Xp(){return Ee()}function id(e){return!!e&&"function"==typeof e.then}function Qp(e){return!!e&&"function"==typeof e.subscribe}function od(e,t,n,r){const i=Ee(),o=Kt(),s=Cr();return function Zp(e,t,n,r,i,o,s){const f=Wt(r),E=e.firstCreatePass&&pp(e),A=t[gn],x=hp(t);let H=!0;if(3&r.type||s){const Se=bn(r,t),$e=s?s(Se):Se,et=x.length,_e=s?Rt=>s(Jt(Rt[r.index])):r.index;let Tt=null;if(!s&&f&&(Tt=function NE(e,t,n,r){const i=e.cleanup;if(null!=i)for(let o=0;o<i.length-1;o+=2){const s=i[o];if(s===n&&i[o+1]===r){const f=t[Mr],p=i[o+2];return f.length>p?f[p]:null}"string"==typeof s&&(o+=2)}return null}(e,t,i,r.index)),null!==Tt)(Tt.__ngLastListenerFn__||Tt).__ngNextListenerFn__=o,Tt.__ngLastListenerFn__=o,H=!1;else{o=Yp(r,t,A,o,!1);const Rt=n.listen($e,i,o);x.push(o,Rt),E&&E.push(i,_e,et,et+1)}}else o=Yp(r,t,A,o,!1);const K=r.outputs;let ge;if(H&&null!==K&&(ge=K[i])){const Se=ge.length;if(Se)for(let $e=0;$e<Se;$e+=2){const En=t[ge[$e]][ge[$e+1]].subscribe(o),Ei=x.length;x.push(o,En),E&&E.push(i,r.index,Ei,-(Ei+1))}}}(o,i,i[ut],s,e,t,r),od}function Jp(e,t,n,r){try{return hn(6,t,n),!1!==n(r)}catch(i){return mp(e,i),!1}finally{hn(7,t,n)}}function Yp(e,t,n,r,i){return function o(s){if(s===Function)return r;ja(e.componentOffset>-1?cr(e.index,t):t);let p=Jp(t,n,r,s),E=o.__ngNextListenerFn__;for(;E;)p=Jp(t,n,E,s)&&p,E=E.__ngNextListenerFn__;return i&&!1===p&&s.preventDefault(),p}}function eg(e=1){return function Yo(e){return(ot.lFrame.contextLView=function Au(e,t){for(;e>0;)t=t[Wr],e--;return t}(e,ot.lFrame.contextLView))[gn]}(e)}function lu(e,t){return e<<17|t<<2}function rs(e){return e>>17&32767}function ad(e){return 2|e}function _s(e){return(131068&e)>>2}function ld(e,t){return-131069&e|t<<2}function ud(e){return 1|e}function cg(e,t,n,r,i){const o=e[n+1],s=null===t;let f=r?rs(o):_s(o),p=!1;for(;0!==f&&(!1===p||s);){const A=e[f+1];UE(e[f],t)&&(p=!0,e[f+1]=r?ud(A):ad(A)),f=r?rs(A):_s(A)}p&&(e[n+1]=r?ad(o):ud(o))}function UE(e,t){return null===e||null==t||(Array.isArray(e)?e[1]:e)===t||!(!Array.isArray(e)||"string"!=typeof t)&&Ls(e,t)>=0}const Er={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function dg(e){return e.substring(Er.key,Er.keyEnd)}function fg(e,t){const n=Er.textEnd;return n===t?-1:(t=Er.keyEnd=function zE(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}(e,Er.key=t,n),la(e,t,n))}function la(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function cd(e,t,n){return fo(e,t,n,!1),cd}function dd(e,t){return fo(e,t,null,!0),dd}function yg(e){ho(YE,Oo,e,!0)}function Oo(e,t){for(let n=function $E(e){return function pg(e){Er.key=0,Er.keyEnd=0,Er.value=0,Er.valueEnd=0,Er.textEnd=e.length}(e),fg(e,la(e,0,Er.textEnd))}(t);n>=0;n=fg(t,n))Pi(e,dg(t),!0)}function fo(e,t,n,r){const i=Ee(),o=Kt(),s=d(2);o.firstUpdatePass&&Dg(o,e,s,r),t!==Ot&&Jr(i,s,t)&&_g(o,o.data[si()],i,i[ut],e,i[s+1]=function tw(e,t){return null==e||""===e||("string"==typeof t?e+=t:"object"==typeof e&&(e=Ve(ts(e)))),e}(t,n),r,s)}function ho(e,t,n,r){const i=Kt(),o=d(2);i.firstUpdatePass&&Dg(i,null,o,r);const s=Ee();if(n!==Ot&&Jr(s,o,n)){const f=i.data[si()];if(wg(f,r)&&!vg(i,o)){let p=r?f.classesWithoutHost:f.stylesWithoutHost;null!==p&&(n=tt(p,n||"")),nd(i,f,s,n,r)}else!function ew(e,t,n,r,i,o,s,f){i===Ot&&(i=gt);let p=0,E=0,A=0<i.length?i[0]:null,x=0<o.length?o[0]:null;for(;null!==A||null!==x;){const H=p<i.length?i[p+1]:void 0,K=E<o.length?o[E+1]:void 0;let Se,ge=null;A===x?(p+=2,E+=2,H!==K&&(ge=x,Se=K)):null===x||null!==A&&A<x?(p+=2,ge=A):(E+=2,ge=x,Se=K),null!==ge&&_g(e,t,n,r,ge,Se,s,f),A=p<i.length?i[p]:null,x=E<o.length?o[E]:null}}(i,f,s,s[ut],s[o+1],s[o+1]=function JE(e,t,n){if(null==n||""===n)return gt;const r=[],i=ts(n);if(Array.isArray(i))for(let o=0;o<i.length;o++)e(r,i[o],!0);else if("object"==typeof i)for(const o in i)i.hasOwnProperty(o)&&e(r,o,i[o]);else"string"==typeof i&&t(r,i);return r}(e,t,n),r,o)}}function vg(e,t){return t>=e.expandoStartIndex}function Dg(e,t,n,r){const i=e.data;if(null===i[n+1]){const o=i[si()],s=vg(e,n);wg(o,r)&&null===t&&!s&&(t=!1),t=function XE(e,t,n,r){const i=function F(e){const t=ot.lFrame.currentDirectiveIndex;return-1===t?null:e[t]}(e);let o=r?t.residualClasses:t.residualStyles;if(null===i)0===(r?t.classBindings:t.styleBindings)&&(n=Xa(n=fd(null,e,t,n,r),t.attrs,r),o=null);else{const s=t.directiveStylingLast;if(-1===s||e[s]!==i)if(n=fd(i,e,t,n,r),null===o){let p=function QE(e,t,n){const r=n?t.classBindings:t.styleBindings;if(0!==_s(r))return e[rs(r)]}(e,t,r);void 0!==p&&Array.isArray(p)&&(p=fd(null,e,t,p[1],r),p=Xa(p,t.attrs,r),function qE(e,t,n,r){e[rs(n?t.classBindings:t.styleBindings)]=r}(e,t,r,p))}else o=function ZE(e,t,n){let r;const i=t.directiveEnd;for(let o=1+t.directiveStylingLast;o<i;o++)r=Xa(r,e[o].hostAttrs,n);return Xa(r,t.attrs,n)}(e,t,r)}return void 0!==o&&(r?t.residualClasses=o:t.residualStyles=o),n}(i,o,t,r),function VE(e,t,n,r,i,o){let s=o?t.classBindings:t.styleBindings,f=rs(s),p=_s(s);e[r]=n;let A,E=!1;if(Array.isArray(n)?(A=n[1],(null===A||Ls(n,A)>0)&&(E=!0)):A=n,i)if(0!==p){const H=rs(e[f+1]);e[r+1]=lu(H,f),0!==H&&(e[H+1]=ld(e[H+1],r)),e[f+1]=function kE(e,t){return 131071&e|t<<17}(e[f+1],r)}else e[r+1]=lu(f,0),0!==f&&(e[f+1]=ld(e[f+1],r)),f=r;else e[r+1]=lu(p,0),0===f?f=r:e[p+1]=ld(e[p+1],r),p=r;E&&(e[r+1]=ad(e[r+1])),cg(e,A,r,!0),cg(e,A,r,!1),function jE(e,t,n,r,i){const o=i?e.residualClasses:e.residualStyles;null!=o&&"string"==typeof t&&Ls(o,t)>=0&&(n[r+1]=ud(n[r+1]))}(t,A,e,r,o),s=lu(f,p),o?t.classBindings=s:t.styleBindings=s}(i,o,t,n,s,r)}}function fd(e,t,n,r,i){let o=null;const s=n.directiveEnd;let f=n.directiveStylingLast;for(-1===f?f=n.directiveStart:f++;f<s&&(o=t[f],r=Xa(r,o.hostAttrs,i),o!==e);)f++;return null!==e&&(n.directiveStylingLast=f),r}function Xa(e,t,n){const r=n?1:2;let i=-1;if(null!==t)for(let o=0;o<t.length;o++){const s=t[o];"number"==typeof s?i=s:i===r&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),Pi(e,s,!!n||t[++o]))}return void 0===e?null:e}function YE(e,t,n){const r=String(t);""!==r&&!r.includes(" ")&&Pi(e,r,n)}function _g(e,t,n,r,i,o,s,f){if(!(3&t.type))return;const p=e.data,E=p[f+1],A=function LE(e){return 1==(1&e)}(E)?Eg(p,t,n,i,_s(E),s):void 0;uu(A)||(uu(o)||function FE(e){return 2==(2&e)}(E)&&(o=Eg(p,null,n,i,f,s)),function OD(e,t,n,r,i){if(t)i?e.addClass(n,r):e.removeClass(n,r);else{let o=-1===r.indexOf("-")?void 0:Sl.DashCase;null==i?e.removeStyle(n,r,o):("string"==typeof i&&i.endsWith("!important")&&(i=i.slice(0,-10),o|=Sl.Important),e.setStyle(n,r,i,o))}}(r,s,Rn(si(),n),i,o))}function Eg(e,t,n,r,i,o){const s=null===t;let f;for(;i>0;){const p=e[i],E=Array.isArray(p),A=E?p[1]:p,x=null===A;let H=n[i+1];H===Ot&&(H=x?gt:void 0);let K=x?ju(H,r):A===r?H:void 0;if(E&&!uu(K)&&(K=ju(p,r)),uu(K)&&(f=K,s))return f;const ge=e[i+1];i=s?rs(ge):_s(ge)}if(null!==t){let p=o?t.residualClasses:t.residualStyles;null!=p&&(f=ju(p,r))}return f}function uu(e){return void 0!==e}function wg(e,t){return 0!=(e.flags&(t?8:16))}function bg(e,t=""){const n=Ee(),r=Kt(),i=e+wt,o=r.firstCreatePass?Js(r,i,1,t,null):r.data[i],s=Mg(r,n,o,t,e);n[i]=s,ll()&&Nl(r,n,s,o),Xi(o,!1)}let Mg=(e,t,n,r,i)=>(es(!0),function Il(e,t){return e.createText(t)}(t[ut],r));function hd(e){return cu("",e,""),hd}function cu(e,t,n){const r=Ee(),i=ea(r,e,t,n);return i!==Ot&&Bo(r,si(),i),cu}function pd(e,t,n,r,i){const o=Ee(),s=ta(o,e,t,n,r,i);return s!==Ot&&Bo(o,si(),s),pd}function Rg(e,t,n){ho(Pi,Oo,ea(Ee(),e,t,n),!0)}function xg(e,t,n,r,i){ho(Pi,Oo,ta(Ee(),e,t,n,r,i),!0)}const Es=void 0;var _w=["en",[["a","p"],["AM","PM"],Es],[["AM","PM"],Es,Es],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Es,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Es,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Es,"{1} 'at' {0}",Es],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function Cw(e){const n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return 1===n&&0===r?1:5}];let ua={};function gd(e){const t=function Ew(e){return e.toLowerCase().replace(/_/g,"-")}(e);let n=Kg(t);if(n)return n;const r=t.split("-")[0];if(n=Kg(r),n)return n;if("en"===r)return _w;throw new Re(701,!1)}function Wg(e){return gd(e)[ca.PluralCase]}function Kg(e){return e in ua||(ua[e]=At.ng&&At.ng.common&&At.ng.common.locales&&At.ng.common.locales[e]),ua[e]}var ca=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ca||{});const da="en-US";let Xg=da;function vd(e,t,n,r,i){if(e=be(e),Array.isArray(e))for(let o=0;o<e.length;o++)vd(e[o],t,n,r,i);else{const o=Kt(),s=Ee(),f=Cr();let p=Ds(e)?e:be(e.provide);const E=_h(e),A=1048575&f.providerIndexes,x=f.directiveStart,H=f.providerIndexes>>20;if(Ds(e)||!e.multi){const K=new _a(E,i,Zs),ge=Cd(p,t,i?A:A+H,x);-1===ge?(Fu(hl(f,s),o,p),Dd(o,e,t.length),t.push(p),f.directiveStart++,f.directiveEnd++,i&&(f.providerIndexes+=1048576),n.push(K),s.push(K)):(n[ge]=K,s[ge]=K)}else{const K=Cd(p,t,A+H,x),ge=Cd(p,t,A,A+H),$e=ge>=0&&n[ge];if(i&&!$e||!i&&!(K>=0&&n[K])){Fu(hl(f,s),o,p);const et=function _0(e,t,n,r,i){const o=new _a(e,n,Zs);return o.multi=[],o.index=t,o.componentProviders=0,Dm(o,i,r&&!n),o}(i?C0:D0,n.length,i,r,E);!i&&$e&&(n[ge].providerFactory=et),Dd(o,e,t.length,0),t.push(p),f.directiveStart++,f.directiveEnd++,i&&(f.providerIndexes+=1048576),n.push(et),s.push(et)}else Dd(o,e,K>-1?K:ge,Dm(n[i?ge:K],E,!i&&r));!i&&r&&$e&&n[ge].componentProviders++}}}function Dd(e,t,n,r){const i=Ds(t),o=function sC(e){return!!e.useClass}(t);if(i||o){const p=(o?be(t.useClass):t).prototype.ngOnDestroy;if(p){const E=e.destroyHooks||(e.destroyHooks=[]);if(!i&&t.multi){const A=E.indexOf(n);-1===A?E.push(n,[r,p]):E[A+1].push(r,p)}else E.push(n,p)}}}function Dm(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Cd(e,t,n,r){for(let i=n;i<r;i++)if(t[i]===e)return i;return-1}function D0(e,t,n,r){return _d(this.multi,[])}function C0(e,t,n,r){const i=this.multi;let o;if(this.providerFactory){const s=this.providerFactory.componentProviders,f=ys(n,n[Le],this.providerFactory.index,r);o=f.slice(0,s),_d(i,o);for(let p=s;p<f.length;p++)o.push(f[p])}else o=[],_d(i,o);return o}function _d(e,t){for(let n=0;n<e.length;n++)t.push((0,e[n])());return t}function Cm(e,t=[]){return n=>{n.providersResolver=(r,i)=>function v0(e,t,n){const r=Kt();if(r.firstCreatePass){const i=lr(e);vd(n,r.data,r.blueprint,i,!0),vd(t,r.data,r.blueprint,i,!1)}}(r,i?i(e):e,t)}}class ws{}class _m{}function E0(e,t){return new Ed(e,t??null,[])}class Ed extends ws{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new wp(this);const i=Pn(t);this._bootstrapComponents=Uo(i.bootstrap),this._r3Injector=Fh(t,n,[{provide:ws,useValue:this},{provide:zl,useValue:this.componentFactoryResolver},...r],Ve(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class wd extends _m{constructor(t){super(),this.moduleType=t}create(t){return new Ed(this.moduleType,t,[])}}class Em extends ws{constructor(t){super(),this.componentFactoryResolver=new wp(this),this.instance=null;const n=new zs([...t.providers,{provide:ws,useValue:this},{provide:zl,useValue:this.componentFactoryResolver}],t.parent||Vl(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}}function wm(e,t,n=null){return new Em({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}let b0=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){const r=yh(0,n.type),i=r.length>0?wm([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(const n of this.cachedInjectors.values())null!==n&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=Nt({token:e,providedIn:"environment",factory:()=>new e(Ne(Mo))})}}return e})();function bm(e){e.getStandaloneInjector=t=>t.get(b0).getOrCreateStandaloneInjector(e)}function Nm(e,t,n){const r=qr()+e,i=Ee();return i[r]===Ot?To(i,r,n?t.call(n):t()):function za(e,t){return e[t]}(i,r)}function Pm(e,t,n,r){return km(Ee(),qr(),e,t,n,r)}function Rm(e,t,n,r,i){return Lm(Ee(),qr(),e,t,n,r,i)}function xm(e,t,n,r,i,o){return Vm(Ee(),qr(),e,t,n,r,i,o)}function Fm(e,t,n,r,i,o,s){return function jm(e,t,n,r,i,o,s,f,p){const E=t+n;return function Zi(e,t,n,r,i,o){const s=Cs(e,t,n,r);return Cs(e,t+2,i,o)||s}(e,E,i,o,s,f)?To(e,E+4,p?r.call(p,i,o,s,f):r(i,o,s,f)):el(e,E+4)}(Ee(),qr(),e,t,n,r,i,o,s)}function el(e,t){const n=e[t];return n===Ot?void 0:n}function km(e,t,n,r,i,o){const s=t+n;return Jr(e,s,i)?To(e,s+1,o?r.call(o,i):r(i)):el(e,s+1)}function Lm(e,t,n,r,i,o,s){const f=t+n;return Cs(e,f,i,o)?To(e,f+2,s?r.call(s,i,o):r(i,o)):el(e,f+2)}function Vm(e,t,n,r,i,o,s,f){const p=t+n;return function eu(e,t,n,r,i){const o=Cs(e,t,n,r);return Jr(e,t+2,i)||o}(e,p,i,o,s)?To(e,p+3,f?r.call(f,i,o,s):r(i,o,s)):el(e,p+3)}function Bm(e,t){const n=Kt();let r;const i=e+wt;n.firstCreatePass?(r=function V0(e,t){if(t)for(let n=t.length-1;n>=0;n--){const r=t[n];if(e===r.name)return r}}(t,n.pipeRegistry),n.data[i]=r,r.onDestroy&&(n.destroyHooks??=[]).push(i,r.onDestroy)):r=n.data[i];const o=r.factory||(r.factory=Wi(r.type)),f=Sn(Zs);try{const p=fl(!1),E=o();return fl(p),function wE(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}(n,Ee(),i,E),E}finally{Sn(f)}}function $m(e,t,n){const r=e+wt,i=Ee(),o=_o(i,r);return tl(i,r)?km(i,qr(),t,o.transform,n,o):o.transform(n)}function Hm(e,t,n,r){const i=e+wt,o=Ee(),s=_o(o,i);return tl(o,i)?Lm(o,qr(),t,s.transform,n,r,s):s.transform(n,r)}function zm(e,t,n,r,i){const o=e+wt,s=Ee(),f=_o(s,o);return tl(s,o)?Vm(s,qr(),t,f.transform,n,r,i,f):f.transform(n,r,i)}function tl(e,t){return e[Le].data[t].pure}function B0(){return this._results[Symbol.iterator]()}class Md{get changes(){return this._changes||(this._changes=new So)}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._results=[],this._changesDetected=!1,this._changes=null,this.length=0,this.first=void 0,this.last=void 0;const n=Md.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=B0)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){const r=this;r.dirty=!1;const i=function qi(e){return e.flat(Number.POSITIVE_INFINITY)}(t);(this._changesDetected=!function Rv(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let i=e[r],o=t[r];if(n&&(i=n(i),o=n(o)),o!==i)return!1}return!0}(r._results,i,n))&&(r._results=i,r.length=i.length,r.last=i[this.length-1],r.first=i[0])}notifyOnChanges(){this._changes&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}setDirty(){this.dirty=!0}destroy(){this.changes.complete(),this.changes.unsubscribe()}}function H0(e,t,n,r=!0){const i=t[Le];if(function ED(e,t,n,r){const i=Un+r,o=n.length;r>0&&(n[i-1][qn]=t),r<o-Un?(t[qn]=n[i],Df(n,Un+r,t)):(n.push(t),t[qn]=null),t[on]=n;const s=t[xn];null!==s&&n!==s&&function wD(e,t){const n=e[Dr];t[fn]!==t[on][on][fn]&&(e[vr]=!0),null===n?e[Dr]=[t]:n.push(t)}(s,t);const f=t[Fn];null!==f&&f.insertView(e),t[mt]|=128}(i,t,e,n),r){const o=ec(n,e),s=t[ut],f=Ol(s,e[Sr]);null!==f&&function DD(e,t,n,r,i,o){r[an]=i,r[Tn]=t,Na(e,r,n,1,i,o)}(i,e[Tn],s,t,f,o)}}let nl=(()=>{class e{static{this.__NG_ELEMENT_ID__=W0}}return e})();const z0=nl,G0=class extends z0{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){const i=function $0(e,t,n,r){const i=t.tView,f=Xl(e,i,n,4096&e[mt]?4096:16,null,t,null,null,null,r?.injector??null,r?.hydrationInfo??null);f[xn]=e[t.index];const E=e[Fn];return null!==E&&(f[Fn]=E.createEmbeddedView(i)),Wc(i,f,n),f}(this._declarationLView,this._declarationTContainer,t,{injector:n,hydrationInfo:r});return new $a(i)}};function W0(){return gu(Cr(),Ee())}function gu(e,t){return 4&e.type?new G0(t,e,Xs(e,t)):null}let yu=(()=>{class e{static{this.__NG_ELEMENT_ID__=J0}}return e})();function J0(){return Zm(Cr(),Ee())}const Y0=yu,Qm=class extends Y0{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Xs(this._hostTNode,this._hostLView)}get injector(){return new ai(this._hostTNode,this._hostLView)}get parentInjector(){const t=pl(this._hostTNode,this._hostLView);if(Pu(t)){const n=wa(t,this._hostLView),r=Ea(t);return new ai(n[Le].data[r+8],n)}return new ai(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const n=qm(this._lContainer);return null!==n&&n[t]||null}get length(){return this._lContainer.length-Un}createEmbeddedView(t,n,r){let i,o;"number"==typeof r?i=r:null!=r&&(i=r.index,o=r.injector);const f=t.createEmbeddedViewImpl(n||{},o,null);return this.insertImpl(f,i,false),f}createComponent(t,n,r,i,o){const s=t&&!function Ma(e){return"function"==typeof e}(t);let f;if(s)f=n;else{const Se=n||{};f=Se.index,r=Se.injector,i=Se.projectableNodes,o=Se.environmentInjector||Se.ngModuleRef}const p=s?t:new Ha(pt(t)),E=r||this.parentInjector;if(!o&&null==p.ngModule){const $e=(s?E:this.parentInjector).get(Mo,null);$e&&(o=$e)}pt(p.componentType??{});const K=p.create(E,i,null,o);return this.insertImpl(K.hostView,f,false),K}insert(t,n){return this.insertImpl(t,n,!1)}insertImpl(t,n,r){const i=t._lView;if(function ds(e){return kn(e[on])}(i)){const p=this.indexOf(t);if(-1!==p)this.detach(p);else{const E=i[on],A=new Qm(E,E[Tn],E[on]);A.detach(A.indexOf(t))}}const s=this._adjustIndex(n),f=this._lContainer;return H0(f,i,s,!r),t.attachToViewContainerRef(),Df(Sd(f),s,t),t}move(t,n){return this.insert(t,n)}indexOf(t){const n=qm(this._lContainer);return null!==n?n.indexOf(t):-1}remove(t){const n=this._adjustIndex(t,-1),r=Al(this._lContainer,n);r&&(ml(Sd(this._lContainer),n),qu(r[Le],r))}detach(t){const n=this._adjustIndex(t,-1),r=Al(this._lContainer,n);return r&&null!=ml(Sd(this._lContainer),n)?new $a(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function qm(e){return e[8]}function Sd(e){return e[8]||(e[8]=[])}function Zm(e,t){let n;const r=t[e.index];return kn(r)?n=r:(n=dp(r,t,null,e),t[e.index]=n,Ql(t,n)),Jm(n,t,e,r),new Qm(n,e,t)}let Jm=function Ym(e,t,n,r){if(e[Sr])return;let i;i=8&n.type?Jt(r):function eb(e,t){const n=e[ut],r=n.createComment(""),i=bn(t,e);return vs(n,Ol(n,i),r,function ID(e,t){return e.nextSibling(t)}(n,i),!1),r}(t,n),e[Sr]=i};class Id{constructor(t){this.queryList=t,this.matches=null}clone(){return new Id(this.queryList)}setDirty(){this.queryList.setDirty()}}class Td{constructor(t=[]){this.queries=t}createEmbeddedView(t){const n=t.queries;if(null!==n){const r=null!==t.contentQueries?t.contentQueries[0]:n.length,i=[];for(let o=0;o<r;o++){const s=n.getByIndex(o);i.push(this.queries[s.indexInDeclarationView].clone())}return new Td(i)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)null!==ly(t,n).matches&&this.queries[n].setDirty()}}class ey{constructor(t,n,r=null){this.predicate=t,this.flags=n,this.read=r}}class Ad{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){const i=null!==n?n.length:0,o=this.getByIndex(r).embeddedTView(t,i);o&&(o.indexInDeclarationView=r,null!==n?n.push(o):n=[o])}return null!==n?new Ad(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}}class Od{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new Od(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&1!=(1&this.metadata.flags)){const n=this._declarationNodeIndex;let r=t.parent;for(;null!==r&&8&r.type&&r.index!==n;)r=r.parent;return n===(null!==r?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){const r=this.metadata.predicate;if(Array.isArray(r))for(let i=0;i<r.length;i++){const o=r[i];this.matchTNodeWithReadOption(t,n,rb(n,o)),this.matchTNodeWithReadOption(t,n,gl(n,t,o,!1,!1))}else r===nl?4&n.type&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,gl(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(null!==r){const i=this.metadata.read;if(null!==i)if(i===Va||i===yu||i===nl&&4&n.type)this.addMatch(n.index,-2);else{const o=gl(n,t,i,!1,!1);null!==o&&this.addMatch(n.index,o)}else this.addMatch(n.index,r)}}addMatch(t,n){null===this.matches?this.matches=[t,n]:this.matches.push(t,n)}}function rb(e,t){const n=e.localNames;if(null!==n)for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1];return null}function ob(e,t,n,r){return-1===n?function ib(e,t){return 11&e.type?Xs(e,t):4&e.type?gu(e,t):null}(t,e):-2===n?function sb(e,t,n){return n===Va?Xs(t,e):n===nl?gu(t,e):n===yu?Zm(t,e):void 0}(e,t,r):ys(e,e[Le],n,t)}function ty(e,t,n,r){const i=t[Fn].queries[r];if(null===i.matches){const o=e.data,s=n.matches,f=[];for(let p=0;p<s.length;p+=2){const E=s[p];f.push(E<0?null:ob(t,o[E],s[p+1],n.metadata.read))}i.matches=f}return i.matches}function Nd(e,t,n,r){const i=e.queries.getByIndex(n),o=i.matches;if(null!==o){const s=ty(e,t,i,n);for(let f=0;f<o.length;f+=2){const p=o[f];if(p>0)r.push(s[f/2]);else{const E=o[f+1],A=t[-p];for(let x=Un;x<A.length;x++){const H=A[x];H[xn]===H[on]&&Nd(H[Le],H,E,r)}if(null!==A[Dr]){const x=A[Dr];for(let H=0;H<x.length;H++){const K=x[H];Nd(K[Le],K,E,r)}}}}}return r}function ny(e){const t=Ee(),n=Kt(),r=re();ee(r+1);const i=ly(n,r);if(e.dirty&&function uo(e){return 4==(4&e[mt])}(t)===(2==(2&i.metadata.flags))){if(null===i.matches)e.reset([]);else{const o=i.crossesNgTemplate?Nd(n,t,r,[]):ty(n,t,i,r);e.reset(o,AC),e.notifyOnChanges()}return!0}return!1}function ry(e,t,n){const r=Kt();r.firstCreatePass&&(ay(r,new ey(e,t,n),-1),2==(2&t)&&(r.staticViewQueries=!0)),sy(r,Ee(),t)}function iy(e,t,n,r){const i=Kt();if(i.firstCreatePass){const o=Cr();ay(i,new ey(t,n,r),o.index),function lb(e,t){const n=e.contentQueries||(e.contentQueries=[]);t!==(n.length?n[n.length-1]:-1)&&n.push(e.queries.length-1,t)}(i,e),2==(2&n)&&(i.staticContentQueries=!0)}sy(i,Ee(),n)}function oy(){return function ab(e,t){return e[Fn].queries[t].queryList}(Ee(),re())}function sy(e,t,n){const r=new Md(4==(4&n));(function c_(e,t,n,r){const i=hp(t);i.push(n),e.firstCreatePass&&pp(e).push(r,i.length-1)})(e,t,r,r.destroy),null===t[Fn]&&(t[Fn]=new Td),t[Fn].queries.push(new Id(r))}function ay(e,t,n){null===e.queries&&(e.queries=new Ad),e.queries.track(new Od(t,n))}function ly(e,t){return e.queries.getByIndex(t)}function Pd(e){return!!Pn(e)}const My=new vt("Application Initializer");let Ld=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=ke(My,{optional:!0})??[]}runInitializers(){if(this.initialized)return;const n=[];for(const i of this.appInits){const o=i();if(id(o))n.push(o);else if(Qp(o)){const s=new Promise((f,p)=>{o.subscribe({complete:f,error:p})});n.push(s)}}const r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(i=>{this.reject(i)}),0===n.length&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Nt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Sy=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Nt({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();const Du=new vt("LocaleId",{providedIn:"root",factory:()=>ke(Du,ze.Optional|ze.SkipSelf)||function Ob(){return typeof $localize<"u"&&$localize.locale||da}()}),Nb=new vt("DefaultCurrencyCode",{providedIn:"root",factory:()=>"USD"});let Iy=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new pe.t(!1)}add(){this.hasPendingTasks.next(!0);const n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),0===this.pendingTasks.size&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks.next(!1)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Nt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();class Rb{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}}let xb=(()=>{class e{compileModuleSync(n){return new wd(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){const r=this.compileModuleSync(n),o=Uo(Pn(n).declarations).reduce((s,f)=>{const p=pt(f);return p&&s.push(new Ha(p)),s},[]);return new Rb(r,o)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Nt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const Ny=new vt(""),Py=new vt("");let Ud,nM=(()=>{class e{constructor(n,r,i){this._ngZone=n,this.registry=r,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,Ud||(function rM(e){Ud=e}(i),i.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{Ar.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;0!==this._callbacks.length;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb(this._didWork)}this._didWork=!1});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>!r.updateCb||!r.updateCb(n)||(clearTimeout(r.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,i){let o=-1;r&&r>0&&(o=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==o),n(this._didWork,this.getPendingTasks())},r)),this._callbacks.push({doneCb:n,timeoutId:o,updateCb:i})}whenStable(n,r,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,i),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,i){return[]}static{this.\u0275fac=function(r){return new(r||e)(Ne(Ar),Ne(Ry),Ne(Py))}}static{this.\u0275prov=Nt({token:e,factory:e.\u0275fac})}}return e})(),Ry=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Ud?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Nt({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),is=null;const xy=new vt("AllowMultipleToken"),Bd=new vt("PlatformDestroyListeners"),$d=new vt("appBootstrapListener");class sM{constructor(t,n){this.name=t,this.token=n}}function Ly(e,t,n=[]){const r=`Platform: ${t}`,i=new vt(r);return(o=[])=>{let s=Hd();if(!s||s.injector.get(xy,!1)){const f=[...n,...o,{provide:i,useValue:!0}];e?e(f):function aM(e){if(is&&!is.get(xy,!1))throw new Re(400,!1);(function Fy(){!function ve(e){k=e}(()=>{throw new Re(600,!1)})})(),is=e;const t=e.get(jy);(function ky(e){e.get(wh,null)?.forEach(n=>n())})(e)}(function Vy(e=[],t){return co.create({name:t,providers:[{provide:pc,useValue:"platform"},{provide:Bd,useValue:new Set([()=>is=null])},...e]})}(f,r))}return function uM(e){const t=Hd();if(!t)throw new Re(401,!1);return t}()}}function Hd(){return is?.get(jy)??null}let jy=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){const i=function cM(e="zone.js",t){return"noop"===e?new WC:"zone.js"===e?new Ar(t):e}(r?.ngZone,function Uy(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return i.run(()=>{const o=function w0(e,t,n){return new Ed(e,t,n)}(n.moduleType,this.injector,function Gy(e){return[{provide:Ar,useFactory:e},{provide:Fa,multi:!0,useFactory:()=>{const t=ke(fM,{optional:!0});return()=>t.initialize()}},{provide:zy,useFactory:dM},{provide:Uh,useFactory:Bh}]}(()=>i)),s=o.injector.get(ns,null);return i.runOutsideAngular(()=>{const f=i.onError.subscribe({next:p=>{s.handleError(p)}});o.onDestroy(()=>{_u(this._modules,o),f.unsubscribe()})}),function By(e,t,n){try{const r=n();return id(r)?r.catch(i=>{throw t.runOutsideAngular(()=>e.handleError(i)),i}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}(s,i,()=>{const f=o.injector.get(Ld);return f.runInitializers(),f.donePromise.then(()=>(function Qg(e){Gt(e,"Expected localeId to be defined"),"string"==typeof e&&(Xg=e.toLowerCase().replace(/_/g,"-"))}(o.injector.get(Du,da)||da),this._moduleDoBootstrap(o),o))})})}bootstrapModule(n,r=[]){const i=$y({},r);return function iM(e,t,n){const r=new wd(n);return Promise.resolve(r)}(0,0,n).then(o=>this.bootstrapModuleFactory(o,i))}_moduleDoBootstrap(n){const r=n.injector.get(pa);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(i=>r.bootstrap(i));else{if(!n.instance.ngDoBootstrap)throw new Re(-403,!1);n.instance.ngDoBootstrap(r)}this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new Re(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());const n=this._injector.get(Bd,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(Ne(co))}}static{this.\u0275prov=Nt({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function $y(e,t){return Array.isArray(t)?t.reduce($y,e):{...e,...t}}let pa=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=ke(zy),this.zoneIsStable=ke(Uh),this.componentTypes=[],this.components=[],this.isStable=ke(Iy).hasPendingTasks.pipe((0,kt.n)(n=>n?(0,z.of)(!1):this.zoneIsStable),function Vt(e,t=Mt.D){return e=e??Qt,(0,De.N)((n,r)=>{let i,o=!0;n.subscribe((0,Lt._)(r,s=>{const f=t(s);(o||!e(i,f))&&(o=!1,i=f,r.next(s))}))})}(),we()),this._injector=ke(Mo)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){const i=n instanceof Ih;if(!this._injector.get(Ld).done)throw!i&&xr(n),new Re(405,!1);let s;s=i?n:this._injector.get(zl).resolveComponentFactory(n),this.componentTypes.push(s.componentType);const f=function oM(e){return e.isBoundToModule}(s)?void 0:this._injector.get(ws),E=s.create(co.NULL,[],r||s.selector,f),A=E.location.nativeElement,x=E.injector.get(Ny,null);return x?.registerApplication(A),E.onDestroy(()=>{this.detachView(E.hostView),_u(this.components,E),x?.unregisterApplication(A)}),this._loadComponent(E),E}tick(){if(this._runningTick)throw new Re(101,!1);try{this._runningTick=!0;for(let n of this._views)n.detectChanges()}catch(n){this.internalErrorHandler(n)}finally{this._runningTick=!1}}attachView(n){const r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){const r=n;_u(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);const r=this._injector.get($d,[]);r.push(...this._bootstrapListeners),r.forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>_u(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new Re(406,!1);const n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Nt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function _u(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const zy=new vt("",{providedIn:"root",factory:()=>ke(ns).handleError.bind(void 0)});function dM(){const e=ke(Ar),t=ke(ns);return n=>e.runOutsideAngular(()=>t.handleError(n))}let fM=(()=>{class e{constructor(){this.zone=ke(Ar),this.applicationRef=ke(pa)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Nt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();let pM=(()=>{class e{static{this.__NG_ELEMENT_ID__=gM}}return e})();function gM(e){return function mM(e,t,n){if(Ir(e)&&!n){const r=cr(e.index,t);return new $a(r,r)}return 47&e.type?new $a(t[fn],t):null}(Cr(),Ee(),16==(16&e))}class qy{constructor(){}supports(t){return Yl(t)}create(t){return new _M(t)}}const CM=(e,t)=>t;class _M{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||CM}forEachItem(t){let n;for(n=this._itHead;null!==n;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,i=0,o=null;for(;n||r;){const s=!r||n&&n.currentIndex<Jy(r,i,o)?n:r,f=Jy(s,i,o),p=s.currentIndex;if(s===r)i--,r=r._nextRemoved;else if(n=n._next,null==s.previousIndex)i++;else{o||(o=[]);const E=f-i,A=p-i;if(E!=A){for(let H=0;H<E;H++){const K=H<o.length?o[H]:o[H]=0,ge=K+H;A<=ge&&ge<E&&(o[H]=K+1)}o[s.previousIndex]=A-E}}f!==p&&t(s,f,p)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;null!==n;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;null!==n;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;null!==n;n=n._nextIdentityChange)t(n)}diff(t){if(null==t&&(t=[]),!Yl(t))throw new Re(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let i,o,s,n=this._itHead,r=!1;if(Array.isArray(t)){this.length=t.length;for(let f=0;f<this.length;f++)o=t[f],s=this._trackByFn(f,o),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,o,s,f)),Object.is(n.item,o)||this._addIdentityChange(n,o)):(n=this._mismatch(n,o,s,f),r=!0),n=n._next}else i=0,function nE(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{const n=e[Symbol.iterator]();let r;for(;!(r=n.next()).done;)t(r.value)}}(t,f=>{s=this._trackByFn(i,f),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,f,s,i)),Object.is(n.item,f)||this._addIdentityChange(n,f)):(n=this._mismatch(n,f,s,i),r=!0),n=n._next,i++}),this.length=i;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,i){let o;return null===t?o=this._itTail:(o=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,o,i)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(r,i))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,o,i)):t=this._addAfter(new EM(n,r),o,i),t}_verifyReinsertion(t,n,r,i){let o=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==o?t=this._reinsertAfter(o,t._prev,i):t.currentIndex!=i&&(t.currentIndex=i,this._addToMoves(t,i)),t}_truncate(t){for(;null!==t;){const n=t._next;this._addToRemovals(this._unlink(t)),t=n}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const i=t._prevRemoved,o=t._nextRemoved;return null===i?this._removalsHead=o:i._nextRemoved=o,null===o?this._removalsTail=i:o._prevRemoved=i,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){const i=null===n?this._itHead:n._next;return t._next=i,t._prev=n,null===i?this._itTail=t:i._prev=t,null===n?this._itHead=t:n._next=t,null===this._linkedRecords&&(this._linkedRecords=new Zy),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const n=t._prev,r=t._next;return null===n?this._itHead=r:n._next=r,null===r?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new Zy),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class EM{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class wM{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===n||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){const n=t._prevDup,r=t._nextDup;return null===n?this._head=r:n._nextDup=r,null===r?this._tail=n:r._prevDup=n,null===this._head}}class Zy{constructor(){this.map=new Map}put(t){const n=t.trackById;let r=this.map.get(n);r||(r=new wM,this.map.set(n,r)),r.add(t)}get(t,n){const i=this.map.get(t);return i?i.get(t,n):null}remove(t){const n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function Jy(e,t,n){const r=e.previousIndex;if(null===r)return r;let i=0;return n&&r<n.length&&(i=n[r]),r+t+i}class Yy{constructor(){}supports(t){return t instanceof Map||Xc(t)}create(){return new bM}}class bM{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(t){let n;for(n=this._mapHead;null!==n;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;null!==n;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;null!==n;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}diff(t){if(t){if(!(t instanceof Map||Xc(t)))throw new Re(900,!1)}else t=new Map;return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,i)=>{if(n&&n.key===i)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{const o=this._getOrCreateRecordForKey(i,r);n=this._insertBeforeOrAppend(n,o)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;null!==r;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){const r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){const i=this._records.get(t);this._maybeAddToChanges(i,n);const o=i._prev,s=i._next;return o&&(o._next=s),s&&(s._prev=o),i._next=null,i._prev=null,i}const r=new MM(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;null!==t;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;null!=t;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){null===this._additionsHead?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){null===this._changesHead?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}}class MM{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function ev(){return new Xd([new qy])}let Xd=(()=>{class e{static{this.\u0275prov=Nt({token:e,providedIn:"root",factory:ev})}constructor(n){this.factories=n}static create(n,r){if(null!=r){const i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||ev()),deps:[[e,new Dl,new vl]]}}find(n){const r=this.factories.find(i=>i.supports(n));if(null!=r)return r;throw new Re(901,!1)}}return e})();function tv(){return new Qd([new Yy])}let Qd=(()=>{class e{static{this.\u0275prov=Nt({token:e,providedIn:"root",factory:tv})}constructor(n){this.factories=n}static create(n,r){if(r){const i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||tv()),deps:[[e,new Dl,new vl]]}}find(n){const r=this.factories.find(i=>i.supports(n));if(r)return r;throw new Re(901,!1)}}return e})();const TM=Ly(null,"core",[]);let AM=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(Ne(pa))}}static{this.\u0275mod=ti({type:e})}static{this.\u0275inj=ht({})}}return e})();function UM(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}function $M(e){const t=pt(e);if(!t)return null;const n=new Ha(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}},4341:(Je,me,O)=>{O.d(me,{Zm:()=>Mt,me:()=>Ce,ok:()=>kn,JD:()=>pi,j4:()=>pt,YN:()=>lr,zX:()=>Hi,VZ:()=>fn,BC:()=>ht,cb:()=>st,vS:()=>fi,xH:()=>mt,Q0:()=>no,Fm:()=>Gr,X1:()=>mo,wz:()=>Le,k0:()=>be,qT:()=>to,y7:()=>gn});var c=O(540),C=O(177),ue=O(6648),se=O(1985),Q=O(3073),oe=O(8750),ne=O(9326),fe=O(4360),ae=O(6450),pe=O(8496),G=O(6354);let De=(()=>{class b{constructor(v,P){this._renderer=v,this._elementRef=P,this.onChange=B=>{},this.onTouched=()=>{}}setProperty(v,P){this._renderer.setProperty(this._elementRef.nativeElement,v,P)}registerOnTouched(v){this.onTouched=v}registerOnChange(v){this.onChange=v}setDisabledState(v){this.setProperty("disabled",v)}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(c.sFG),c.rXU(c.aKT))}}static{this.\u0275dir=c.FsC({type:b})}}return b})(),we=(()=>{class b extends De{static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,features:[c.Vt3]})}}return b})();const Fe=new c.nKC("NgValueAccessor"),kt={provide:Fe,useExisting:(0,c.Rfq)(()=>Mt),multi:!0};let Mt=(()=>{class b extends we{writeValue(v){this.setProperty("checked",v)}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","type","checkbox","formControlName",""],["input","type","checkbox","formControl",""],["input","type","checkbox","ngModel",""]],hostBindings:function(P,B){1&P&&c.bIt("change",function(Dt){return B.onChange(Dt.target.checked)})("blur",function(){return B.onTouched()})},features:[c.Jv_([kt]),c.Vt3]})}}return b})();const Lt={provide:Fe,useExisting:(0,c.Rfq)(()=>Ce),multi:!0},Qt=new c.nKC("CompositionEventMode");let Ce=(()=>{class b extends De{constructor(v,P,B){super(v,P),this._compositionMode=B,this._composing=!1,null==this._compositionMode&&(this._compositionMode=!function Vt(){const b=(0,C.QT)()?(0,C.QT)().getUserAgent():"";return/android (\d+)/.test(b.toLowerCase())}())}writeValue(v){this.setProperty("value",v??"")}_handleInput(v){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(v)}_compositionStart(){this._composing=!0}_compositionEnd(v){this._composing=!1,this._compositionMode&&this.onChange(v)}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(c.sFG),c.rXU(c.aKT),c.rXU(Qt,8))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(P,B){1&P&&c.bIt("input",function(Dt){return B._handleInput(Dt.target.value)})("blur",function(){return B.onTouched()})("compositionstart",function(){return B._compositionStart()})("compositionend",function(Dt){return B._compositionEnd(Dt.target.value)})},features:[c.Jv_([Lt]),c.Vt3]})}}return b})();function Ae(b){return null==b||("string"==typeof b||Array.isArray(b))&&0===b.length}function Ve(b){return null!=b&&"number"==typeof b.length}const tt=new c.nKC("NgValidators"),Pe=new c.nKC("NgAsyncValidators"),Oe=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;class be{static min(S){return nt(S)}static max(S){return jt(S)}static required(S){return function Mn(b){return Ae(b.value)?{required:!0}:null}(S)}static requiredTrue(S){return function Hn(b){return!0===b.value?null:{required:!0}}(S)}static email(S){return function Re(b){return Ae(b.value)||Oe.test(b.value)?null:{email:!0}}(S)}static minLength(S){return function nn(b){return S=>Ae(S.value)||!Ve(S.value)?null:S.value.length<b?{minlength:{requiredLength:b,actualLength:S.value.length}}:null}(S)}static maxLength(S){return function je(b){return S=>Ve(S.value)&&S.value.length>b?{maxlength:{requiredLength:b,actualLength:S.value.length}}:null}(S)}static pattern(S){return function ft(b){if(!b)return Ln;let S,v;return"string"==typeof b?(v="","^"!==b.charAt(0)&&(v+="^"),v+=b,"$"!==b.charAt(b.length-1)&&(v+="$"),S=new RegExp(v)):(v=b.toString(),S=b),P=>{if(Ae(P.value))return null;const B=P.value;return S.test(B)?null:{pattern:{requiredPattern:v,actualValue:B}}}}(S)}static nullValidator(S){return null}static compose(S){return Me(S)}static composeAsync(S){return dt(S)}}function nt(b){return S=>{if(Ae(S.value)||Ae(b))return null;const v=parseFloat(S.value);return!isNaN(v)&&v<b?{min:{min:b,actual:S.value}}:null}}function jt(b){return S=>{if(Ae(S.value)||Ae(b))return null;const v=parseFloat(S.value);return!isNaN(v)&&v>b?{max:{max:b,actual:S.value}}:null}}function Ln(b){return null}function jr(b){return null!=b}function Z(b){return(0,c.jNT)(b)?(0,ue.H)(b):b}function te(b){let S={};return b.forEach(v=>{S=null!=v?{...S,...v}:S}),0===Object.keys(S).length?null:S}function Y(b,S){return S.map(v=>v(b))}function Te(b){return b.map(S=>function le(b){return!b.validate}(S)?S:v=>S.validate(v))}function Me(b){if(!b)return null;const S=b.filter(jr);return 0==S.length?null:function(v){return te(Y(v,S))}}function Ge(b){return null!=b?Me(Te(b)):null}function dt(b){if(!b)return null;const S=b.filter(jr);return 0==S.length?null:function(v){return function z(...b){const S=(0,ne.ms)(b),{args:v,keys:P}=(0,Q.D)(b),B=new se.c(We=>{const{length:Dt}=v;if(!Dt)return void We.complete();const Jn=new Array(Dt);let Ii=Dt,yi=Dt;for(let Ti=0;Ti<Dt;Ti++){let Ai=!1;(0,oe.Tg)(v[Ti]).subscribe((0,fe._)(We,ii=>{Ai||(Ai=!0,yi--),Jn[Ti]=ii},()=>Ii--,void 0,()=>{(!Ii||!Ai)&&(yi||We.next(P?(0,pe.e)(P,Jn):Jn),We.complete())}))}});return S?B.pipe((0,ae.I)(S)):B}(Y(v,S).map(Z)).pipe((0,G.T)(te))}}function zt(b){return null!=b?dt(Te(b)):null}function lt(b,S){return null===b?[S]:Array.isArray(b)?[...b,S]:[b,S]}function xt(b){return b._rawValidators}function qt(b){return b._rawAsyncValidators}function rr(b){return b?Array.isArray(b)?b:[b]:[]}function yt(b,S){return Array.isArray(b)?b.includes(S):b===S}function yn(b,S){const v=rr(S);return rr(b).forEach(B=>{yt(v,B)||v.push(B)}),v}function Gt(b,S){return rr(S).filter(v=>!yt(b,v))}class qe{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(S){this._rawValidators=S||[],this._composedValidatorFn=Ge(this._rawValidators)}_setAsyncValidators(S){this._rawAsyncValidators=S||[],this._composedAsyncValidatorFn=zt(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(S){this._onDestroyCallbacks.push(S)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(S=>S()),this._onDestroyCallbacks=[]}reset(S=void 0){this.control&&this.control.reset(S)}hasError(S,v){return!!this.control&&this.control.hasError(S,v)}getError(S,v){return this.control?this.control.getError(S,v):null}}class Xe extends qe{get formDirective(){return null}get path(){return null}}class Vn extends qe{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}class Ur{constructor(S){this._cd=S}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}}let ht=(()=>{class b extends Ur{constructor(v){super(v)}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(Vn,2))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(P,B){2&P&&c.AVh("ng-untouched",B.isUntouched)("ng-touched",B.isTouched)("ng-pristine",B.isPristine)("ng-dirty",B.isDirty)("ng-valid",B.isValid)("ng-invalid",B.isInvalid)("ng-pending",B.isPending)},features:[c.Vt3]})}}return b})(),st=(()=>{class b extends Ur{constructor(v){super(v)}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(Xe,10))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(P,B){2&P&&c.AVh("ng-untouched",B.isUntouched)("ng-touched",B.isTouched)("ng-pristine",B.isPristine)("ng-dirty",B.isDirty)("ng-valid",B.isValid)("ng-invalid",B.isInvalid)("ng-pending",B.isPending)("ng-submitted",B.isSubmitted)},features:[c.Vt3]})}}return b})();const vt="VALID",er="INVALID",Gn="PENDING",Hr="DISABLED";function or(b){return(W(b)?b.validators:b)||null}function q(b,S){return(W(S)?S.asyncValidators:b)||null}function W(b){return null!=b&&!Array.isArray(b)&&"object"==typeof b}function ye(b,S,v){const P=b.controls;if(!(S?Object.keys(P):P).length)throw new c.wOt(1e3,"");if(!P[v])throw new c.wOt(1001,"")}function xe(b,S,v){b._forEachChild((P,B)=>{if(void 0===v[B])throw new c.wOt(1002,"")})}class Ye{constructor(S,v){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(S),this._assignAsyncValidators(v)}get validator(){return this._composedValidatorFn}set validator(S){this._rawValidators=this._composedValidatorFn=S}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(S){this._rawAsyncValidators=this._composedAsyncValidatorFn=S}get parent(){return this._parent}get valid(){return this.status===vt}get invalid(){return this.status===er}get pending(){return this.status==Gn}get disabled(){return this.status===Hr}get enabled(){return this.status!==Hr}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(S){this._assignValidators(S)}setAsyncValidators(S){this._assignAsyncValidators(S)}addValidators(S){this.setValidators(yn(S,this._rawValidators))}addAsyncValidators(S){this.setAsyncValidators(yn(S,this._rawAsyncValidators))}removeValidators(S){this.setValidators(Gt(S,this._rawValidators))}removeAsyncValidators(S){this.setAsyncValidators(Gt(S,this._rawAsyncValidators))}hasValidator(S){return yt(this._rawValidators,S)}hasAsyncValidator(S){return yt(this._rawAsyncValidators,S)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(S={}){this.touched=!0,this._parent&&!S.onlySelf&&this._parent.markAsTouched(S)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(S=>S.markAllAsTouched())}markAsUntouched(S={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(v=>{v.markAsUntouched({onlySelf:!0})}),this._parent&&!S.onlySelf&&this._parent._updateTouched(S)}markAsDirty(S={}){this.pristine=!1,this._parent&&!S.onlySelf&&this._parent.markAsDirty(S)}markAsPristine(S={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(v=>{v.markAsPristine({onlySelf:!0})}),this._parent&&!S.onlySelf&&this._parent._updatePristine(S)}markAsPending(S={}){this.status=Gn,!1!==S.emitEvent&&this.statusChanges.emit(this.status),this._parent&&!S.onlySelf&&this._parent.markAsPending(S)}disable(S={}){const v=this._parentMarkedDirty(S.onlySelf);this.status=Hr,this.errors=null,this._forEachChild(P=>{P.disable({...S,onlySelf:!0})}),this._updateValue(),!1!==S.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...S,skipPristineCheck:v}),this._onDisabledChange.forEach(P=>P(!0))}enable(S={}){const v=this._parentMarkedDirty(S.onlySelf);this.status=vt,this._forEachChild(P=>{P.enable({...S,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:S.emitEvent}),this._updateAncestors({...S,skipPristineCheck:v}),this._onDisabledChange.forEach(P=>P(!1))}_updateAncestors(S){this._parent&&!S.onlySelf&&(this._parent.updateValueAndValidity(S),S.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(S){this._parent=S}getRawValue(){return this.value}updateValueAndValidity(S={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===vt||this.status===Gn)&&this._runAsyncValidator(S.emitEvent)),!1!==S.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!S.onlySelf&&this._parent.updateValueAndValidity(S)}_updateTreeValidity(S={emitEvent:!0}){this._forEachChild(v=>v._updateTreeValidity(S)),this.updateValueAndValidity({onlySelf:!0,emitEvent:S.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Hr:vt}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(S){if(this.asyncValidator){this.status=Gn,this._hasOwnPendingAsyncValidator=!0;const v=Z(this.asyncValidator(this));this._asyncValidationSubscription=v.subscribe(P=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(P,{emitEvent:S})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(S,v={}){this.errors=S,this._updateControlsErrors(!1!==v.emitEvent)}get(S){let v=S;return null==v||(Array.isArray(v)||(v=v.split(".")),0===v.length)?null:v.reduce((P,B)=>P&&P._find(B),this)}getError(S,v){const P=v?this.get(v):this;return P&&P.errors?P.errors[S]:null}hasError(S,v){return!!this.getError(S,v)}get root(){let S=this;for(;S._parent;)S=S._parent;return S}_updateControlsErrors(S){this.status=this._calculateStatus(),S&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(S)}_initObservables(){this.valueChanges=new c.bkB,this.statusChanges=new c.bkB}_calculateStatus(){return this._allControlsDisabled()?Hr:this.errors?er:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Gn)?Gn:this._anyControlsHaveStatus(er)?er:vt}_anyControlsHaveStatus(S){return this._anyControls(v=>v.status===S)}_anyControlsDirty(){return this._anyControls(S=>S.dirty)}_anyControlsTouched(){return this._anyControls(S=>S.touched)}_updatePristine(S={}){this.pristine=!this._anyControlsDirty(),this._parent&&!S.onlySelf&&this._parent._updatePristine(S)}_updateTouched(S={}){this.touched=this._anyControlsTouched(),this._parent&&!S.onlySelf&&this._parent._updateTouched(S)}_registerOnCollectionChange(S){this._onCollectionChange=S}_setUpdateStrategy(S){W(S)&&null!=S.updateOn&&(this._updateOn=S.updateOn)}_parentMarkedDirty(S){return!S&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(S){return null}_assignValidators(S){this._rawValidators=Array.isArray(S)?S.slice():S,this._composedValidatorFn=function ce(b){return Array.isArray(b)?Ge(b):b||null}(this._rawValidators)}_assignAsyncValidators(S){this._rawAsyncValidators=Array.isArray(S)?S.slice():S,this._composedAsyncValidatorFn=function V(b){return Array.isArray(b)?zt(b):b||null}(this._rawAsyncValidators)}}class Et extends Ye{constructor(S,v,P){super(or(v),q(P,v)),this.controls=S,this._initObservables(),this._setUpdateStrategy(v),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(S,v){return this.controls[S]?this.controls[S]:(this.controls[S]=v,v.setParent(this),v._registerOnCollectionChange(this._onCollectionChange),v)}addControl(S,v,P={}){this.registerControl(S,v),this.updateValueAndValidity({emitEvent:P.emitEvent}),this._onCollectionChange()}removeControl(S,v={}){this.controls[S]&&this.controls[S]._registerOnCollectionChange(()=>{}),delete this.controls[S],this.updateValueAndValidity({emitEvent:v.emitEvent}),this._onCollectionChange()}setControl(S,v,P={}){this.controls[S]&&this.controls[S]._registerOnCollectionChange(()=>{}),delete this.controls[S],v&&this.registerControl(S,v),this.updateValueAndValidity({emitEvent:P.emitEvent}),this._onCollectionChange()}contains(S){return this.controls.hasOwnProperty(S)&&this.controls[S].enabled}setValue(S,v={}){xe(this,0,S),Object.keys(S).forEach(P=>{ye(this,!0,P),this.controls[P].setValue(S[P],{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v)}patchValue(S,v={}){null!=S&&(Object.keys(S).forEach(P=>{const B=this.controls[P];B&&B.patchValue(S[P],{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v))}reset(S={},v={}){this._forEachChild((P,B)=>{P.reset(S?S[B]:null,{onlySelf:!0,emitEvent:v.emitEvent})}),this._updatePristine(v),this._updateTouched(v),this.updateValueAndValidity(v)}getRawValue(){return this._reduceChildren({},(S,v,P)=>(S[P]=v.getRawValue(),S))}_syncPendingControls(){let S=this._reduceChildren(!1,(v,P)=>!!P._syncPendingControls()||v);return S&&this.updateValueAndValidity({onlySelf:!0}),S}_forEachChild(S){Object.keys(this.controls).forEach(v=>{const P=this.controls[v];P&&S(P,v)})}_setUpControls(){this._forEachChild(S=>{S.setParent(this),S._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(S){for(const[v,P]of Object.entries(this.controls))if(this.contains(v)&&S(P))return!0;return!1}_reduceValue(){return this._reduceChildren({},(v,P,B)=>((P.enabled||this.disabled)&&(v[B]=P.value),v))}_reduceChildren(S,v){let P=S;return this._forEachChild((B,We)=>{P=v(P,B,We)}),P}_allControlsDisabled(){for(const S of Object.keys(this.controls))if(this.controls[S].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(S){return this.controls.hasOwnProperty(S)?this.controls[S]:null}}class Wn extends Et{}const X=new c.nKC("CallSetDisabledState",{providedIn:"root",factory:()=>j}),j="always";function $(b,S){return[...S.path,b]}function he(b,S,v=j){$t(b,S),S.valueAccessor.writeValue(b.value),(b.disabled||"always"===v)&&S.valueAccessor.setDisabledState?.(b.disabled),function dn(b,S){S.valueAccessor.registerOnChange(v=>{b._pendingValue=v,b._pendingChange=!0,b._pendingDirty=!0,"change"===b.updateOn&&Dn(b,S)})}(b,S),function tr(b,S){const v=(P,B)=>{S.valueAccessor.writeValue(P),B&&S.viewToModelUpdate(P)};b.registerOnChange(v),S._registerOnDestroy(()=>{b._unregisterOnChange(v)})}(b,S),function Nn(b,S){S.valueAccessor.registerOnTouched(()=>{b._pendingTouched=!0,"blur"===b.updateOn&&b._pendingChange&&Dn(b,S),"submit"!==b.updateOn&&b.markAsTouched()})}(b,S),function Ne(b,S){if(S.valueAccessor.setDisabledState){const v=P=>{S.valueAccessor.setDisabledState(P)};b.registerOnDisabledChange(v),S._registerOnDestroy(()=>{b._unregisterOnDisabledChange(v)})}}(b,S)}function Ie(b,S,v=!0){const P=()=>{};S.valueAccessor&&(S.valueAccessor.registerOnChange(P),S.valueAccessor.registerOnTouched(P)),ke(b,S),b&&(S._invokeOnDestroyCallbacks(),b._registerOnCollectionChange(()=>{}))}function Ue(b,S){b.forEach(v=>{v.registerOnValidatorChange&&v.registerOnValidatorChange(S)})}function $t(b,S){const v=xt(b);null!==S.validator?b.setValidators(lt(v,S.validator)):"function"==typeof v&&b.setValidators([v]);const P=qt(b);null!==S.asyncValidator?b.setAsyncValidators(lt(P,S.asyncValidator)):"function"==typeof P&&b.setAsyncValidators([P]);const B=()=>b.updateValueAndValidity();Ue(S._rawValidators,B),Ue(S._rawAsyncValidators,B)}function ke(b,S){let v=!1;if(null!==b){if(null!==S.validator){const B=xt(b);if(Array.isArray(B)&&B.length>0){const We=B.filter(Dt=>Dt!==S.validator);We.length!==B.length&&(v=!0,b.setValidators(We))}}if(null!==S.asyncValidator){const B=qt(b);if(Array.isArray(B)&&B.length>0){const We=B.filter(Dt=>Dt!==S.asyncValidator);We.length!==B.length&&(v=!0,b.setAsyncValidators(We))}}}const P=()=>{};return Ue(S._rawValidators,P),Ue(S._rawAsyncValidators,P),v}function Dn(b,S){b._pendingDirty&&b.markAsDirty(),b.setValue(b._pendingValue,{emitModelToViewChange:!1}),S.viewToModelUpdate(b._pendingValue),b._pendingChange=!1}function Xn(b,S){if(!b.hasOwnProperty("model"))return!1;const v=b.model;return!!v.isFirstChange()||!Object.is(S,v.currentValue)}function Or(b,S){if(!S)return null;let v,P,B;return Array.isArray(S),S.forEach(We=>{We.constructor===Ce?v=We:function ci(b){return Object.getPrototypeOf(b.constructor)===we}(We)?P=We:B=We}),B||P||v||null}function wi(b,S){const v=b.indexOf(S);v>-1&&b.splice(v,1)}function ki(b){return"object"==typeof b&&null!==b&&2===Object.keys(b).length&&"value"in b&&"disabled"in b}const Qn=class extends Ye{constructor(S=null,v,P){super(or(v),q(P,v)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(S),this._setUpdateStrategy(v),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),W(v)&&(v.nonNullable||v.initialValueIsDefault)&&(this.defaultValue=ki(S)?S.value:S)}setValue(S,v={}){this.value=this._pendingValue=S,this._onChange.length&&!1!==v.emitModelToViewChange&&this._onChange.forEach(P=>P(this.value,!1!==v.emitViewToModelChange)),this.updateValueAndValidity(v)}patchValue(S,v={}){this.setValue(S,v)}reset(S=this.defaultValue,v={}){this._applyFormState(S),this.markAsPristine(v),this.markAsUntouched(v),this.setValue(this.value,v),this._pendingChange=!1}_updateValue(){}_anyControls(S){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(S){this._onChange.push(S)}_unregisterOnChange(S){wi(this._onChange,S)}registerOnDisabledChange(S){this._onDisabledChange.push(S)}_unregisterOnDisabledChange(S){wi(this._onDisabledChange,S)}_forEachChild(S){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(S){ki(S)?(this.value=this._pendingValue=S.value,S.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=S}},eo={provide:Vn,useExisting:(0,c.Rfq)(()=>fi)},Vi=(()=>Promise.resolve())();let fi=(()=>{class b extends Vn{constructor(v,P,B,We,Dt,Jn){super(),this._changeDetectorRef=Dt,this.callSetDisabledState=Jn,this.control=new Qn,this._registered=!1,this.name="",this.update=new c.bkB,this._parent=v,this._setValidators(P),this._setAsyncValidators(B),this.valueAccessor=Or(0,We)}ngOnChanges(v){if(this._checkForErrors(),!this._registered||"name"in v){if(this._registered&&(this._checkName(),this.formDirective)){const P=v.name.previousValue;this.formDirective.removeControl({name:P,path:this._getPath(P)})}this._setUpControl()}"isDisabled"in v&&this._updateDisabled(v),Xn(v,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(v){this.viewModel=v,this.update.emit(v)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!(!this.options||!this.options.standalone)}_setUpStandalone(){he(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),this._isStandalone()}_updateValue(v){Vi.then(()=>{this.control.setValue(v,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(v){const P=v.isDisabled.currentValue,B=0!==P&&(0,c.L39)(P);Vi.then(()=>{B&&!this.control.disabled?this.control.disable():!B&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(v){return this._parent?$(v,this._parent):[v]}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(Xe,9),c.rXU(tt,10),c.rXU(Pe,10),c.rXU(Fe,10),c.rXU(c.gRc,8),c.rXU(X,8))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:["disabled","isDisabled"],model:["ngModel","model"],options:["ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[c.Jv_([eo]),c.Vt3,c.OA$]})}}return b})(),to=(()=>{class b{static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275dir=c.FsC({type:b,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return b})();const po={provide:Fe,useExisting:(0,c.Rfq)(()=>no),multi:!0};let no=(()=>{class b extends we{writeValue(v){this.setProperty("value",v??"")}registerOnChange(v){this.onChange=P=>{v(""==P?null:parseFloat(P))}}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(P,B){1&P&&c.bIt("input",function(Dt){return B.onChange(Dt.target.value)})("blur",function(){return B.onTouched()})},features:[c.Jv_([po]),c.Vt3]})}}return b})();const Po={provide:Fe,useExisting:(0,c.Rfq)(()=>Gr),multi:!0};let ei=(()=>{class b{static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=c.$C({type:b})}static{this.\u0275inj=c.G2t({})}}return b})(),ro=(()=>{class b{constructor(){this._accessors=[]}add(v,P){this._accessors.push([v,P])}remove(v){for(let P=this._accessors.length-1;P>=0;--P)if(this._accessors[P][1]===v)return void this._accessors.splice(P,1)}select(v){this._accessors.forEach(P=>{this._isSameGroup(P,v)&&P[1]!==v&&P[1].fireUncheck(v.value)})}_isSameGroup(v,P){return!!v[0].control&&v[0]._parent===P._control._parent&&v[1].name===P.name}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275prov=c.jDH({token:b,factory:b.\u0275fac,providedIn:ei})}}return b})(),Gr=(()=>{class b extends we{constructor(v,P,B,We){super(v,P),this._registry=B,this._injector=We,this.setDisabledStateFired=!1,this.onChange=()=>{},this.callSetDisabledState=(0,c.WQX)(X,{optional:!0})??j}ngOnInit(){this._control=this._injector.get(Vn),this._checkName(),this._registry.add(this._control,this)}ngOnDestroy(){this._registry.remove(this)}writeValue(v){this._state=v===this.value,this.setProperty("checked",this._state)}registerOnChange(v){this._fn=v,this.onChange=()=>{v(this.value),this._registry.select(this)}}setDisabledState(v){(this.setDisabledStateFired||v||"whenDisabledForLegacyCode"===this.callSetDisabledState)&&this.setProperty("disabled",v),this.setDisabledStateFired=!0}fireUncheck(v){this.writeValue(v)}_checkName(){!this.name&&this.formControlName&&(this.name=this.formControlName)}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(c.sFG),c.rXU(c.aKT),c.rXU(ro),c.rXU(c.zZn))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","type","radio","formControlName",""],["input","type","radio","formControl",""],["input","type","radio","ngModel",""]],hostBindings:function(P,B){1&P&&c.bIt("change",function(){return B.onChange()})("blur",function(){return B.onTouched()})},inputs:{name:"name",formControlName:"formControlName",value:"value"},features:[c.Jv_([Po]),c.Vt3]})}}return b})();const ni=new c.nKC("NgModelWithFormControlWarning"),io={provide:Xe,useExisting:(0,c.Rfq)(()=>pt)};let pt=(()=>{class b extends Xe{constructor(v,P,B){super(),this.callSetDisabledState=B,this.submitted=!1,this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new c.bkB,this._setValidators(v),this._setAsyncValidators(P)}ngOnChanges(v){this._checkFormPresent(),v.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(ke(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(v){const P=this.form.get(v.path);return he(P,v,this.callSetDisabledState),P.updateValueAndValidity({emitEvent:!1}),this.directives.push(v),P}getControl(v){return this.form.get(v.path)}removeControl(v){Ie(v.control||null,v,!1),function gr(b,S){const v=b.indexOf(S);v>-1&&b.splice(v,1)}(this.directives,v)}addFormGroup(v){this._setUpFormContainer(v)}removeFormGroup(v){this._cleanUpFormContainer(v)}getFormGroup(v){return this.form.get(v.path)}addFormArray(v){this._setUpFormContainer(v)}removeFormArray(v){this._cleanUpFormContainer(v)}getFormArray(v){return this.form.get(v.path)}updateModel(v,P){this.form.get(v.path).setValue(P)}onSubmit(v){return this.submitted=!0,function di(b,S){b._syncPendingControls(),S.forEach(v=>{const P=v.control;"submit"===P.updateOn&&P._pendingChange&&(v.viewToModelUpdate(P._pendingValue),P._pendingChange=!1)})}(this.form,this.directives),this.ngSubmit.emit(v),"dialog"===v?.target?.method}onReset(){this.resetForm()}resetForm(v=void 0){this.form.reset(v),this.submitted=!1}_updateDomValue(){this.directives.forEach(v=>{const P=v.control,B=this.form.get(v.path);P!==B&&(Ie(P||null,v),(b=>b instanceof Qn)(B)&&(he(B,v,this.callSetDisabledState),v.control=B))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(v){const P=this.form.get(v.path);(function Yr(b,S){$t(b,S)})(P,v),P.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(v){if(this.form){const P=this.form.get(v.path);P&&function sr(b,S){return ke(b,S)}(P,v)&&P.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){$t(this.form,this),this._oldForm&&ke(this._oldForm,this)}_checkFormPresent(){}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(tt,10),c.rXU(Pe,10),c.rXU(X,8))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["","formGroup",""]],hostBindings:function(P,B){1&P&&c.bIt("submit",function(Dt){return B.onSubmit(Dt)})("reset",function(){return B.onReset()})},inputs:{form:["formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[c.Jv_([io]),c.Vt3,c.OA$]})}}return b})();const Ui={provide:Vn,useExisting:(0,c.Rfq)(()=>pi)};let pi=(()=>{class b extends Vn{set isDisabled(v){}static{this._ngModelWarningSentOnce=!1}constructor(v,P,B,We,Dt){super(),this._ngModelWarningConfig=Dt,this._added=!1,this.name=null,this.update=new c.bkB,this._ngModelWarningSent=!1,this._parent=v,this._setValidators(P),this._setAsyncValidators(B),this.valueAccessor=Or(0,We)}ngOnChanges(v){this._added||this._setUpControl(),Xn(v,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(v){this.viewModel=v,this.update.emit(v)}get path(){return $(null==this.name?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}_setUpControl(){this._checkParentType(),this.control=this.formDirective.addControl(this),this._added=!0}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(Xe,13),c.rXU(tt,10),c.rXU(Pe,10),c.rXU(Fe,10),c.rXU(ni,8))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["","formControlName",""]],inputs:{name:["formControlName","name"],isDisabled:["disabled","isDisabled"],model:["ngModel","model"]},outputs:{update:"ngModelChange"},features:[c.Jv_([Ui]),c.Vt3,c.OA$]})}}return b})();const Bi={provide:Fe,useExisting:(0,c.Rfq)(()=>Le),multi:!0};function oo(b,S){return null==b?`${S}`:(S&&"object"==typeof S&&(S="Object"),`${b}: ${S}`.slice(0,50))}let Le=(()=>{class b extends we{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(v){this._compareWith=v}writeValue(v){this.value=v;const B=oo(this._getOptionId(v),v);this.setProperty("value",B)}registerOnChange(v){this.onChange=P=>{this.value=this._getOptionValue(P),v(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(v){for(const P of this._optionMap.keys())if(this._compareWith(this._optionMap.get(P),v))return P;return null}_getOptionValue(v){const P=function an(b){return b.split(":")[0]}(v);return this._optionMap.has(P)?this._optionMap.get(P):v}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(P,B){1&P&&c.bIt("change",function(Dt){return B.onChange(Dt.target.value)})("blur",function(){return B.onTouched()})},inputs:{compareWith:"compareWith"},features:[c.Jv_([Bi]),c.Vt3]})}}return b})(),mt=(()=>{class b{constructor(v,P,B){this._element=v,this._renderer=P,this._select=B,this._select&&(this.id=this._select._registerOption())}set ngValue(v){null!=this._select&&(this._select._optionMap.set(this.id,v),this._setElementValue(oo(this.id,v)),this._select.writeValue(this._select.value))}set value(v){this._setElementValue(v),this._select&&this._select.writeValue(this._select.value)}_setElementValue(v){this._renderer.setProperty(this._element.nativeElement,"value",v)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(c.aKT),c.rXU(c.sFG),c.rXU(Le,9))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}})}}return b})();const on={provide:Fe,useExisting:(0,c.Rfq)(()=>Mr),multi:!0};function qn(b,S){return null==b?`${S}`:("string"==typeof S&&(S=`'${S}'`),S&&"object"==typeof S&&(S="Object"),`${b}: ${S}`.slice(0,50))}let Mr=(()=>{class b extends we{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(v){this._compareWith=v}writeValue(v){let P;if(this.value=v,Array.isArray(v)){const B=v.map(We=>this._getOptionId(We));P=(We,Dt)=>{We._setSelected(B.indexOf(Dt.toString())>-1)}}else P=(B,We)=>{B._setSelected(!1)};this._optionMap.forEach(P)}registerOnChange(v){this.onChange=P=>{const B=[],We=P.selectedOptions;if(void 0!==We){const Dt=We;for(let Jn=0;Jn<Dt.length;Jn++){const yi=this._getOptionValue(Dt[Jn].value);B.push(yi)}}else{const Dt=P.options;for(let Jn=0;Jn<Dt.length;Jn++){const Ii=Dt[Jn];if(Ii.selected){const yi=this._getOptionValue(Ii.value);B.push(yi)}}}this.value=B,v(B)}}_registerOption(v){const P=(this._idCounter++).toString();return this._optionMap.set(P,v),P}_getOptionId(v){for(const P of this._optionMap.keys())if(this._compareWith(this._optionMap.get(P)._value,v))return P;return null}_getOptionValue(v){const P=function $i(b){return b.split(":")[0]}(v);return this._optionMap.has(P)?this._optionMap.get(P)._value:v}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(P,B){1&P&&c.bIt("change",function(Dt){return B.onChange(Dt.target)})("blur",function(){return B.onTouched()})},inputs:{compareWith:"compareWith"},features:[c.Jv_([on]),c.Vt3]})}}return b})(),gn=(()=>{class b{constructor(v,P,B){this._element=v,this._renderer=P,this._select=B,this._select&&(this.id=this._select._registerOption(this))}set ngValue(v){null!=this._select&&(this._value=v,this._setElementValue(qn(this.id,v)),this._select.writeValue(this._select.value))}set value(v){this._select?(this._value=v,this._setElementValue(qn(this.id,v)),this._select.writeValue(this._select.value)):this._setElementValue(v)}_setElementValue(v){this._renderer.setProperty(this._element.nativeElement,"value",v)}_setSelected(v){this._renderer.setProperty(this._element.nativeElement,"selected",v)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(c.aKT),c.rXU(c.sFG),c.rXU(Mr,9))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}})}}return b})();function ri(b){return"number"==typeof b?b:parseFloat(b)}let ut=(()=>{class b{constructor(){this._validator=Ln}ngOnChanges(v){if(this.inputName in v){const P=this.normalizeInput(v[this.inputName].currentValue);this._enabled=this.enabled(P),this._validator=this._enabled?this.createValidator(P):Ln,this._onChange&&this._onChange()}}validate(v){return this._validator(v)}registerOnValidatorChange(v){this._onChange=v}enabled(v){return null!=v}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275dir=c.FsC({type:b,features:[c.OA$]})}}return b})();const Fr={provide:tt,useExisting:(0,c.Rfq)(()=>Hi),multi:!0};let Hi=(()=>{class b extends ut{constructor(){super(...arguments),this.inputName="max",this.normalizeInput=v=>ri(v),this.createValidator=v=>jt(v)}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(P,B){2&P&&c.BMQ("max",B._enabled?B.max:null)},inputs:{max:"max"},features:[c.Jv_([Fr]),c.Vt3]})}}return b})();const Wr={provide:tt,useExisting:(0,c.Rfq)(()=>fn),multi:!0};let fn=(()=>{class b extends ut{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=v=>ri(v),this.createValidator=v=>nt(v)}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(P,B){2&P&&c.BMQ("min",B._enabled?B.min:null)},inputs:{min:"min"},features:[c.Jv_([Wr]),c.Vt3]})}}return b})(),Dr=(()=>{class b{static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=c.$C({type:b})}static{this.\u0275inj=c.G2t({imports:[ei]})}}return b})();class Si extends Ye{constructor(S,v,P){super(or(v),q(P,v)),this.controls=S,this._initObservables(),this._setUpdateStrategy(v),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}at(S){return this.controls[this._adjustIndex(S)]}push(S,v={}){this.controls.push(S),this._registerControl(S),this.updateValueAndValidity({emitEvent:v.emitEvent}),this._onCollectionChange()}insert(S,v,P={}){this.controls.splice(S,0,v),this._registerControl(v),this.updateValueAndValidity({emitEvent:P.emitEvent})}removeAt(S,v={}){let P=this._adjustIndex(S);P<0&&(P=0),this.controls[P]&&this.controls[P]._registerOnCollectionChange(()=>{}),this.controls.splice(P,1),this.updateValueAndValidity({emitEvent:v.emitEvent})}setControl(S,v,P={}){let B=this._adjustIndex(S);B<0&&(B=0),this.controls[B]&&this.controls[B]._registerOnCollectionChange(()=>{}),this.controls.splice(B,1),v&&(this.controls.splice(B,0,v),this._registerControl(v)),this.updateValueAndValidity({emitEvent:P.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(S,v={}){xe(this,0,S),S.forEach((P,B)=>{ye(this,!1,B),this.at(B).setValue(P,{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v)}patchValue(S,v={}){null!=S&&(S.forEach((P,B)=>{this.at(B)&&this.at(B).patchValue(P,{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v))}reset(S=[],v={}){this._forEachChild((P,B)=>{P.reset(S[B],{onlySelf:!0,emitEvent:v.emitEvent})}),this._updatePristine(v),this._updateTouched(v),this.updateValueAndValidity(v)}getRawValue(){return this.controls.map(S=>S.getRawValue())}clear(S={}){this.controls.length<1||(this._forEachChild(v=>v._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:S.emitEvent}))}_adjustIndex(S){return S<0?S+this.length:S}_syncPendingControls(){let S=this.controls.reduce((v,P)=>!!P._syncPendingControls()||v,!1);return S&&this.updateValueAndValidity({onlySelf:!0}),S}_forEachChild(S){this.controls.forEach((v,P)=>{S(v,P)})}_updateValue(){this.value=this.controls.filter(S=>S.enabled||this.disabled).map(S=>S.value)}_anyControls(S){return this.controls.some(v=>v.enabled&&S(v))}_setUpControls(){this._forEachChild(S=>this._registerControl(S))}_allControlsDisabled(){for(const S of this.controls)if(S.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(S){S.setParent(this),S._registerOnCollectionChange(this._onCollectionChange)}_find(S){return this.at(S)??null}}function On(b){return!!b&&(void 0!==b.asyncValidators||void 0!==b.validators||void 0!==b.updateOn)}let kn=(()=>{class b{constructor(){this.useNonNullable=!1}get nonNullable(){const v=new b;return v.useNonNullable=!0,v}group(v,P=null){const B=this._reduceControls(v);let We={};return On(P)?We=P:null!==P&&(We.validators=P.validator,We.asyncValidators=P.asyncValidator),new Et(B,We)}record(v,P=null){const B=this._reduceControls(v);return new Wn(B,P)}control(v,P,B){let We={};return this.useNonNullable?(On(P)?We=P:(We.validators=P,We.asyncValidators=B),new Qn(v,{...We,nonNullable:!0})):new Qn(v,P,B)}array(v,P,B){const We=v.map(Dt=>this._createControl(Dt));return new Si(We,P,B)}_reduceControls(v){const P={};return Object.keys(v).forEach(B=>{P[B]=this._createControl(v[B])}),P}_createControl(v){return v instanceof Qn||v instanceof Ye?v:Array.isArray(v)?this.control(v[0],v.length>1?v[1]:null,v.length>2?v[2]:null):this.control(v)}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275prov=c.jDH({token:b,factory:b.\u0275fac,providedIn:"root"})}}return b})(),lr=(()=>{class b{static withConfig(v){return{ngModule:b,providers:[{provide:X,useValue:v.callSetDisabledState??j}]}}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=c.$C({type:b})}static{this.\u0275inj=c.G2t({imports:[Dr]})}}return b})(),mo=(()=>{class b{static withConfig(v){return{ngModule:b,providers:[{provide:ni,useValue:v.warnOnNgModelWithFormControl??"always"},{provide:X,useValue:v.callSetDisabledState??j}]}}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=c.$C({type:b})}static{this.\u0275inj=c.G2t({imports:[Dr]})}}return b})()},345:(Je,me,O)=>{O.d(me,{B7:()=>Oe,Bb:()=>yt,hE:()=>Vn,sG:()=>lt});var c=O(540),C=O(177);class ue extends C.VF{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class se extends ue{static makeCurrent(){(0,C.ZD)(new se)}onAndCancel(q,V,W){return q.addEventListener(V,W),()=>{q.removeEventListener(V,W)}}dispatchEvent(q,V){q.dispatchEvent(V)}remove(q){q.parentNode&&q.parentNode.removeChild(q)}createElement(q,V){return(V=V||this.getDefaultDocument()).createElement(q)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(q){return q.nodeType===Node.ELEMENT_NODE}isShadowRoot(q){return q instanceof DocumentFragment}getGlobalEventTarget(q,V){return"window"===V?window:"document"===V?q:"body"===V?q.body:null}getBaseHref(q){const V=function oe(){return Q=Q||document.querySelector("base"),Q?Q.getAttribute("href"):null}();return null==V?null:function fe(ce){ne=ne||document.createElement("a"),ne.setAttribute("href",ce);const q=ne.pathname;return"/"===q.charAt(0)?q:`/${q}`}(V)}resetBaseElement(){Q=null}getUserAgent(){return window.navigator.userAgent}getCookie(q){return(0,C._b)(document.cookie,q)}}let ne,Q=null,pe=(()=>{class ce{build(){return new XMLHttpRequest}static{this.\u0275fac=function(W){return new(W||ce)}}static{this.\u0275prov=c.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();const z=new c.nKC("EventManagerPlugins");let G=(()=>{class ce{constructor(V,W){this._zone=W,this._eventNameToPlugin=new Map,V.forEach(ye=>{ye.manager=this}),this._plugins=V.slice().reverse()}addEventListener(V,W,ye){return this._findPluginFor(W).addEventListener(V,W,ye)}getZone(){return this._zone}_findPluginFor(V){let W=this._eventNameToPlugin.get(V);if(W)return W;if(W=this._plugins.find(xe=>xe.supports(V)),!W)throw new c.wOt(5101,!1);return this._eventNameToPlugin.set(V,W),W}static{this.\u0275fac=function(W){return new(W||ce)(c.KVO(z),c.KVO(c.SKi))}}static{this.\u0275prov=c.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();class De{constructor(q){this._doc=q}}const we="ng-app-id";let Fe=(()=>{class ce{constructor(V,W,ye,xe={}){this.doc=V,this.appId=W,this.nonce=ye,this.platformId=xe,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=(0,C.Vy)(xe),this.resetHostNodes()}addStyles(V){for(const W of V)1===this.changeUsageCount(W,1)&&this.onStyleAdded(W)}removeStyles(V){for(const W of V)this.changeUsageCount(W,-1)<=0&&this.onStyleRemoved(W)}ngOnDestroy(){const V=this.styleNodesInDOM;V&&(V.forEach(W=>W.remove()),V.clear());for(const W of this.getAllStyles())this.onStyleRemoved(W);this.resetHostNodes()}addHost(V){this.hostNodes.add(V);for(const W of this.getAllStyles())this.addStyleToHost(V,W)}removeHost(V){this.hostNodes.delete(V)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(V){for(const W of this.hostNodes)this.addStyleToHost(W,V)}onStyleRemoved(V){const W=this.styleRef;W.get(V)?.elements?.forEach(ye=>ye.remove()),W.delete(V)}collectServerRenderedStyles(){const V=this.doc.head?.querySelectorAll(`style[${we}="${this.appId}"]`);if(V?.length){const W=new Map;return V.forEach(ye=>{null!=ye.textContent&&W.set(ye.textContent,ye)}),W}return null}changeUsageCount(V,W){const ye=this.styleRef;if(ye.has(V)){const xe=ye.get(V);return xe.usage+=W,xe.usage}return ye.set(V,{usage:W,elements:[]}),W}getStyleElement(V,W){const ye=this.styleNodesInDOM,xe=ye?.get(W);if(xe?.parentNode===V)return ye.delete(W),xe.removeAttribute(we),xe;{const Ye=this.doc.createElement("style");return this.nonce&&Ye.setAttribute("nonce",this.nonce),Ye.textContent=W,this.platformIsServer&&Ye.setAttribute(we,this.appId),Ye}}addStyleToHost(V,W){const ye=this.getStyleElement(V,W);V.appendChild(ye);const xe=this.styleRef,Ye=xe.get(W)?.elements;Ye?Ye.push(ye):xe.set(W,{elements:[ye],usage:1})}resetHostNodes(){const V=this.hostNodes;V.clear(),V.add(this.doc.head)}static{this.\u0275fac=function(W){return new(W||ce)(c.KVO(C.qQ),c.KVO(c.sZ2),c.KVO(c.BIS,8),c.KVO(c.Agw))}}static{this.\u0275prov=c.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();const kt={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},Mt=/%COMP%/g,Ae=new c.nKC("RemoveStylesOnCompDestroy",{providedIn:"root",factory:()=>!1});function Pe(ce,q){return q.map(V=>V.replace(Mt,ce))}let Oe=(()=>{class ce{constructor(V,W,ye,xe,Ye,Et,Ft,en=null){this.eventManager=V,this.sharedStylesHost=W,this.appId=ye,this.removeStylesOnCompDestroy=xe,this.doc=Ye,this.platformId=Et,this.ngZone=Ft,this.nonce=en,this.rendererByCompId=new Map,this.platformIsServer=(0,C.Vy)(Et),this.defaultRenderer=new be(V,Ye,Ft,this.platformIsServer)}createRenderer(V,W){if(!V||!W)return this.defaultRenderer;this.platformIsServer&&W.encapsulation===c.gXe.ShadowDom&&(W={...W,encapsulation:c.gXe.Emulated});const ye=this.getOrCreateRenderer(V,W);return ye instanceof nn?ye.applyToHost(V):ye instanceof Re&&ye.applyStyles(),ye}getOrCreateRenderer(V,W){const ye=this.rendererByCompId;let xe=ye.get(W.id);if(!xe){const Ye=this.doc,Et=this.ngZone,Ft=this.eventManager,en=this.sharedStylesHost,Wn=this.removeStylesOnCompDestroy,pr=this.platformIsServer;switch(W.encapsulation){case c.gXe.Emulated:xe=new nn(Ft,en,W,this.appId,Wn,Ye,Et,pr);break;case c.gXe.ShadowDom:return new Hn(Ft,en,V,W,Ye,Et,this.nonce,pr);default:xe=new Re(Ft,en,W,Wn,Ye,Et,pr)}ye.set(W.id,xe)}return xe}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(W){return new(W||ce)(c.KVO(G),c.KVO(Fe),c.KVO(c.sZ2),c.KVO(Ae),c.KVO(C.qQ),c.KVO(c.Agw),c.KVO(c.SKi),c.KVO(c.BIS))}}static{this.\u0275prov=c.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();class be{constructor(q,V,W,ye){this.eventManager=q,this.doc=V,this.ngZone=W,this.platformIsServer=ye,this.data=Object.create(null),this.destroyNode=null}destroy(){}createElement(q,V){return V?this.doc.createElementNS(kt[V]||V,q):this.doc.createElement(q)}createComment(q){return this.doc.createComment(q)}createText(q){return this.doc.createTextNode(q)}appendChild(q,V){(Mn(q)?q.content:q).appendChild(V)}insertBefore(q,V,W){q&&(Mn(q)?q.content:q).insertBefore(V,W)}removeChild(q,V){q&&q.removeChild(V)}selectRootElement(q,V){let W="string"==typeof q?this.doc.querySelector(q):q;if(!W)throw new c.wOt(-5104,!1);return V||(W.textContent=""),W}parentNode(q){return q.parentNode}nextSibling(q){return q.nextSibling}setAttribute(q,V,W,ye){if(ye){V=ye+":"+V;const xe=kt[ye];xe?q.setAttributeNS(xe,V,W):q.setAttribute(V,W)}else q.setAttribute(V,W)}removeAttribute(q,V,W){if(W){const ye=kt[W];ye?q.removeAttributeNS(ye,V):q.removeAttribute(`${W}:${V}`)}else q.removeAttribute(V)}addClass(q,V){q.classList.add(V)}removeClass(q,V){q.classList.remove(V)}setStyle(q,V,W,ye){ye&(c.czy.DashCase|c.czy.Important)?q.style.setProperty(V,W,ye&c.czy.Important?"important":""):q.style[V]=W}removeStyle(q,V,W){W&c.czy.DashCase?q.style.removeProperty(V):q.style[V]=""}setProperty(q,V,W){q[V]=W}setValue(q,V){q.nodeValue=V}listen(q,V,W){if("string"==typeof q&&!(q=(0,C.QT)().getGlobalEventTarget(this.doc,q)))throw new Error(`Unsupported event target ${q} for event ${V}`);return this.eventManager.addEventListener(q,V,this.decoratePreventDefault(W))}decoratePreventDefault(q){return V=>{if("__ngUnwrap__"===V)return q;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>q(V)):q(V))&&V.preventDefault()}}}function Mn(ce){return"TEMPLATE"===ce.tagName&&void 0!==ce.content}class Hn extends be{constructor(q,V,W,ye,xe,Ye,Et,Ft){super(q,xe,Ye,Ft),this.sharedStylesHost=V,this.hostEl=W,this.shadowRoot=W.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const en=Pe(ye.id,ye.styles);for(const Wn of en){const pr=document.createElement("style");Et&&pr.setAttribute("nonce",Et),pr.textContent=Wn,this.shadowRoot.appendChild(pr)}}nodeOrShadowRoot(q){return q===this.hostEl?this.shadowRoot:q}appendChild(q,V){return super.appendChild(this.nodeOrShadowRoot(q),V)}insertBefore(q,V,W){return super.insertBefore(this.nodeOrShadowRoot(q),V,W)}removeChild(q,V){return super.removeChild(this.nodeOrShadowRoot(q),V)}parentNode(q){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(q)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class Re extends be{constructor(q,V,W,ye,xe,Ye,Et,Ft){super(q,xe,Ye,Et),this.sharedStylesHost=V,this.removeStylesOnCompDestroy=ye,this.styles=Ft?Pe(Ft,W.styles):W.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}}class nn extends Re{constructor(q,V,W,ye,xe,Ye,Et,Ft){const en=ye+"-"+W.id;super(q,V,W,xe,Ye,Et,Ft,en),this.contentAttr=function Ve(ce){return"_ngcontent-%COMP%".replace(Mt,ce)}(en),this.hostAttr=function tt(ce){return"_nghost-%COMP%".replace(Mt,ce)}(en)}applyToHost(q){this.applyStyles(),this.setAttribute(q,this.hostAttr,"")}createElement(q,V){const W=super.createElement(q,V);return super.setAttribute(W,this.contentAttr,""),W}}let je=(()=>{class ce extends De{constructor(V){super(V)}supports(V){return!0}addEventListener(V,W,ye){return V.addEventListener(W,ye,!1),()=>this.removeEventListener(V,W,ye)}removeEventListener(V,W,ye){return V.removeEventListener(W,ye)}static{this.\u0275fac=function(W){return new(W||ce)(c.KVO(C.qQ))}}static{this.\u0275prov=c.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();const ft=["alt","control","meta","shift"],Ln={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},jr={alt:ce=>ce.altKey,control:ce=>ce.ctrlKey,meta:ce=>ce.metaKey,shift:ce=>ce.shiftKey};let Z=(()=>{class ce extends De{constructor(V){super(V)}supports(V){return null!=ce.parseEventName(V)}addEventListener(V,W,ye){const xe=ce.parseEventName(W),Ye=ce.eventCallback(xe.fullKey,ye,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>(0,C.QT)().onAndCancel(V,xe.domEventName,Ye))}static parseEventName(V){const W=V.toLowerCase().split("."),ye=W.shift();if(0===W.length||"keydown"!==ye&&"keyup"!==ye)return null;const xe=ce._normalizeKey(W.pop());let Ye="",Et=W.indexOf("code");if(Et>-1&&(W.splice(Et,1),Ye="code."),ft.forEach(en=>{const Wn=W.indexOf(en);Wn>-1&&(W.splice(Wn,1),Ye+=en+".")}),Ye+=xe,0!=W.length||0===xe.length)return null;const Ft={};return Ft.domEventName=ye,Ft.fullKey=Ye,Ft}static matchEventFullKeyCode(V,W){let ye=Ln[V.key]||V.key,xe="";return W.indexOf("code.")>-1&&(ye=V.code,xe="code."),!(null==ye||!ye)&&(ye=ye.toLowerCase()," "===ye?ye="space":"."===ye&&(ye="dot"),ft.forEach(Ye=>{Ye!==ye&&(0,jr[Ye])(V)&&(xe+=Ye+".")}),xe+=ye,xe===W)}static eventCallback(V,W,ye){return xe=>{ce.matchEventFullKeyCode(xe,V)&&ye.runGuarded(()=>W(xe))}}static _normalizeKey(V){return"esc"===V?"escape":V}static{this.\u0275fac=function(W){return new(W||ce)(c.KVO(C.qQ))}}static{this.\u0275prov=c.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();const lt=(0,c.oH4)(c.fpN,"browser",[{provide:c.Agw,useValue:C.AJ},{provide:c.PLl,useValue:function Me(){se.makeCurrent()},multi:!0},{provide:C.qQ,useFactory:function dt(){return(0,c.TL$)(document),document},deps:[]}]),xt=new c.nKC(""),qt=[{provide:c.e01,useClass:class ae{addToWindow(q){c.JZv.getAngularTestability=(W,ye=!0)=>{const xe=q.findTestabilityInTree(W,ye);if(null==xe)throw new c.wOt(5103,!1);return xe},c.JZv.getAllAngularTestabilities=()=>q.getAllTestabilities(),c.JZv.getAllAngularRootElements=()=>q.getAllRootElements(),c.JZv.frameworkStabilizers||(c.JZv.frameworkStabilizers=[]),c.JZv.frameworkStabilizers.push(W=>{const ye=c.JZv.getAllAngularTestabilities();let xe=ye.length,Ye=!1;const Et=function(Ft){Ye=Ye||Ft,xe--,0==xe&&W(Ye)};ye.forEach(Ft=>{Ft.whenStable(Et)})})}findTestabilityInTree(q,V,W){return null==V?null:q.getTestability(V)??(W?(0,C.QT)().isShadowRoot(V)?this.findTestabilityInTree(q,V.host,!0):this.findTestabilityInTree(q,V.parentElement,!0):null)}},deps:[]},{provide:c.WHO,useClass:c.NYb,deps:[c.SKi,c.giA,c.e01]},{provide:c.NYb,useClass:c.NYb,deps:[c.SKi,c.giA,c.e01]}],rr=[{provide:c.H8p,useValue:"root"},{provide:c.zcH,useFactory:function Ge(){return new c.zcH},deps:[]},{provide:z,useClass:je,multi:!0,deps:[C.qQ,c.SKi,c.Agw]},{provide:z,useClass:Z,multi:!0,deps:[C.qQ]},Oe,Fe,G,{provide:c._9s,useExisting:Oe},{provide:C.N0,useClass:pe,deps:[]},[]];let yt=(()=>{class ce{constructor(V){}static withServerTransition(V){return{ngModule:ce,providers:[{provide:c.sZ2,useValue:V.appId}]}}static{this.\u0275fac=function(W){return new(W||ce)(c.KVO(xt,12))}}static{this.\u0275mod=c.$C({type:ce})}static{this.\u0275inj=c.G2t({providers:[...rr,...qt],imports:[C.MD,c.Hbi]})}}return ce})(),Vn=(()=>{class ce{constructor(V){this._doc=V}getTitle(){return this._doc.title}setTitle(V){this._doc.title=V||""}static{this.\u0275fac=function(W){return new(W||ce)(c.KVO(C.qQ))}}static{this.\u0275prov=c.jDH({token:ce,factory:function(W){let ye=null;return ye=W?new W:function Xe(){return new Vn((0,c.KVO)(C.qQ))}(),ye},providedIn:"root"})}}return ce})();typeof window<"u"&&window},2434:(Je,me,O)=>{O.d(me,{nX:()=>br,wF:()=>Pr,Ix:()=>tn,Wk:()=>Rn,wQ:()=>bn,iI:()=>sl,n3:()=>qn});var c=O(540),C=O(1985),ue=O(8071),Q=O(6648),oe=O(7673),ne=O(4412),fe=O(3073),ae=O(3669),pe=O(6450),z=O(9326),G=O(8496),De=O(4360),we=O(5225);function Fe(...d){const g=(0,z.lI)(d),u=(0,z.ms)(d),{args:m,keys:w}=(0,fe.D)(d);if(0===m.length)return(0,Q.H)([],g);const N=new C.c(function kt(d,g,u=ae.D){return m=>{Mt(g,()=>{const{length:w}=d,N=new Array(w);let F=w,re=w;for(let ee=0;ee<w;ee++)Mt(g,()=>{const He=(0,Q.H)(d[ee],g);let bt=!1;He.subscribe((0,De._)(m,cn=>{N[ee]=cn,bt||(bt=!0,re--),re||m.next(u(N.slice()))},()=>{--F||m.complete()}))},m)},m)}}(m,g,w?F=>(0,G.e)(w,F):ae.D));return u?N.pipe((0,pe.I)(u)):N}function Mt(d,g,u){d?(0,we.N)(u,d,g):g()}const Vt=(0,O(1853).L)(d=>function(){d(this),this.name="EmptyError",this.message="no elements in sequence"});var Qt=O(6365);function Ae(...d){return function Ce(){return(0,Qt.U)(1)}()((0,Q.H)(d,(0,z.lI)(d)))}var Ve=O(8750);function tt(d){return new C.c(g=>{(0,Ve.Tg)(d()).subscribe(g)})}var Pe=O(1203),Oe=O(8810),be=O(983),nt=O(8359),jt=O(9974);function Mn(){return(0,jt.N)((d,g)=>{let u=null;d._refCount++;const m=(0,De._)(g,void 0,void 0,void 0,()=>{if(!d||d._refCount<=0||0<--d._refCount)return void(u=null);const w=d._connection,N=u;u=null,w&&(!N||w===N)&&w.unsubscribe(),g.unsubscribe()});d.subscribe(m),m.closed||(u=d.connect())})}class Hn extends C.c{constructor(g,u){super(),this.source=g,this.subjectFactory=u,this._subject=null,this._refCount=0,this._connection=null,(0,jt.S)(g)&&(this.lift=g.lift)}_subscribe(g){return this.getSubject().subscribe(g)}getSubject(){const g=this._subject;return(!g||g.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;const{_connection:g}=this;this._subject=this._connection=null,g?.unsubscribe()}connect(){let g=this._connection;if(!g){g=this._connection=new nt.yU;const u=this.getSubject();g.add(this.source.subscribe((0,De._)(u,void 0,()=>{this._teardown(),u.complete()},m=>{this._teardown(),u.error(m)},()=>this._teardown()))),g.closed&&(this._connection=null,g=nt.yU.EMPTY)}return g}refCount(){return Mn()(this)}}var Re=O(3794),nn=O(177),je=O(6354),ft=O(5558);function Ln(d){return d<=0?()=>be.w:(0,jt.N)((g,u)=>{let m=0;g.subscribe((0,De._)(u,w=>{++m<=d&&(u.next(w),d<=m&&u.complete())}))})}var Z=O(5964),te=O(1397);function Y(d){return(0,jt.N)((g,u)=>{let m=!1;g.subscribe((0,De._)(u,w=>{m=!0,u.next(w)},()=>{m||u.next(d),u.complete()}))})}function le(d=Te){return(0,jt.N)((g,u)=>{let m=!1;g.subscribe((0,De._)(u,w=>{m=!0,u.next(w)},()=>m?u.complete():u.error(d())))})}function Te(){return new Vt}function Me(d,g){const u=arguments.length>=2;return m=>m.pipe(d?(0,Z.p)((w,N)=>d(w,N,m)):ae.D,Ln(1),u?Y(g):le(()=>new Vt))}var Ge=O(274),dt=O(8141),zt=O(9437);function qt(d){return d<=0?()=>be.w:(0,jt.N)((g,u)=>{let m=[];g.subscribe((0,De._)(u,w=>{m.push(w),d<m.length&&m.shift()},()=>{for(const w of m)u.next(w);u.complete()},void 0,()=>{m=null}))})}var yn=O(980),Gt=O(6977),qe=O(345);const Xe="primary",Vn=Symbol("RouteTitle");class Ur{constructor(g){this.params=g||{}}has(g){return Object.prototype.hasOwnProperty.call(this.params,g)}get(g){if(this.has(g)){const u=this.params[g];return Array.isArray(u)?u[0]:u}return null}getAll(g){if(this.has(g)){const u=this.params[g];return Array.isArray(u)?u:[u]}return[]}get keys(){return Object.keys(this.params)}}function Nt(d){return new Ur(d)}function fr(d,g,u){const m=u.path.split("/");if(m.length>d.length||"full"===u.pathMatch&&(g.hasChildren()||m.length<d.length))return null;const w={};for(let N=0;N<m.length;N++){const F=m[N],re=d[N];if(F.startsWith(":"))w[F.substring(1)]=re;else if(F!==re.path)return null}return{consumed:d.slice(0,m.length),posParams:w}}function st(d,g){const u=d?Object.keys(d):void 0,m=g?Object.keys(g):void 0;if(!u||!m||u.length!=m.length)return!1;let w;for(let N=0;N<u.length;N++)if(w=u[N],!wr(d[w],g[w]))return!1;return!0}function wr(d,g){if(Array.isArray(d)&&Array.isArray(g)){if(d.length!==g.length)return!1;const u=[...d].sort(),m=[...g].sort();return u.every((w,N)=>m[N]===w)}return d===g}function zn(d){return d.length>0?d[d.length-1]:null}function vn(d){return function se(d){return!!d&&(d instanceof C.c||(0,ue.T)(d.lift)&&(0,ue.T)(d.subscribe))}(d)?d:(0,c.jNT)(d)?(0,Q.H)(Promise.resolve(d)):(0,oe.of)(d)}const rn={exact:function xi(d,g,u){if(!hr(d.segments,g.segments)||!Sn(d.segments,g.segments,u)||d.numberOfChildren!==g.numberOfChildren)return!1;for(const m in g.children)if(!d.children[m]||!xi(d.children[m],g.children[m],u))return!1;return!0},subset:Br},pn={exact:function Yt(d,g){return st(d,g)},subset:function ze(d,g){return Object.keys(g).length<=Object.keys(d).length&&Object.keys(g).every(u=>wr(d[u],g[u]))},ignored:()=>!0};function ui(d,g,u){return rn[u.paths](d.root,g.root,u.matrixParams)&&pn[u.queryParams](d.queryParams,g.queryParams)&&!("exact"===u.fragment&&d.fragment!==g.fragment)}function Br(d,g,u){return $r(d,g,g.segments,u)}function $r(d,g,u,m){if(d.segments.length>u.length){const w=d.segments.slice(0,u.length);return!(!hr(w,u)||g.hasChildren()||!Sn(w,u,m))}if(d.segments.length===u.length){if(!hr(d.segments,u)||!Sn(d.segments,u,m))return!1;for(const w in g.children)if(!d.children[w]||!Br(d.children[w],g.children[w],m))return!1;return!0}{const w=u.slice(0,d.segments.length),N=u.slice(d.segments.length);return!!(hr(d.segments,w)&&Sn(d.segments,w,m)&&d.children[Xe])&&$r(d.children[Xe],g,N,m)}}function Sn(d,g,u){return g.every((m,w)=>pn[u](d[w].parameters,m.parameters))}class ir{constructor(g=new _t([],{}),u={},m=null){this.root=g,this.queryParams=u,this.fragment=m}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=Nt(this.queryParams)),this._queryParamMap}toString(){return Hr.serialize(this)}}class _t{constructor(g,u){this.segments=g,this.children=u,this.parent=null,Object.values(u).forEach(m=>m.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return or(this)}}class At{constructor(g,u){this.path=g,this.parameters=u}get parameterMap(){return this._parameterMap||(this._parameterMap=Nt(this.parameters)),this._parameterMap}toString(){return Et(this)}}function hr(d,g){return d.length===g.length&&d.every((u,m)=>u.path===g[m].path)}let er=(()=>{class d{static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275prov=c.jDH({token:d,factory:function(){return new Gn},providedIn:"root"})}}return d})();class Gn{parse(g){const u=new Ne(g);return new ir(u.parseRootSegment(),u.parseQueryParams(),u.parseFragment())}serialize(g){const u=`/${ce(g.root,!0)}`,m=function en(d){const g=Object.keys(d).map(u=>{const m=d[u];return Array.isArray(m)?m.map(w=>`${V(u)}=${V(w)}`).join("&"):`${V(u)}=${V(m)}`}).filter(u=>!!u);return g.length?`?${g.join("&")}`:""}(g.queryParams);return`${u}${m}${"string"==typeof g.fragment?`#${function W(d){return encodeURI(d)}(g.fragment)}`:""}`}}const Hr=new Gn;function or(d){return d.segments.map(g=>Et(g)).join("/")}function ce(d,g){if(!d.hasChildren())return or(d);if(g){const u=d.children[Xe]?ce(d.children[Xe],!1):"",m=[];return Object.entries(d.children).forEach(([w,N])=>{w!==Xe&&m.push(`${w}:${ce(N,!1)}`)}),m.length>0?`${u}(${m.join("//")})`:u}{const u=function vt(d,g){let u=[];return Object.entries(d.children).forEach(([m,w])=>{m===Xe&&(u=u.concat(g(w,m)))}),Object.entries(d.children).forEach(([m,w])=>{m!==Xe&&(u=u.concat(g(w,m)))}),u}(d,(m,w)=>w===Xe?[ce(d.children[Xe],!1)]:[`${w}:${ce(m,!1)}`]);return 1===Object.keys(d.children).length&&null!=d.children[Xe]?`${or(d)}/${u[0]}`:`${or(d)}/(${u.join("//")})`}}function q(d){return encodeURIComponent(d).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function V(d){return q(d).replace(/%3B/gi,";")}function ye(d){return q(d).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function xe(d){return decodeURIComponent(d)}function Ye(d){return xe(d.replace(/\+/g,"%20"))}function Et(d){return`${ye(d.path)}${function Ft(d){return Object.keys(d).map(g=>`;${ye(g)}=${ye(d[g])}`).join("")}(d.parameters)}`}const Wn=/^[^\/()?;#]+/;function pr(d){const g=d.match(Wn);return g?g[0]:""}const X=/^[^\/()?;=#]+/,$=/^[^=?&#]+/,Ie=/^[^&#]+/;class Ne{constructor(g){this.url=g,this.remaining=g}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new _t([],{}):new _t([],this.parseChildren())}parseQueryParams(){const g={};if(this.consumeOptional("?"))do{this.parseQueryParam(g)}while(this.consumeOptional("&"));return g}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const g=[];for(this.peekStartsWith("(")||g.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),g.push(this.parseSegment());let u={};this.peekStartsWith("/(")&&(this.capture("/"),u=this.parseParens(!0));let m={};return this.peekStartsWith("(")&&(m=this.parseParens(!1)),(g.length>0||Object.keys(u).length>0)&&(m[Xe]=new _t(g,u)),m}parseSegment(){const g=pr(this.remaining);if(""===g&&this.peekStartsWith(";"))throw new c.wOt(4009,!1);return this.capture(g),new At(xe(g),this.parseMatrixParams())}parseMatrixParams(){const g={};for(;this.consumeOptional(";");)this.parseParam(g);return g}parseParam(g){const u=function j(d){const g=d.match(X);return g?g[0]:""}(this.remaining);if(!u)return;this.capture(u);let m="";if(this.consumeOptional("=")){const w=pr(this.remaining);w&&(m=w,this.capture(m))}g[xe(u)]=xe(m)}parseQueryParam(g){const u=function he(d){const g=d.match($);return g?g[0]:""}(this.remaining);if(!u)return;this.capture(u);let m="";if(this.consumeOptional("=")){const F=function Ue(d){const g=d.match(Ie);return g?g[0]:""}(this.remaining);F&&(m=F,this.capture(m))}const w=Ye(u),N=Ye(m);if(g.hasOwnProperty(w)){let F=g[w];Array.isArray(F)||(F=[F],g[w]=F),F.push(N)}else g[w]=N}parseParens(g){const u={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const m=pr(this.remaining),w=this.remaining[m.length];if("/"!==w&&")"!==w&&";"!==w)throw new c.wOt(4010,!1);let N;m.indexOf(":")>-1?(N=m.slice(0,m.indexOf(":")),this.capture(N),this.capture(":")):g&&(N=Xe);const F=this.parseChildren();u[N]=1===Object.keys(F).length?F[Xe]:new _t([],F),this.consumeOptional("//")}return u}peekStartsWith(g){return this.remaining.startsWith(g)}consumeOptional(g){return!!this.peekStartsWith(g)&&(this.remaining=this.remaining.substring(g.length),!0)}capture(g){if(!this.consumeOptional(g))throw new c.wOt(4011,!1)}}function $t(d){return d.segments.length>0?new _t([],{[Xe]:d}):d}function ke(d){const g={};for(const m of Object.keys(d.children)){const N=ke(d.children[m]);if(m===Xe&&0===N.segments.length&&N.hasChildren())for(const[F,re]of Object.entries(N.children))g[F]=re;else(N.segments.length>0||N.hasChildren())&&(g[m]=N)}return function dn(d){if(1===d.numberOfChildren&&d.children[Xe]){const g=d.children[Xe];return new _t(d.segments.concat(g.segments),g.children)}return d}(new _t(d.segments,g))}function Nn(d){return d instanceof ir}function tr(d){let g;const w=$t(function u(N){const F={};for(const ee of N.children){const He=u(ee);F[ee.outlet]=He}const re=new _t(N.url,F);return N===d&&(g=re),re}(d.root));return g??w}function Yr(d,g,u,m){let w=d;for(;w.parent;)w=w.parent;if(0===g.length)return Ut(w,w,w,u,m);const N=function gt(d){if("string"==typeof d[0]&&1===d.length&&"/"===d[0])return new Kn(!0,0,d);let g=0,u=!1;const m=d.reduce((w,N,F)=>{if("object"==typeof N&&null!=N){if(N.outlets){const re={};return Object.entries(N.outlets).forEach(([ee,He])=>{re[ee]="string"==typeof He?He.split("/"):He}),[...w,{outlets:re}]}if(N.segmentPath)return[...w,N.segmentPath]}return"string"!=typeof N?[...w,N]:0===F?(N.split("/").forEach((re,ee)=>{0==ee&&"."===re||(0==ee&&""===re?u=!0:".."===re?g++:""!=re&&w.push(re))}),w):[...w,N]},[]);return new Kn(u,g,m)}(g);if(N.toRoot())return Ut(w,w,new _t([],{}),u,m);const F=function ci(d,g,u){if(d.isAbsolute)return new Xn(g,!0,0);if(!u)return new Xn(g,!1,NaN);if(null===u.parent)return new Xn(u,!0,0);const m=sr(d.commands[0])?0:1;return function di(d,g,u){let m=d,w=g,N=u;for(;N>w;){if(N-=w,m=m.parent,!m)throw new c.wOt(4005,!1);w=m.segments.length}return new Xn(m,!1,w-N)}(u,u.segments.length-1+m,d.numberOfDoubleDots)}(N,w,d),re=F.processChildren?Nr(F.segmentGroup,F.index,N.commands):gr(F.segmentGroup,F.index,N.commands);return Ut(w,F.segmentGroup,re,u,m)}function sr(d){return"object"==typeof d&&null!=d&&!d.outlets&&!d.segmentPath}function ct(d){return"object"==typeof d&&null!=d&&d.outlets}function Ut(d,g,u,m,w){let F,N={};m&&Object.entries(m).forEach(([ee,He])=>{N[ee]=Array.isArray(He)?He.map(bt=>`${bt}`):`${He}`}),F=d===g?u:In(d,g,u);const re=$t(ke(F));return new ir(re,N,w)}function In(d,g,u){const m={};return Object.entries(d.children).forEach(([w,N])=>{m[w]=N===g?u:In(N,g,u)}),new _t(d.segments,m)}class Kn{constructor(g,u,m){if(this.isAbsolute=g,this.numberOfDoubleDots=u,this.commands=m,g&&m.length>0&&sr(m[0]))throw new c.wOt(4003,!1);const w=m.find(ct);if(w&&w!==zn(m))throw new c.wOt(4004,!1)}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}class Xn{constructor(g,u,m){this.segmentGroup=g,this.processChildren=u,this.index=m}}function gr(d,g,u){if(d||(d=new _t([],{})),0===d.segments.length&&d.hasChildren())return Nr(d,g,u);const m=function Fi(d,g,u){let m=0,w=g;const N={match:!1,pathIndex:0,commandIndex:0};for(;w<d.segments.length;){if(m>=u.length)return N;const F=d.segments[w],re=u[m];if(ct(re))break;const ee=`${re}`,He=m<u.length-1?u[m+1]:null;if(w>0&&void 0===ee)break;if(ee&&He&&"object"==typeof He&&void 0===He.outlets){if(!ki(ee,He,F))return N;m+=2}else{if(!ki(ee,{},F))return N;m++}w++}return{match:!0,pathIndex:w,commandIndex:m}}(d,g,u),w=u.slice(m.commandIndex);if(m.match&&m.pathIndex<d.segments.length){const N=new _t(d.segments.slice(0,m.pathIndex),{});return N.children[Xe]=new _t(d.segments.slice(m.pathIndex),d.children),Nr(N,0,w)}return m.match&&0===w.length?new _t(d.segments,{}):m.match&&!d.hasChildren()?nr(d,g,u):m.match?Nr(d,0,w):nr(d,g,u)}function Nr(d,g,u){if(0===u.length)return new _t(d.segments,{});{const m=function Or(d){return ct(d[0])?d[0].outlets:{[Xe]:d}}(u),w={};if(Object.keys(m).some(N=>N!==Xe)&&d.children[Xe]&&1===d.numberOfChildren&&0===d.children[Xe].segments.length){const N=Nr(d.children[Xe],g,u);return new _t(d.segments,N.children)}return Object.entries(m).forEach(([N,F])=>{"string"==typeof F&&(F=[F]),null!==F&&(w[N]=gr(d.children[N],g,F))}),Object.entries(d.children).forEach(([N,F])=>{void 0===m[N]&&(w[N]=F)}),new _t(d.segments,w)}}function nr(d,g,u){const m=d.segments.slice(0,g);let w=0;for(;w<u.length;){const N=u[w];if(ct(N)){const ee=zr(N.outlets);return new _t(m,ee)}if(0===w&&sr(u[0])){m.push(new At(d.segments[g].path,wi(u[0]))),w++;continue}const F=ct(N)?N.outlets[Xe]:`${N}`,re=w<u.length-1?u[w+1]:null;F&&re&&sr(re)?(m.push(new At(F,wi(re))),w+=2):(m.push(new At(F,{})),w++)}return new _t(m,{})}function zr(d){const g={};return Object.entries(d).forEach(([u,m])=>{"string"==typeof m&&(m=[m]),null!==m&&(g[u]=nr(new _t([],{}),0,m))}),g}function wi(d){const g={};return Object.entries(d).forEach(([u,m])=>g[u]=`${m}`),g}function ki(d,g,u){return d==u.path&&st(g,u.parameters)}const Qn="imperative";class mr{constructor(g,u){this.id=g,this.url=u}}class bi extends mr{constructor(g,u,m="imperative",w=null){super(g,u),this.type=0,this.navigationTrigger=m,this.restoredState=w}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class Pr extends mr{constructor(g,u,m){super(g,u),this.urlAfterRedirects=m,this.type=1}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}class Li extends mr{constructor(g,u,m,w){super(g,u),this.reason=m,this.code=w,this.type=2}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class jn extends mr{constructor(g,u,m,w){super(g,u),this.reason=m,this.code=w,this.type=16}}class Yi extends mr{constructor(g,u,m,w){super(g,u),this.error=m,this.target=w,this.type=3}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class yr extends mr{constructor(g,u,m,w){super(g,u),this.urlAfterRedirects=m,this.state=w,this.type=4}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class No extends mr{constructor(g,u,m,w){super(g,u),this.urlAfterRedirects=m,this.state=w,this.type=7}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Rr extends mr{constructor(g,u,m,w,N){super(g,u),this.urlAfterRedirects=m,this.state=w,this.shouldActivate=N,this.type=8}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class eo extends mr{constructor(g,u,m,w){super(g,u),this.urlAfterRedirects=m,this.state=w,this.type=5}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Vi extends mr{constructor(g,u,m,w){super(g,u),this.urlAfterRedirects=m,this.state=w,this.type=6}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class fi{constructor(g){this.route=g,this.type=9}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class to{constructor(g){this.route=g,this.type=10}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class po{constructor(g){this.snapshot=g,this.type=11}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class no{constructor(g){this.snapshot=g,this.type=12}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Po{constructor(g){this.snapshot=g,this.type=13}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Ho{constructor(g){this.snapshot=g,this.type=14}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class ei{constructor(g,u,m){this.routerEvent=g,this.position=u,this.anchor=m,this.type=15}toString(){return`Scroll(anchor: '${this.anchor}', position: '${this.position?`${this.position[0]}, ${this.position[1]}`:null}')`}}class ro{}class Gr{constructor(g){this.url=g}}class ti{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new ni,this.attachRef=null}}let ni=(()=>{class d{constructor(){this.contexts=new Map}onChildOutletCreated(u,m){const w=this.getOrCreateContext(u);w.outlet=m,this.contexts.set(u,w)}onChildOutletDestroyed(u){const m=this.getContext(u);m&&(m.outlet=null,m.attachRef=null)}onOutletDeactivated(){const u=this.contexts;return this.contexts=new Map,u}onOutletReAttached(u){this.contexts=u}getOrCreateContext(u){let m=this.getContext(u);return m||(m=new ti,this.contexts.set(u,m)),m}getContext(u){return this.contexts.get(u)||null}static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275prov=c.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})}}return d})();class ji{constructor(g){this._root=g}get root(){return this._root.value}parent(g){const u=this.pathFromRoot(g);return u.length>1?u[u.length-2]:null}children(g){const u=hi(g,this._root);return u?u.children.map(m=>m.value):[]}firstChild(g){const u=hi(g,this._root);return u&&u.children.length>0?u.children[0].value:null}siblings(g){const u=io(g,this._root);return u.length<2?[]:u[u.length-2].children.map(w=>w.value).filter(w=>w!==g)}pathFromRoot(g){return io(g,this._root).map(u=>u.value)}}function hi(d,g){if(d===g.value)return g;for(const u of g.children){const m=hi(d,u);if(m)return m}return null}function io(d,g){if(d===g.value)return[g];for(const u of g.children){const m=io(d,u);if(m.length)return m.unshift(g),m}return[]}class pt{constructor(g,u){this.value=g,this.children=u}toString(){return`TreeNode(${this.value})`}}function Cn(d){const g={};return d&&d.children.forEach(u=>g[u.value.outlet]=u),g}class wn extends ji{constructor(g,u){super(g),this.snapshot=u,an(this,g)}toString(){return this.snapshot.toString()}}function xr(d,g){const u=function Pn(d,g){const F=new Bi([],{},{},"",{},Xe,g,null,{});return new oo("",new pt(F,[]))}(0,g),m=new ne.t([new At("",{})]),w=new ne.t({}),N=new ne.t({}),F=new ne.t({}),re=new ne.t(""),ee=new br(m,w,F,re,N,Xe,g,u.root);return ee.snapshot=u.root,new wn(new pt(ee,[]),u)}class br{constructor(g,u,m,w,N,F,re,ee){this.urlSubject=g,this.paramsSubject=u,this.queryParamsSubject=m,this.fragmentSubject=w,this.dataSubject=N,this.outlet=F,this.component=re,this._futureSnapshot=ee,this.title=this.dataSubject?.pipe((0,je.T)(He=>He[Vn]))??(0,oe.of)(void 0),this.url=g,this.params=u,this.queryParams=m,this.fragment=w,this.data=N}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=this.params.pipe((0,je.T)(g=>Nt(g)))),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=this.queryParams.pipe((0,je.T)(g=>Nt(g)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function Ui(d,g="emptyOnly"){const u=d.pathFromRoot;let m=0;if("always"!==g)for(m=u.length-1;m>=1;){const w=u[m],N=u[m-1];if(w.routeConfig&&""===w.routeConfig.path)m--;else{if(N.component)break;m--}}return function pi(d){return d.reduce((g,u)=>({params:{...g.params,...u.params},data:{...g.data,...u.data},resolve:{...u.data,...g.resolve,...u.routeConfig?.data,...u._resolvedData}}),{params:{},data:{},resolve:{}})}(u.slice(m))}class Bi{get title(){return this.data?.[Vn]}constructor(g,u,m,w,N,F,re,ee,He){this.url=g,this.params=u,this.queryParams=m,this.fragment=w,this.data=N,this.outlet=F,this.component=re,this.routeConfig=ee,this._resolve=He}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=Nt(this.params)),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=Nt(this.queryParams)),this._queryParamMap}toString(){return`Route(url:'${this.url.map(m=>m.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class oo extends ji{constructor(g,u){super(u),this.url=g,an(this,u)}toString(){return Le(this._root)}}function an(d,g){g.value._routerState=d,g.children.forEach(u=>an(d,u))}function Le(d){const g=d.children.length>0?` { ${d.children.map(Le).join(", ")} } `:"";return`${d.value}${g}`}function mt(d){if(d.snapshot){const g=d.snapshot,u=d._futureSnapshot;d.snapshot=u,st(g.queryParams,u.queryParams)||d.queryParamsSubject.next(u.queryParams),g.fragment!==u.fragment&&d.fragmentSubject.next(u.fragment),st(g.params,u.params)||d.paramsSubject.next(u.params),function ht(d,g){if(d.length!==g.length)return!1;for(let u=0;u<d.length;++u)if(!st(d[u],g[u]))return!1;return!0}(g.url,u.url)||d.urlSubject.next(u.url),st(g.data,u.data)||d.dataSubject.next(u.data)}else d.snapshot=d._futureSnapshot,d.dataSubject.next(d._futureSnapshot.data)}function on(d,g){const u=st(d.params,g.params)&&function Ji(d,g){return hr(d,g)&&d.every((u,m)=>st(u.parameters,g[m].parameters))}(d.url,g.url);return u&&!(!d.parent!=!g.parent)&&(!d.parent||on(d.parent,g.parent))}let qn=(()=>{class d{constructor(){this.activated=null,this._activatedRoute=null,this.name=Xe,this.activateEvents=new c.bkB,this.deactivateEvents=new c.bkB,this.attachEvents=new c.bkB,this.detachEvents=new c.bkB,this.parentContexts=(0,c.WQX)(ni),this.location=(0,c.WQX)(c.c1b),this.changeDetector=(0,c.WQX)(c.gRc),this.environmentInjector=(0,c.WQX)(c.uvJ),this.inputBinder=(0,c.WQX)(Tn,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(u){if(u.name){const{firstChange:m,previousValue:w}=u.name;if(m)return;this.isTrackedInParentContexts(w)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(w)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(u){return this.parentContexts.getContext(u)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;const u=this.parentContexts.getContext(this.name);u?.route&&(u.attachRef?this.attach(u.attachRef,u.route):this.activateWith(u.route,u.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new c.wOt(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new c.wOt(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new c.wOt(4012,!1);this.location.detach();const u=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(u.instance),u}attach(u,m){this.activated=u,this._activatedRoute=m,this.location.insert(u.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(u.instance)}deactivate(){if(this.activated){const u=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(u)}}activateWith(u,m){if(this.isActivated)throw new c.wOt(4013,!1);this._activatedRoute=u;const w=this.location,F=u.snapshot.component,re=this.parentContexts.getOrCreateContext(this.name).children,ee=new $i(u,re,w.injector);this.activated=w.createComponent(F,{index:w.length,injector:ee,environmentInjector:m??this.environmentInjector}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275dir=c.FsC({type:d,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[c.OA$]})}}return d})();class $i{constructor(g,u,m){this.route=g,this.childContexts=u,this.parent=m}get(g,u){return g===br?this.route:g===ni?this.childContexts:this.parent.get(g,u)}}const Tn=new c.nKC("");let Mr=(()=>{class d{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(u){this.unsubscribeFromRouteData(u),this.subscribeToRouteData(u)}unsubscribeFromRouteData(u){this.outletDataSubscriptions.get(u)?.unsubscribe(),this.outletDataSubscriptions.delete(u)}subscribeToRouteData(u){const{activatedRoute:m}=u,w=Fe([m.queryParams,m.params,m.data]).pipe((0,ft.n)(([N,F,re],ee)=>(re={...N,...F,...re},0===ee?(0,oe.of)(re):Promise.resolve(re)))).subscribe(N=>{if(!u.isActivated||!u.activatedComponentRef||u.activatedRoute!==m||null===m.component)return void this.unsubscribeFromRouteData(u);const F=(0,c.HJs)(m.component);if(F)for(const{templateName:re}of F.inputs)u.activatedComponentRef.setInput(re,N[re]);else this.unsubscribeFromRouteData(u)});this.outletDataSubscriptions.set(u,w)}static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275prov=c.jDH({token:d,factory:d.\u0275fac})}}return d})();function Zn(d,g,u){if(u&&d.shouldReuseRoute(g.value,u.value.snapshot)){const m=u.value;m._futureSnapshot=g.value;const w=function ri(d,g,u){return g.children.map(m=>{for(const w of u.children)if(d.shouldReuseRoute(m.value,w.value.snapshot))return Zn(d,m,w);return Zn(d,m)})}(d,g,u);return new pt(m,w)}{if(d.shouldAttach(g.value)){const N=d.retrieve(g.value);if(null!==N){const F=N.route;return F.value._futureSnapshot=g.value,F.children=g.children.map(re=>Zn(d,re)),F}}const m=function ut(d){return new br(new ne.t(d.url),new ne.t(d.params),new ne.t(d.queryParams),new ne.t(d.fragment),new ne.t(d.data),d.outlet,d.component,d)}(g.value),w=g.children.map(N=>Zn(d,N));return new pt(m,w)}}const Fr="ngNavigationCancelingError";function Hi(d,g){const{redirectTo:u,navigationBehaviorOptions:m}=Nn(g)?{redirectTo:g,navigationBehaviorOptions:void 0}:g,w=Wr(!1,0,g);return w.url=u,w.navigationBehaviorOptions=m,w}function Wr(d,g,u){const m=new Error("NavigationCancelingError: "+(d||""));return m[Fr]=!0,m.cancellationCode=g,u&&(m.url=u),m}function xn(d){return d&&d[Fr]}let ar=(()=>{class d{static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275cmp=c.VBU({type:d,selectors:[["ng-component"]],standalone:!0,features:[c.aNF],decls:1,vars:0,template:function(m,w){1&m&&c.nrm(0,"router-outlet")},dependencies:[qn],encapsulation:2})}}return d})();function mi(d){const g=d.children&&d.children.map(mi),u=g?{...d,children:g}:{...d};return!u.component&&!u.loadComponent&&(g||u.loadChildren)&&u.outlet&&u.outlet!==Xe&&(u.component=ar),u}function vr(d){return d.outlet||Xe}function An(d){if(!d)return null;if(d.routeConfig?._injector)return d.routeConfig._injector;for(let g=d.parent;g;g=g.parent){const u=g.routeConfig;if(u?._loadedInjector)return u._loadedInjector;if(u?._injector)return u._injector}return null}class Un{constructor(g,u,m,w,N){this.routeReuseStrategy=g,this.futureState=u,this.currState=m,this.forwardEvent=w,this.inputBindingEnabled=N}activate(g){const u=this.futureState._root,m=this.currState?this.currState._root:null;this.deactivateChildRoutes(u,m,g),mt(this.futureState.root),this.activateChildRoutes(u,m,g)}deactivateChildRoutes(g,u,m){const w=Cn(u);g.children.forEach(N=>{const F=N.value.outlet;this.deactivateRoutes(N,w[F],m),delete w[F]}),Object.values(w).forEach(N=>{this.deactivateRouteAndItsChildren(N,m)})}deactivateRoutes(g,u,m){const w=g.value,N=u?u.value:null;if(w===N)if(w.component){const F=m.getContext(w.outlet);F&&this.deactivateChildRoutes(g,u,F.children)}else this.deactivateChildRoutes(g,u,m);else N&&this.deactivateRouteAndItsChildren(u,m)}deactivateRouteAndItsChildren(g,u){g.value.component&&this.routeReuseStrategy.shouldDetach(g.value.snapshot)?this.detachAndStoreRouteSubtree(g,u):this.deactivateRouteAndOutlet(g,u)}detachAndStoreRouteSubtree(g,u){const m=u.getContext(g.value.outlet),w=m&&g.value.component?m.children:u,N=Cn(g);for(const F of Object.keys(N))this.deactivateRouteAndItsChildren(N[F],w);if(m&&m.outlet){const F=m.outlet.detach(),re=m.children.onOutletDeactivated();this.routeReuseStrategy.store(g.value.snapshot,{componentRef:F,route:g,contexts:re})}}deactivateRouteAndOutlet(g,u){const m=u.getContext(g.value.outlet),w=m&&g.value.component?m.children:u,N=Cn(g);for(const F of Object.keys(N))this.deactivateRouteAndItsChildren(N[F],w);m&&(m.outlet&&(m.outlet.deactivate(),m.children.onOutletDeactivated()),m.attachRef=null,m.route=null)}activateChildRoutes(g,u,m){const w=Cn(u);g.children.forEach(N=>{this.activateRoutes(N,w[N.value.outlet],m),this.forwardEvent(new Ho(N.value.snapshot))}),g.children.length&&this.forwardEvent(new no(g.value.snapshot))}activateRoutes(g,u,m){const w=g.value,N=u?u.value:null;if(mt(w),w===N)if(w.component){const F=m.getOrCreateContext(w.outlet);this.activateChildRoutes(g,u,F.children)}else this.activateChildRoutes(g,u,m);else if(w.component){const F=m.getOrCreateContext(w.outlet);if(this.routeReuseStrategy.shouldAttach(w.snapshot)){const re=this.routeReuseStrategy.retrieve(w.snapshot);this.routeReuseStrategy.store(w.snapshot,null),F.children.onOutletReAttached(re.contexts),F.attachRef=re.componentRef,F.route=re.route.value,F.outlet&&F.outlet.attach(re.componentRef,re.route.value),mt(re.route.value),this.activateChildRoutes(g,null,F.children)}else{const re=An(w.snapshot);F.attachRef=null,F.route=w,F.injector=re,F.outlet&&F.outlet.activateWith(w,F.injector),this.activateChildRoutes(g,null,F.children)}}else this.activateChildRoutes(g,null,m)}}class xo{constructor(g){this.path=g,this.route=this.path[this.path.length-1]}}class On{constructor(g,u){this.component=g,this.route=u}}function kn(d,g,u){const m=d._root;return Wt(m,g?g._root:null,u,[m.value])}function Ir(d,g){const u=Symbol(),m=g.get(d,u);return m===u?"function"!=typeof d||(0,c.LfX)(d)?g.get(d):d:m}function Wt(d,g,u,m,w={canDeactivateChecks:[],canActivateChecks:[]}){const N=Cn(g);return d.children.forEach(F=>{(function lr(d,g,u,m,w={canDeactivateChecks:[],canActivateChecks:[]}){const N=d.value,F=g?g.value:null,re=u?u.getContext(d.value.outlet):null;if(F&&N.routeConfig===F.routeConfig){const ee=function mo(d,g,u){if("function"==typeof u)return u(d,g);switch(u){case"pathParamsChange":return!hr(d.url,g.url);case"pathParamsOrQueryParamsChange":return!hr(d.url,g.url)||!st(d.queryParams,g.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!on(d,g)||!st(d.queryParams,g.queryParams);default:return!on(d,g)}}(F,N,N.routeConfig.runGuardsAndResolvers);ee?w.canActivateChecks.push(new xo(m)):(N.data=F.data,N._resolvedData=F._resolvedData),Wt(d,g,N.component?re?re.children:null:u,m,w),ee&&re&&re.outlet&&re.outlet.isActivated&&w.canDeactivateChecks.push(new On(re.outlet.component,F))}else F&&b(g,re,w),w.canActivateChecks.push(new xo(m)),Wt(d,null,N.component?re?re.children:null:u,m,w)})(F,N[F.value.outlet],u,m.concat([F.value]),w),delete N[F.value.outlet]}),Object.entries(N).forEach(([F,re])=>b(re,u.getContext(F),w)),w}function b(d,g,u){const m=Cn(d),w=d.value;Object.entries(m).forEach(([N,F])=>{b(F,w.component?g?g.children.getContext(N):null:g,u)}),u.canDeactivateChecks.push(new On(w.component&&g&&g.outlet&&g.outlet.isActivated?g.outlet.component:null,w))}function S(d){return"function"==typeof d}function Ti(d){return d instanceof Vt||"EmptyError"===d?.name}const Ai=Symbol("INITIAL_VALUE");function ii(){return(0,ft.n)(d=>Fe(d.map(g=>g.pipe(Ln(1),function jr(...d){const g=(0,z.lI)(d);return(0,jt.N)((u,m)=>{(g?Ae(d,u,g):Ae(d,u)).subscribe(m)})}(Ai)))).pipe((0,je.T)(g=>{for(const u of g)if(!0!==u){if(u===Ai)return Ai;if(!1===u||u instanceof ir)return u}return!0}),(0,Z.p)(g=>g!==Ai),Ln(1)))}function Wi(d){return(0,Pe.F)((0,dt.M)(g=>{if(Nn(g))throw Hi(0,g)}),(0,je.T)(g=>!0===g))}class ko{constructor(g){this.segmentGroup=g||null}}class ss{constructor(g){this.urlTree=g}}function Zt(d){return(0,Oe.$)(new ko(d))}function Xr(d){return(0,Oe.$)(new ss(d))}class Go{constructor(g,u){this.urlSerializer=g,this.urlTree=u}noMatchError(g){return new c.wOt(4002,!1)}lineralizeSegments(g,u){let m=[],w=u.root;for(;;){if(m=m.concat(w.segments),0===w.numberOfChildren)return(0,oe.of)(m);if(w.numberOfChildren>1||!w.children[Xe])return(0,Oe.$)(new c.wOt(4e3,!1));w=w.children[Xe]}}applyRedirectCommands(g,u,m){return this.applyRedirectCreateUrlTree(u,this.urlSerializer.parse(u),g,m)}applyRedirectCreateUrlTree(g,u,m,w){const N=this.createSegmentGroup(g,u.root,m,w);return new ir(N,this.createQueryParams(u.queryParams,this.urlTree.queryParams),u.fragment)}createQueryParams(g,u){const m={};return Object.entries(g).forEach(([w,N])=>{if("string"==typeof N&&N.startsWith(":")){const re=N.substring(1);m[w]=u[re]}else m[w]=N}),m}createSegmentGroup(g,u,m,w){const N=this.createSegments(g,u.segments,m,w);let F={};return Object.entries(u.children).forEach(([re,ee])=>{F[re]=this.createSegmentGroup(g,ee,m,w)}),new _t(N,F)}createSegments(g,u,m,w){return u.map(N=>N.path.startsWith(":")?this.findPosParam(g,N,w):this.findOrReturn(N,m))}findPosParam(g,u,m){const w=m[u.path.substring(1)];if(!w)throw new c.wOt(4001,!1);return w}findOrReturn(g,u){let m=0;for(const w of u){if(w.path===g.path)return u.splice(m),w;m++}return g}}const so={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Wo(d,g,u,m,w){const N=ao(d,g,u);return N.matched?(m=function Fn(d,g){return d.providers&&!d._injector&&(d._injector=(0,c.Ol2)(d.providers,g,`Route: ${d.path}`)),d._injector??g}(g,m),function Ki(d,g,u,m){const w=g.canMatch;if(!w||0===w.length)return(0,oe.of)(!0);const N=w.map(F=>{const re=Ir(F,d);return vn(function Jn(d){return d&&S(d.canMatch)}(re)?re.canMatch(g,u):d.runInContext(()=>re(g,u)))});return(0,oe.of)(N).pipe(ii(),Wi())}(m,g,u).pipe((0,je.T)(F=>!0===F?N:{...so}))):(0,oe.of)(N)}function ao(d,g,u){if(""===g.path)return"full"===g.pathMatch&&(d.hasChildren()||u.length>0)?{...so}:{matched:!0,consumedSegments:[],remainingSegments:u,parameters:{},positionalParamSegments:{}};const w=(g.matcher||fr)(u,d,g);if(!w)return{...so};const N={};Object.entries(w.posParams??{}).forEach(([re,ee])=>{N[re]=ee.path});const F=w.consumed.length>0?{...N,...w.consumed[w.consumed.length-1].parameters}:N;return{matched:!0,consumedSegments:w.consumed,remainingSegments:u.slice(w.consumed.length),parameters:F,positionalParamSegments:w.posParams??{}}}function Ko(d,g,u,m){return u.length>0&&function ls(d,g,u){return u.some(m=>Do(d,g,m)&&vr(m)!==Xe)}(d,u,m)?{segmentGroup:new _t(g,Qo(m,new _t(u,d.children))),slicedSegments:[]}:0===u.length&&function us(d,g,u){return u.some(m=>Do(d,g,m))}(d,u,m)?{segmentGroup:new _t(d.segments,Xo(d,0,u,m,d.children)),slicedSegments:u}:{segmentGroup:new _t(d.segments,d.children),slicedSegments:u}}function Xo(d,g,u,m,w){const N={};for(const F of m)if(Do(d,u,F)&&!w[vr(F)]){const re=new _t([],{});N[vr(F)]=re}return{...w,...N}}function Qo(d,g){const u={};u[Xe]=g;for(const m of d)if(""===m.path&&vr(m)!==Xe){const w=new _t([],{});u[vr(m)]=w}return u}function Do(d,g,u){return(!(d.hasChildren()||g.length>0)||"full"!==u.pathMatch)&&""===u.path}class cs{constructor(g,u,m,w,N,F,re){this.injector=g,this.configLoader=u,this.rootComponentType=m,this.config=w,this.urlTree=N,this.paramsInheritanceStrategy=F,this.urlSerializer=re,this.allowRedirects=!0,this.applyRedirects=new Go(this.urlSerializer,this.urlTree)}noMatchError(g){return new c.wOt(4002,!1)}recognize(){const g=Ko(this.urlTree.root,[],[],this.config).segmentGroup;return this.processSegmentGroup(this.injector,this.config,g,Xe).pipe((0,zt.W)(u=>{if(u instanceof ss)return this.allowRedirects=!1,this.urlTree=u.urlTree,this.match(u.urlTree);throw u instanceof ko?this.noMatchError(u):u}),(0,je.T)(u=>{const m=new Bi([],Object.freeze({}),Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,{},Xe,this.rootComponentType,null,{}),w=new pt(m,u),N=new oo("",w),F=function Dn(d,g,u=null,m=null){return Yr(tr(d),g,u,m)}(m,[],this.urlTree.queryParams,this.urlTree.fragment);return F.queryParams=this.urlTree.queryParams,N.url=this.urlSerializer.serialize(F),this.inheritParamsAndData(N._root),{state:N,tree:F}}))}match(g){return this.processSegmentGroup(this.injector,this.config,g.root,Xe).pipe((0,zt.W)(m=>{throw m instanceof ko?this.noMatchError(m):m}))}inheritParamsAndData(g){const u=g.value,m=Ui(u,this.paramsInheritanceStrategy);u.params=Object.freeze(m.params),u.data=Object.freeze(m.data),g.children.forEach(w=>this.inheritParamsAndData(w))}processSegmentGroup(g,u,m,w){return 0===m.segments.length&&m.hasChildren()?this.processChildren(g,u,m):this.processSegment(g,u,m,m.segments,w,!0)}processChildren(g,u,m){const w=[];for(const N of Object.keys(m.children))"primary"===N?w.unshift(N):w.push(N);return(0,Q.H)(w).pipe((0,Ge.H)(N=>{const F=m.children[N],re=function Sr(d,g){const u=d.filter(m=>vr(m)===g);return u.push(...d.filter(m=>vr(m)!==g)),u}(u,N);return this.processSegmentGroup(g,re,F,N)}),function xt(d,g){return(0,jt.N)(function lt(d,g,u,m,w){return(N,F)=>{let re=u,ee=g,He=0;N.subscribe((0,De._)(F,bt=>{const cn=He++;ee=re?d(ee,bt,cn):(re=!0,bt),m&&F.next(ee)},w&&(()=>{re&&F.next(ee),F.complete()})))}}(d,g,arguments.length>=2,!0))}((N,F)=>(N.push(...F),N)),Y(null),function rr(d,g){const u=arguments.length>=2;return m=>m.pipe(d?(0,Z.p)((w,N)=>d(w,N,m)):ae.D,qt(1),u?Y(g):le(()=>new Vt))}(),(0,te.Z)(N=>{if(null===N)return Zt(m);const F=h(N);return function Vo(d){d.sort((g,u)=>g.value.outlet===Xe?-1:u.value.outlet===Xe?1:g.value.outlet.localeCompare(u.value.outlet))}(F),(0,oe.of)(F)}))}processSegment(g,u,m,w,N,F){return(0,Q.H)(u).pipe((0,Ge.H)(re=>this.processSegmentAgainstRoute(re._injector??g,u,re,m,w,N,F).pipe((0,zt.W)(ee=>{if(ee instanceof ko)return(0,oe.of)(null);throw ee}))),Me(re=>!!re),(0,zt.W)(re=>{if(Ti(re))return function Co(d,g,u){return 0===g.length&&!d.children[u]}(m,w,N)?(0,oe.of)([]):Zt(m);throw re}))}processSegmentAgainstRoute(g,u,m,w,N,F,re){return function Lo(d,g,u,m){return!!(vr(d)===m||m!==Xe&&Do(g,u,d))&&("**"===d.path||ao(g,d,u).matched)}(m,w,N,F)?void 0===m.redirectTo?this.matchSegmentAgainstRoute(g,w,m,N,F,re):re&&this.allowRedirects?this.expandSegmentAgainstRouteUsingRedirect(g,w,u,m,N,F):Zt(w):Zt(w)}expandSegmentAgainstRouteUsingRedirect(g,u,m,w,N,F){return"**"===w.path?this.expandWildCardWithParamsAgainstRouteUsingRedirect(g,m,w,F):this.expandRegularSegmentAgainstRouteUsingRedirect(g,u,m,w,N,F)}expandWildCardWithParamsAgainstRouteUsingRedirect(g,u,m,w){const N=this.applyRedirects.applyRedirectCommands([],m.redirectTo,{});return m.redirectTo.startsWith("/")?Xr(N):this.applyRedirects.lineralizeSegments(m,N).pipe((0,te.Z)(F=>{const re=new _t(F,{});return this.processSegment(g,u,re,F,w,!1)}))}expandRegularSegmentAgainstRouteUsingRedirect(g,u,m,w,N,F){const{matched:re,consumedSegments:ee,remainingSegments:He,positionalParamSegments:bt}=ao(u,w,N);if(!re)return Zt(u);const cn=this.applyRedirects.applyRedirectCommands(ee,w.redirectTo,bt);return w.redirectTo.startsWith("/")?Xr(cn):this.applyRedirects.lineralizeSegments(w,cn).pipe((0,te.Z)(Xt=>this.processSegment(g,m,u,Xt.concat(He),F,!1)))}matchSegmentAgainstRoute(g,u,m,w,N,F){let re;if("**"===m.path){const ee=w.length>0?zn(w).parameters:{},He=new Bi(w,ee,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,D(m),vr(m),m.component??m._loadedComponent??null,m,I(m));re=(0,oe.of)({snapshot:He,consumedSegments:[],remainingSegments:[]}),u.children={}}else re=Wo(u,m,w,g).pipe((0,je.T)(({matched:ee,consumedSegments:He,remainingSegments:bt,parameters:cn})=>ee?{snapshot:new Bi(He,cn,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,D(m),vr(m),m.component??m._loadedComponent??null,m,I(m)),consumedSegments:He,remainingSegments:bt}:null));return re.pipe((0,ft.n)(ee=>null===ee?Zt(u):this.getChildConfig(g=m._injector??g,m,w).pipe((0,ft.n)(({routes:He})=>{const bt=m._loadedInjector??g,{snapshot:cn,consumedSegments:Xt,remainingSegments:Qi}=ee,{segmentGroup:Zo,slicedSegments:Jo}=Ko(u,Xt,Qi,He);if(0===Jo.length&&Zo.hasChildren())return this.processChildren(bt,He,Zo).pipe((0,je.T)(Yo=>null===Yo?null:[new pt(cn,Yo)]));if(0===He.length&&0===Jo.length)return(0,oe.of)([new pt(cn,[])]);const gs=vr(m)===N;return this.processSegment(bt,He,Zo,Jo,gs?Xe:N,!0).pipe((0,je.T)(Yo=>[new pt(cn,Yo)]))}))))}getChildConfig(g,u,m){return u.children?(0,oe.of)({routes:u.children,injector:g}):u.loadChildren?void 0!==u._loadedRoutes?(0,oe.of)({routes:u._loadedRoutes,injector:u._loadedInjector}):function Is(d,g,u,m){const w=g.canLoad;if(void 0===w||0===w.length)return(0,oe.of)(!0);const N=w.map(F=>{const re=Ir(F,d);return vn(function P(d){return d&&S(d.canLoad)}(re)?re.canLoad(g,u):d.runInContext(()=>re(g,u)))});return(0,oe.of)(N).pipe(ii(),Wi())}(g,u,m).pipe((0,te.Z)(w=>w?this.configLoader.loadChildren(g,u).pipe((0,dt.M)(N=>{u._loadedRoutes=N.routes,u._loadedInjector=N.injector})):function as(d){return(0,Oe.$)(Wr(!1,3))}())):(0,oe.of)({routes:[],injector:g})}}function va(d){const g=d.value.routeConfig;return g&&""===g.path}function h(d){const g=[],u=new Set;for(const m of d){if(!va(m)){g.push(m);continue}const w=g.find(N=>m.value.routeConfig===N.value.routeConfig);void 0!==w?(w.children.push(...m.children),u.add(w)):g.push(m)}for(const m of u){const w=h(m.children);g.push(new pt(m.value,w))}return g.filter(m=>!u.has(m))}function D(d){return d.data||{}}function I(d){return d.resolve||{}}function Ht(d){return"string"==typeof d.title||null===d.title}function St(d){return(0,ft.n)(g=>{const u=d(g);return u?(0,Q.H)(u).pipe((0,je.T)(()=>g)):(0,oe.of)(g)})}const sn=new c.nKC("ROUTES");let _=(()=>{class d{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=(0,c.WQX)(c.Ql9)}loadComponent(u){if(this.componentLoaders.get(u))return this.componentLoaders.get(u);if(u._loadedComponent)return(0,oe.of)(u._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(u);const m=vn(u.loadComponent()).pipe((0,je.T)(y),(0,dt.M)(N=>{this.onLoadEndListener&&this.onLoadEndListener(u),u._loadedComponent=N}),(0,yn.j)(()=>{this.componentLoaders.delete(u)})),w=new Hn(m,()=>new Re.B).pipe(Mn());return this.componentLoaders.set(u,w),w}loadChildren(u,m){if(this.childrenLoaders.get(m))return this.childrenLoaders.get(m);if(m._loadedRoutes)return(0,oe.of)({routes:m._loadedRoutes,injector:m._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(m);const N=function a(d,g,u,m){return vn(d.loadChildren()).pipe((0,je.T)(y),(0,te.Z)(w=>w instanceof c.Co$||Array.isArray(w)?(0,oe.of)(w):(0,Q.H)(g.compileModuleAsync(w))),(0,je.T)(w=>{m&&m(d);let N,F,re=!1;return Array.isArray(w)?(F=w,!0):(N=w.create(u).injector,F=N.get(sn,[],{optional:!0,self:!0}).flat()),{routes:F.map(mi),injector:N}}))}(m,this.compiler,u,this.onLoadEndListener).pipe((0,yn.j)(()=>{this.childrenLoaders.delete(m)})),F=new Hn(N,()=>new Re.B).pipe(Mn());return this.childrenLoaders.set(m,F),F}static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275prov=c.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})}}return d})();function y(d){return function l(d){return d&&"object"==typeof d&&"default"in d}(d)?d.default:d}let M=(()=>{class d{get hasRequestedNavigation(){return 0!==this.navigationId}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new Re.B,this.transitionAbortSubject=new Re.B,this.configLoader=(0,c.WQX)(_),this.environmentInjector=(0,c.WQX)(c.uvJ),this.urlSerializer=(0,c.WQX)(er),this.rootContexts=(0,c.WQX)(ni),this.inputBindingEnabled=null!==(0,c.WQX)(Tn,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>(0,oe.of)(void 0),this.rootComponentType=null,this.configLoader.onLoadEndListener=w=>this.events.next(new to(w)),this.configLoader.onLoadStartListener=w=>this.events.next(new fi(w))}complete(){this.transitions?.complete()}handleNavigationRequest(u){const m=++this.navigationId;this.transitions?.next({...this.transitions.value,...u,id:m})}setupNavigations(u,m,w){return this.transitions=new ne.t({id:0,currentUrlTree:m,currentRawUrl:m,currentBrowserUrl:m,extractedUrl:u.urlHandlingStrategy.extract(m),urlAfterRedirects:u.urlHandlingStrategy.extract(m),rawUrl:m,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:Qn,restoredState:null,currentSnapshot:w.snapshot,targetSnapshot:null,currentRouterState:w,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe((0,Z.p)(N=>0!==N.id),(0,je.T)(N=>({...N,extractedUrl:u.urlHandlingStrategy.extract(N.rawUrl)})),(0,ft.n)(N=>{this.currentTransition=N;let F=!1,re=!1;return(0,oe.of)(N).pipe((0,dt.M)(ee=>{this.currentNavigation={id:ee.id,initialUrl:ee.rawUrl,extractedUrl:ee.extractedUrl,trigger:ee.source,extras:ee.extras,previousNavigation:this.lastSuccessfulNavigation?{...this.lastSuccessfulNavigation,previousNavigation:null}:null}}),(0,ft.n)(ee=>{const He=ee.currentBrowserUrl.toString(),bt=!u.navigated||ee.extractedUrl.toString()!==He||He!==ee.currentUrlTree.toString();if(!bt&&"reload"!==(ee.extras.onSameUrlNavigation??u.onSameUrlNavigation)){const Xt="";return this.events.next(new jn(ee.id,this.urlSerializer.serialize(ee.rawUrl),Xt,0)),ee.resolve(null),be.w}if(u.urlHandlingStrategy.shouldProcessUrl(ee.rawUrl))return(0,oe.of)(ee).pipe((0,ft.n)(Xt=>{const Qi=this.transitions?.getValue();return this.events.next(new bi(Xt.id,this.urlSerializer.serialize(Xt.extractedUrl),Xt.source,Xt.restoredState)),Qi!==this.transitions?.getValue()?be.w:Promise.resolve(Xt)}),function k(d,g,u,m,w,N){return(0,te.Z)(F=>function lo(d,g,u,m,w,N,F="emptyOnly"){return new cs(d,g,u,m,w,F,N).recognize()}(d,g,u,m,F.extractedUrl,w,N).pipe((0,je.T)(({state:re,tree:ee})=>({...F,targetSnapshot:re,urlAfterRedirects:ee}))))}(this.environmentInjector,this.configLoader,this.rootComponentType,u.config,this.urlSerializer,u.paramsInheritanceStrategy),(0,dt.M)(Xt=>{N.targetSnapshot=Xt.targetSnapshot,N.urlAfterRedirects=Xt.urlAfterRedirects,this.currentNavigation={...this.currentNavigation,finalUrl:Xt.urlAfterRedirects};const Qi=new yr(Xt.id,this.urlSerializer.serialize(Xt.extractedUrl),this.urlSerializer.serialize(Xt.urlAfterRedirects),Xt.targetSnapshot);this.events.next(Qi)}));if(bt&&u.urlHandlingStrategy.shouldProcessUrl(ee.currentRawUrl)){const{id:Xt,extractedUrl:Qi,source:Zo,restoredState:Jo,extras:gs}=ee,Yo=new bi(Xt,this.urlSerializer.serialize(Qi),Zo,Jo);this.events.next(Yo);const Au=xr(0,this.rootComponentType).snapshot;return this.currentTransition=N={...ee,targetSnapshot:Au,urlAfterRedirects:Qi,extras:{...gs,skipLocationChange:!1,replaceUrl:!1}},(0,oe.of)(N)}{const Xt="";return this.events.next(new jn(ee.id,this.urlSerializer.serialize(ee.extractedUrl),Xt,1)),ee.resolve(null),be.w}}),(0,dt.M)(ee=>{const He=new No(ee.id,this.urlSerializer.serialize(ee.extractedUrl),this.urlSerializer.serialize(ee.urlAfterRedirects),ee.targetSnapshot);this.events.next(He)}),(0,je.T)(ee=>(this.currentTransition=N={...ee,guards:kn(ee.targetSnapshot,ee.currentSnapshot,this.rootContexts)},N)),function yo(d,g){return(0,te.Z)(u=>{const{targetSnapshot:m,currentSnapshot:w,guards:{canActivateChecks:N,canDeactivateChecks:F}}=u;return 0===F.length&&0===N.length?(0,oe.of)({...u,guardsResult:!0}):function ma(d,g,u,m){return(0,Q.H)(d).pipe((0,te.Z)(w=>function Ss(d,g,u,m,w){const N=g&&g.routeConfig?g.routeConfig.canDeactivate:null;if(!N||0===N.length)return(0,oe.of)(!0);const F=N.map(re=>{const ee=An(g)??w,He=Ir(re,ee);return vn(function Dt(d){return d&&S(d.canDeactivate)}(He)?He.canDeactivate(d,g,u,m):ee.runInContext(()=>He(d,g,u,m))).pipe(Me())});return(0,oe.of)(F).pipe(ii())}(w.component,w.route,u,g,m)),Me(w=>!0!==w,!0))}(F,m,w,d).pipe((0,te.Z)(re=>re&&function v(d){return"boolean"==typeof d}(re)?function ya(d,g,u,m){return(0,Q.H)(g).pipe((0,Ge.H)(w=>Ae(function vo(d,g){return null!==d&&g&&g(new po(d)),(0,oe.of)(!0)}(w.route.parent,m),function os(d,g){return null!==d&&g&&g(new Po(d)),(0,oe.of)(!0)}(w.route,m),function Ms(d,g,u){const m=g[g.length-1],N=g.slice(0,g.length-1).reverse().map(F=>function Gi(d){const g=d.routeConfig?d.routeConfig.canActivateChild:null;return g&&0!==g.length?{node:d,guards:g}:null}(F)).filter(F=>null!==F).map(F=>tt(()=>{const re=F.guards.map(ee=>{const He=An(F.node)??u,bt=Ir(ee,He);return vn(function We(d){return d&&S(d.canActivateChild)}(bt)?bt.canActivateChild(m,d):He.runInContext(()=>bt(m,d))).pipe(Me())});return(0,oe.of)(re).pipe(ii())}));return(0,oe.of)(N).pipe(ii())}(d,w.path,u),function Fo(d,g,u){const m=g.routeConfig?g.routeConfig.canActivate:null;if(!m||0===m.length)return(0,oe.of)(!0);const w=m.map(N=>tt(()=>{const F=An(g)??u,re=Ir(N,F);return vn(function B(d){return d&&S(d.canActivate)}(re)?re.canActivate(g,d):F.runInContext(()=>re(g,d))).pipe(Me())}));return(0,oe.of)(w).pipe(ii())}(d,w.route,u))),Me(w=>!0!==w,!0))}(m,N,d,g):(0,oe.of)(re)),(0,je.T)(re=>({...u,guardsResult:re})))})}(this.environmentInjector,ee=>this.events.next(ee)),(0,dt.M)(ee=>{if(N.guardsResult=ee.guardsResult,Nn(ee.guardsResult))throw Hi(0,ee.guardsResult);const He=new Rr(ee.id,this.urlSerializer.serialize(ee.extractedUrl),this.urlSerializer.serialize(ee.urlAfterRedirects),ee.targetSnapshot,!!ee.guardsResult);this.events.next(He)}),(0,Z.p)(ee=>!!ee.guardsResult||(this.cancelNavigationTransition(ee,"",3),!1)),St(ee=>{if(ee.guards.canActivateChecks.length)return(0,oe.of)(ee).pipe((0,dt.M)(He=>{const bt=new eo(He.id,this.urlSerializer.serialize(He.extractedUrl),this.urlSerializer.serialize(He.urlAfterRedirects),He.targetSnapshot);this.events.next(bt)}),(0,ft.n)(He=>{let bt=!1;return(0,oe.of)(He).pipe(function ie(d,g){return(0,te.Z)(u=>{const{targetSnapshot:m,guards:{canActivateChecks:w}}=u;if(!w.length)return(0,oe.of)(u);let N=0;return(0,Q.H)(w).pipe((0,Ge.H)(F=>function ve(d,g,u,m){const w=d.routeConfig,N=d._resolve;return void 0!==w?.title&&!Ht(w)&&(N[Vn]=w.title),function Ze(d,g,u,m){const w=function ln(d){return[...Object.keys(d),...Object.getOwnPropertySymbols(d)]}(d);if(0===w.length)return(0,oe.of)({});const N={};return(0,Q.H)(w).pipe((0,te.Z)(F=>function un(d,g,u,m){const w=An(g)??m,N=Ir(d,w);return vn(N.resolve?N.resolve(g,u):w.runInContext(()=>N(g,u)))}(d[F],g,u,m).pipe(Me(),(0,dt.M)(re=>{N[F]=re}))),qt(1),function yt(d){return(0,je.T)(()=>d)}(N),(0,zt.W)(F=>Ti(F)?be.w:(0,Oe.$)(F)))}(N,d,g,m).pipe((0,je.T)(F=>(d._resolvedData=F,d.data=Ui(d,u).resolve,w&&Ht(w)&&(d.data[Vn]=w.title),null)))}(F.route,m,d,g)),(0,dt.M)(()=>N++),qt(1),(0,te.Z)(F=>N===w.length?(0,oe.of)(u):be.w))})}(u.paramsInheritanceStrategy,this.environmentInjector),(0,dt.M)({next:()=>bt=!0,complete:()=>{bt||this.cancelNavigationTransition(He,"",2)}}))}),(0,dt.M)(He=>{const bt=new Vi(He.id,this.urlSerializer.serialize(He.extractedUrl),this.urlSerializer.serialize(He.urlAfterRedirects),He.targetSnapshot);this.events.next(bt)}))}),St(ee=>{const He=bt=>{const cn=[];bt.routeConfig?.loadComponent&&!bt.routeConfig._loadedComponent&&cn.push(this.configLoader.loadComponent(bt.routeConfig).pipe((0,dt.M)(Xt=>{bt.component=Xt}),(0,je.T)(()=>{})));for(const Xt of bt.children)cn.push(...He(Xt));return cn};return Fe(He(ee.targetSnapshot.root)).pipe(Y(),Ln(1))}),St(()=>this.afterPreactivation()),(0,je.T)(ee=>{const He=function gn(d,g,u){const m=Zn(d,g._root,u?u._root:void 0);return new wn(m,g)}(u.routeReuseStrategy,ee.targetSnapshot,ee.currentRouterState);return this.currentTransition=N={...ee,targetRouterState:He},N}),(0,dt.M)(()=>{this.events.next(new ro)}),((d,g,u,m)=>(0,je.T)(w=>(new Un(g,w.targetRouterState,w.currentRouterState,u,m).activate(d),w)))(this.rootContexts,u.routeReuseStrategy,ee=>this.events.next(ee),this.inputBindingEnabled),Ln(1),(0,dt.M)({next:ee=>{F=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Pr(ee.id,this.urlSerializer.serialize(ee.extractedUrl),this.urlSerializer.serialize(ee.urlAfterRedirects))),u.titleStrategy?.updateTitle(ee.targetRouterState.snapshot),ee.resolve(!0)},complete:()=>{F=!0}}),(0,Gt.Q)(this.transitionAbortSubject.pipe((0,dt.M)(ee=>{throw ee}))),(0,yn.j)(()=>{F||re||this.cancelNavigationTransition(N,"",1),this.currentNavigation?.id===N.id&&(this.currentNavigation=null)}),(0,zt.W)(ee=>{if(re=!0,xn(ee))this.events.next(new Li(N.id,this.urlSerializer.serialize(N.extractedUrl),ee.message,ee.cancellationCode)),function fn(d){return xn(d)&&Nn(d.url)}(ee)?this.events.next(new Gr(ee.url)):N.resolve(!1);else{this.events.next(new Yi(N.id,this.urlSerializer.serialize(N.extractedUrl),ee,N.targetSnapshot??void 0));try{N.resolve(u.errorHandler(ee))}catch(He){N.reject(He)}}return be.w}))}))}cancelNavigationTransition(u,m,w){const N=new Li(u.id,this.urlSerializer.serialize(u.extractedUrl),m,w);this.events.next(N),u.resolve(!1)}static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275prov=c.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})}}return d})();function R(d){return d!==Qn}let L=(()=>{class d{buildTitle(u){let m,w=u.root;for(;void 0!==w;)m=this.getResolvedTitleForRoute(w)??m,w=w.children.find(N=>N.outlet===Xe);return m}getResolvedTitleForRoute(u){return u.data[Vn]}static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275prov=c.jDH({token:d,factory:function(){return(0,c.WQX)(U)},providedIn:"root"})}}return d})(),U=(()=>{class d extends L{constructor(u){super(),this.title=u}updateTitle(u){const m=this.buildTitle(u);void 0!==m&&this.title.setTitle(m)}static{this.\u0275fac=function(m){return new(m||d)(c.KVO(qe.hE))}}static{this.\u0275prov=c.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})}}return d})(),J=(()=>{class d{static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275prov=c.jDH({token:d,factory:function(){return(0,c.WQX)(Be)},providedIn:"root"})}}return d})();class de{shouldDetach(g){return!1}store(g,u){}shouldAttach(g){return!1}retrieve(g){return null}shouldReuseRoute(g,u){return g.routeConfig===u.routeConfig}}let Be=(()=>{class d extends de{static{this.\u0275fac=function(){let u;return function(w){return(u||(u=c.xGo(d)))(w||d)}}()}static{this.\u0275prov=c.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})}}return d})();const Ke=new c.nKC("",{providedIn:"root",factory:()=>({})});let Pt=(()=>{class d{static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275prov=c.jDH({token:d,factory:function(){return(0,c.WQX)(It)},providedIn:"root"})}}return d})(),It=(()=>{class d{shouldProcessUrl(u){return!0}extract(u){return u}merge(u,m){return u}static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275prov=c.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})}}return d})();var rt=function(d){return d[d.COMPLETE=0]="COMPLETE",d[d.FAILED=1]="FAILED",d[d.REDIRECTING=2]="REDIRECTING",d}(rt||{});function Ct(d,g){d.events.pipe((0,Z.p)(u=>u instanceof Pr||u instanceof Li||u instanceof Yi||u instanceof jn),(0,je.T)(u=>u instanceof Pr||u instanceof jn?rt.COMPLETE:u instanceof Li&&(0===u.code||1===u.code)?rt.REDIRECTING:rt.FAILED),(0,Z.p)(u=>u!==rt.REDIRECTING),Ln(1)).subscribe(()=>{g()})}function mn(d){throw d}function Bn(d,g,u){return g.parse("/")}const hn={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},ur={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"};let tn=(()=>{class d{get navigationId(){return this.navigationTransitions.navigationId}get browserPageId(){return"computed"!==this.canceledNavigationResolution?this.currentPageId:this.location.getState()?.\u0275routerPageId??this.currentPageId}get events(){return this._events}constructor(){this.disposed=!1,this.currentPageId=0,this.console=(0,c.WQX)(c.H3F),this.isNgZoneEnabled=!1,this._events=new Re.B,this.options=(0,c.WQX)(Ke,{optional:!0})||{},this.pendingTasks=(0,c.WQX)(c.$K3),this.errorHandler=this.options.errorHandler||mn,this.malformedUriErrorHandler=this.options.malformedUriErrorHandler||Bn,this.navigated=!1,this.lastSuccessfulId=-1,this.urlHandlingStrategy=(0,c.WQX)(Pt),this.routeReuseStrategy=(0,c.WQX)(J),this.titleStrategy=(0,c.WQX)(L),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.config=(0,c.WQX)(sn,{optional:!0})?.flat()??[],this.navigationTransitions=(0,c.WQX)(M),this.urlSerializer=(0,c.WQX)(er),this.location=(0,c.WQX)(nn.aZ),this.componentInputBindingEnabled=!!(0,c.WQX)(Tn,{optional:!0}),this.eventsSubscription=new nt.yU,this.isNgZoneEnabled=(0,c.WQX)(c.SKi)instanceof c.SKi&&c.SKi.isInAngularZone(),this.resetConfig(this.config),this.currentUrlTree=new ir,this.rawUrlTree=this.currentUrlTree,this.browserUrlTree=this.currentUrlTree,this.routerState=xr(0,null),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe(u=>{this.lastSuccessfulId=u.id,this.currentPageId=this.browserPageId},u=>{this.console.warn(`Unhandled Navigation Error: ${u}`)}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){const u=this.navigationTransitions.events.subscribe(m=>{try{const{currentTransition:w}=this.navigationTransitions;if(null===w)return void(Vr(m)&&this._events.next(m));if(m instanceof bi)R(w.source)&&(this.browserUrlTree=w.extractedUrl);else if(m instanceof jn)this.rawUrlTree=w.rawUrl;else if(m instanceof yr){if("eager"===this.urlUpdateStrategy){if(!w.extras.skipLocationChange){const N=this.urlHandlingStrategy.merge(w.urlAfterRedirects,w.rawUrl);this.setBrowserUrl(N,w)}this.browserUrlTree=w.urlAfterRedirects}}else if(m instanceof ro)this.currentUrlTree=w.urlAfterRedirects,this.rawUrlTree=this.urlHandlingStrategy.merge(w.urlAfterRedirects,w.rawUrl),this.routerState=w.targetRouterState,"deferred"===this.urlUpdateStrategy&&(w.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,w),this.browserUrlTree=w.urlAfterRedirects);else if(m instanceof Li)0!==m.code&&1!==m.code&&(this.navigated=!0),(3===m.code||2===m.code)&&this.restoreHistory(w);else if(m instanceof Gr){const N=this.urlHandlingStrategy.merge(m.url,w.currentRawUrl),F={skipLocationChange:w.extras.skipLocationChange,replaceUrl:"eager"===this.urlUpdateStrategy||R(w.source)};this.scheduleNavigation(N,Qn,null,F,{resolve:w.resolve,reject:w.reject,promise:w.promise})}m instanceof Yi&&this.restoreHistory(w,!0),m instanceof Pr&&(this.navigated=!0),Vr(m)&&this._events.next(m)}catch(w){this.navigationTransitions.transitionAbortSubject.next(w)}});this.eventsSubscription.add(u)}resetRootComponentType(u){this.routerState.root.component=u,this.navigationTransitions.rootComponentType=u}initialNavigation(){if(this.setUpLocationChangeListener(),!this.navigationTransitions.hasRequestedNavigation){const u=this.location.getState();this.navigateToSyncWithBrowser(this.location.path(!0),Qn,u)}}setUpLocationChangeListener(){this.locationSubscription||(this.locationSubscription=this.location.subscribe(u=>{const m="popstate"===u.type?"popstate":"hashchange";"popstate"===m&&setTimeout(()=>{this.navigateToSyncWithBrowser(u.url,m,u.state)},0)}))}navigateToSyncWithBrowser(u,m,w){const N={replaceUrl:!0},F=w?.navigationId?w:null;if(w){const ee={...w};delete ee.navigationId,delete ee.\u0275routerPageId,0!==Object.keys(ee).length&&(N.state=ee)}const re=this.parseUrl(u);this.scheduleNavigation(re,m,F,N)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(u){this.config=u.map(mi),this.navigated=!1,this.lastSuccessfulId=-1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.locationSubscription&&(this.locationSubscription.unsubscribe(),this.locationSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(u,m={}){const{relativeTo:w,queryParams:N,fragment:F,queryParamsHandling:re,preserveFragment:ee}=m,He=ee?this.currentUrlTree.fragment:F;let cn,bt=null;switch(re){case"merge":bt={...this.currentUrlTree.queryParams,...N};break;case"preserve":bt=this.currentUrlTree.queryParams;break;default:bt=N||null}null!==bt&&(bt=this.removeEmptyProps(bt));try{cn=tr(w?w.snapshot:this.routerState.snapshot.root)}catch{("string"!=typeof u[0]||!u[0].startsWith("/"))&&(u=[]),cn=this.currentUrlTree.root}return Yr(cn,u,bt,He??null)}navigateByUrl(u,m={skipLocationChange:!1}){const w=Nn(u)?u:this.parseUrl(u),N=this.urlHandlingStrategy.merge(w,this.rawUrlTree);return this.scheduleNavigation(N,Qn,null,m)}navigate(u,m={skipLocationChange:!1}){return function Jt(d){for(let g=0;g<d.length;g++)if(null==d[g])throw new c.wOt(4008,!1)}(u),this.navigateByUrl(this.createUrlTree(u,m),m)}serializeUrl(u){return this.urlSerializer.serialize(u)}parseUrl(u){let m;try{m=this.urlSerializer.parse(u)}catch(w){m=this.malformedUriErrorHandler(w,this.urlSerializer,u)}return m}isActive(u,m){let w;if(w=!0===m?{...hn}:!1===m?{...ur}:m,Nn(u))return ui(this.currentUrlTree,u,w);const N=this.parseUrl(u);return ui(this.currentUrlTree,N,w)}removeEmptyProps(u){return Object.keys(u).reduce((m,w)=>{const N=u[w];return null!=N&&(m[w]=N),m},{})}scheduleNavigation(u,m,w,N,F){if(this.disposed)return Promise.resolve(!1);let re,ee,He;F?(re=F.resolve,ee=F.reject,He=F.promise):He=new Promise((cn,Xt)=>{re=cn,ee=Xt});const bt=this.pendingTasks.add();return Ct(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(bt))}),this.navigationTransitions.handleNavigationRequest({source:m,restoredState:w,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,currentBrowserUrl:this.browserUrlTree,rawUrl:u,extras:N,resolve:re,reject:ee,promise:He,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),He.catch(cn=>Promise.reject(cn))}setBrowserUrl(u,m){const w=this.urlSerializer.serialize(u);if(this.location.isCurrentPathEqualTo(w)||m.extras.replaceUrl){const F={...m.extras.state,...this.generateNgRouterState(m.id,this.browserPageId)};this.location.replaceState(w,"",F)}else{const N={...m.extras.state,...this.generateNgRouterState(m.id,this.browserPageId+1)};this.location.go(w,"",N)}}restoreHistory(u,m=!1){if("computed"===this.canceledNavigationResolution){const N=this.currentPageId-this.browserPageId;0!==N?this.location.historyGo(N):this.currentUrlTree===this.getCurrentNavigation()?.finalUrl&&0===N&&(this.resetState(u),this.browserUrlTree=u.currentUrlTree,this.resetUrlToCurrentUrlTree())}else"replace"===this.canceledNavigationResolution&&(m&&this.resetState(u),this.resetUrlToCurrentUrlTree())}resetState(u){this.routerState=u.currentRouterState,this.currentUrlTree=u.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,u.rawUrl)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(u,m){return"computed"===this.canceledNavigationResolution?{navigationId:u,\u0275routerPageId:m}:{navigationId:u}}static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275prov=c.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})}}return d})();function Vr(d){return!(d instanceof ro||d instanceof Gr)}let Rn=(()=>{class d{constructor(u,m,w,N,F,re){this.router=u,this.route=m,this.tabIndexAttribute=w,this.renderer=N,this.el=F,this.locationStrategy=re,this.href=null,this.commands=null,this.onChanges=new Re.B,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1;const ee=F.nativeElement.tagName?.toLowerCase();this.isAnchorElement="a"===ee||"area"===ee,this.isAnchorElement?this.subscription=u.events.subscribe(He=>{He instanceof Pr&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(u){null!=this.tabIndexAttribute||this.isAnchorElement||this.applyAttributeValue("tabindex",u)}ngOnChanges(u){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(u){null!=u?(this.commands=Array.isArray(u)?u:[u],this.setTabIndexIfNotOnNativeEl("0")):(this.commands=null,this.setTabIndexIfNotOnNativeEl(null))}onClick(u,m,w,N,F){return!!(null===this.urlTree||this.isAnchorElement&&(0!==u||m||w||N||F||"string"==typeof this.target&&"_self"!=this.target))||(this.router.navigateByUrl(this.urlTree,{skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state}),!this.isAnchorElement)}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){this.href=null!==this.urlTree&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(this.urlTree)):null;const u=null===this.href?null:(0,c.n$t)(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",u)}applyAttributeValue(u,m){const w=this.renderer,N=this.el.nativeElement;null!==m?w.setAttribute(N,u,m):w.removeAttribute(N,u)}get urlTree(){return null===this.commands?null:this.router.createUrlTree(this.commands,{relativeTo:void 0!==this.relativeTo?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(m){return new(m||d)(c.rXU(tn),c.rXU(br),c.kS0("tabindex"),c.rXU(c.sFG),c.rXU(c.aKT),c.rXU(nn.hb))}}static{this.\u0275dir=c.FsC({type:d,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(m,w){1&m&&c.bIt("click",function(F){return w.onClick(F.button,F.ctrlKey,F.shiftKey,F.altKey,F.metaKey)}),2&m&&c.BMQ("target",w.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",relativeTo:"relativeTo",preserveFragment:["preserveFragment","preserveFragment",c.L39],skipLocationChange:["skipLocationChange","skipLocationChange",c.L39],replaceUrl:["replaceUrl","replaceUrl",c.L39],routerLink:"routerLink"},standalone:!0,features:[c.GFd,c.OA$]})}}return d})(),bn=(()=>{class d{get isActive(){return this._isActive}constructor(u,m,w,N,F){this.router=u,this.element=m,this.renderer=w,this.cdr=N,this.link=F,this.classes=[],this._isActive=!1,this.routerLinkActiveOptions={exact:!1},this.isActiveChange=new c.bkB,this.routerEventsSubscription=u.events.subscribe(re=>{re instanceof Pr&&this.update()})}ngAfterContentInit(){(0,oe.of)(this.links.changes,(0,oe.of)(null)).pipe((0,Qt.U)()).subscribe(u=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();const u=[...this.links.toArray(),this.link].filter(m=>!!m).map(m=>m.onChanges);this.linkInputChangesSubscription=(0,Q.H)(u).pipe((0,Qt.U)()).subscribe(m=>{this._isActive!==this.isLinkActive(this.router)(m)&&this.update()})}set routerLinkActive(u){const m=Array.isArray(u)?u:u.split(" ");this.classes=m.filter(w=>!!w)}ngOnChanges(u){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{const u=this.hasActiveLinks();this._isActive!==u&&(this._isActive=u,this.cdr.markForCheck(),this.classes.forEach(m=>{u?this.renderer.addClass(this.element.nativeElement,m):this.renderer.removeClass(this.element.nativeElement,m)}),u&&void 0!==this.ariaCurrentWhenActive?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this.isActiveChange.emit(u))})}isLinkActive(u){const m=function Oi(d){return!!d.paths}(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return w=>!!w.urlTree&&u.isActive(w.urlTree,m)}hasActiveLinks(){const u=this.isLinkActive(this.router);return this.link&&u(this.link)||this.links.some(u)}static{this.\u0275fac=function(m){return new(m||d)(c.rXU(tn),c.rXU(c.aKT),c.rXU(c.sFG),c.rXU(c.gRc),c.rXU(Rn,8))}}static{this.\u0275dir=c.FsC({type:d,selectors:[["","routerLinkActive",""]],contentQueries:function(m,w,N){if(1&m&&c.wni(N,Rn,5),2&m){let F;c.mGM(F=c.lsd())&&(w.links=F)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],standalone:!0,features:[c.OA$]})}}return d})();class oi{}let uo=(()=>{class d{constructor(u,m,w,N,F){this.router=u,this.injector=w,this.preloadingStrategy=N,this.loader=F}setUpPreloading(){this.subscription=this.router.events.pipe((0,Z.p)(u=>u instanceof Pr),(0,Ge.H)(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(u,m){const w=[];for(const N of m){N.providers&&!N._injector&&(N._injector=(0,c.Ol2)(N.providers,u,`Route: ${N.path}`));const F=N._injector??u,re=N._loadedInjector??F;(N.loadChildren&&!N._loadedRoutes&&void 0===N.canLoad||N.loadComponent&&!N._loadedComponent)&&w.push(this.preloadConfig(F,N)),(N.children||N._loadedRoutes)&&w.push(this.processRoutes(re,N.children??N._loadedRoutes))}return(0,Q.H)(w).pipe((0,Qt.U)())}preloadConfig(u,m){return this.preloadingStrategy.preload(m,()=>{let w;w=m.loadChildren&&void 0===m.canLoad?this.loader.loadChildren(u,m):(0,oe.of)(null);const N=w.pipe((0,te.Z)(F=>null===F?(0,oe.of)(void 0):(m._loadedRoutes=F.routes,m._loadedInjector=F.injector,this.processRoutes(F.injector??u,F.routes))));if(m.loadComponent&&!m._loadedComponent){const F=this.loader.loadComponent(m);return(0,Q.H)([N,F]).pipe((0,Qt.U)())}return N})}static{this.\u0275fac=function(m){return new(m||d)(c.KVO(tn),c.KVO(c.Ql9),c.KVO(c.uvJ),c.KVO(oi),c.KVO(_))}}static{this.\u0275prov=c.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})}}return d})();const qo=new c.nKC("");let ds=(()=>{class d{constructor(u,m,w,N,F={}){this.urlSerializer=u,this.transitions=m,this.viewportScroller=w,this.zone=N,this.options=F,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},F.scrollPositionRestoration=F.scrollPositionRestoration||"disabled",F.anchorScrolling=F.anchorScrolling||"disabled"}init(){"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(u=>{u instanceof bi?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=u.navigationTrigger,this.restoredId=u.restoredState?u.restoredState.navigationId:0):u instanceof Pr?(this.lastId=u.id,this.scheduleScrollEvent(u,this.urlSerializer.parse(u.urlAfterRedirects).fragment)):u instanceof jn&&0===u.code&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(u,this.urlSerializer.parse(u.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(u=>{u instanceof ei&&(u.position?"top"===this.options.scrollPositionRestoration?this.viewportScroller.scrollToPosition([0,0]):"enabled"===this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition(u.position):u.anchor&&"enabled"===this.options.anchorScrolling?this.viewportScroller.scrollToAnchor(u.anchor):"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(u,m){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new ei(u,"popstate"===this.lastSource?this.store[this.restoredId]:null,m))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(m){c.QTQ()}}static{this.\u0275prov=c.jDH({token:d,factory:d.\u0275fac})}}return d})();function it(d,g){return{\u0275kind:d,\u0275providers:g}}function ot(){const d=(0,c.WQX)(c.zZn);return g=>{const u=d.get(c.o8S);if(g!==u.components[0])return;const m=d.get(tn),w=d.get(_n);1===d.get(fs)&&m.initialNavigation(),d.get(Tr,null,c.$GK.Optional)?.setUpPreloading(),d.get(qo,null,c.$GK.Optional)?.init(),m.resetRootComponentType(u.componentTypes[0]),w.closed||(w.next(),w.complete(),w.unsubscribe())}}const _n=new c.nKC("",{factory:()=>new Re.B}),fs=new c.nKC("",{providedIn:"root",factory:()=>1}),Tr=new c.nKC("");function Ni(d){return it(0,[{provide:Tr,useExisting:uo},{provide:oi,useExisting:d}])}const Ee=new c.nKC("ROUTER_FORROOT_GUARD"),Kt=[nn.aZ,{provide:er,useClass:Gn},tn,ni,{provide:br,useFactory:function at(d){return d.routerState.root},deps:[tn]},_,[]];function ol(){return new c.NEm("Router",tn)}let sl=(()=>{class d{constructor(u){}static forRoot(u,m){return{ngModule:d,providers:[Kt,[],{provide:sn,multi:!0,useValue:u},{provide:Ee,useFactory:Xi,deps:[[tn,new c.Xx1,new c.kdw]]},{provide:Ke,useValue:m||{}},m?.useHash?{provide:nn.hb,useClass:nn.fw}:{provide:nn.hb,useClass:nn.Sm},{provide:qo,useFactory:()=>{const d=(0,c.WQX)(nn.Xr),g=(0,c.WQX)(c.SKi),u=(0,c.WQX)(Ke),m=(0,c.WQX)(M),w=(0,c.WQX)(er);return u.scrollOffset&&d.setOffset(u.scrollOffset),new ds(w,m,d,g,u)}},m?.preloadingStrategy?Ni(m.preloadingStrategy).\u0275providers:[],{provide:c.NEm,multi:!0,useFactory:ol},m?.initialNavigation?Ca(m):[],m?.bindToComponentInputs?it(8,[Mr,{provide:Tn,useExisting:Mr}]).\u0275providers:[],[{provide:As,useFactory:ot},{provide:c.iLQ,multi:!0,useExisting:As}]]}}static forChild(u){return{ngModule:d,providers:[{provide:sn,multi:!0,useValue:u}]}}static{this.\u0275fac=function(m){return new(m||d)(c.KVO(Ee,8))}}static{this.\u0275mod=c.$C({type:d})}static{this.\u0275inj=c.G2t({})}}return d})();function Xi(d){return"guarded"}function Ca(d){return["disabled"===d.initialNavigation?it(3,[{provide:c.hnV,multi:!0,useFactory:()=>{const g=(0,c.WQX)(tn);return()=>{g.setUpLocationChangeListener()}}},{provide:fs,useValue:2}]).\u0275providers:[],"enabledBlocking"===d.initialNavigation?it(2,[{provide:fs,useValue:0},{provide:c.hnV,multi:!0,deps:[c.zZn],useFactory:g=>{const u=g.get(nn.hj,Promise.resolve());return()=>u.then(()=>new Promise(m=>{const w=g.get(tn),N=g.get(_n);Ct(w,()=>{m(!0)}),g.get(M).afterPreactivation=()=>(m(!0),N.closed?(0,oe.of)(void 0):N),w.initialNavigation()}))}}]).\u0275providers:[]]}const As=new c.nKC("")},467:(Je,me,O)=>{function c(ue,se,Q,oe,ne,fe,ae){try{var pe=ue[fe](ae),z=pe.value}catch(G){return void Q(G)}pe.done?se(z):Promise.resolve(z).then(oe,ne)}function C(ue){return function(){var se=this,Q=arguments;return new Promise(function(oe,ne){var fe=ue.apply(se,Q);function ae(z){c(fe,oe,ne,ae,pe,"next",z)}function pe(z){c(fe,oe,ne,ae,pe,"throw",z)}ae(void 0)})}}O.d(me,{A:()=>C})},1635:(Je,me,O)=>{function G(Z,te,Y,le){return new(Y||(Y=Promise))(function(Me,Ge){function dt(xt){try{lt(le.next(xt))}catch(qt){Ge(qt)}}function zt(xt){try{lt(le.throw(xt))}catch(qt){Ge(qt)}}function lt(xt){xt.done?Me(xt.value):function Te(Me){return Me instanceof Y?Me:new Y(function(Ge){Ge(Me)})}(xt.value).then(dt,zt)}lt((le=le.apply(Z,te||[])).next())})}function Ce(Z){return this instanceof Ce?(this.v=Z,this):new Ce(Z)}function Ae(Z,te,Y){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Te,le=Y.apply(Z,te||[]),Me=[];return Te=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),dt("next"),dt("throw"),dt("return",function Ge(yt){return function(yn){return Promise.resolve(yn).then(yt,qt)}}),Te[Symbol.asyncIterator]=function(){return this},Te;function dt(yt,yn){le[yt]&&(Te[yt]=function(Gt){return new Promise(function(qe,Xe){Me.push([yt,Gt,qe,Xe])>1||zt(yt,Gt)})},yn&&(Te[yt]=yn(Te[yt])))}function zt(yt,yn){try{!function lt(yt){yt.value instanceof Ce?Promise.resolve(yt.value.v).then(xt,qt):rr(Me[0][2],yt)}(le[yt](yn))}catch(Gt){rr(Me[0][3],Gt)}}function xt(yt){zt("next",yt)}function qt(yt){zt("throw",yt)}function rr(yt,yn){yt(yn),Me.shift(),Me.length&&zt(Me[0][0],Me[0][1])}}function tt(Z){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y,te=Z[Symbol.asyncIterator];return te?te.call(Z):(Z=function kt(Z){var te="function"==typeof Symbol&&Symbol.iterator,Y=te&&Z[te],le=0;if(Y)return Y.call(Z);if(Z&&"number"==typeof Z.length)return{next:function(){return Z&&le>=Z.length&&(Z=void 0),{value:Z&&Z[le++],done:!Z}}};throw new TypeError(te?"Object is not iterable.":"Symbol.iterator is not defined.")}(Z),Y={},le("next"),le("throw"),le("return"),Y[Symbol.asyncIterator]=function(){return this},Y);function le(Me){Y[Me]=Z[Me]&&function(Ge){return new Promise(function(dt,zt){!function Te(Me,Ge,dt,zt){Promise.resolve(zt).then(function(lt){Me({value:lt,done:dt})},Ge)}(dt,zt,(Ge=Z[Me](Ge)).done,Ge.value)})}}}O.d(me,{AQ:()=>Ae,N3:()=>Ce,sH:()=>G,xN:()=>tt}),"function"==typeof SuppressedError&&SuppressedError}},Je=>{Je(Je.s=1413)}]);