"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[693],{9339:(O,m,s)=>{s.d(m,{q:()=>l});var t=s(540),g=s(8211),h=s(8010),d=s(177);function u(a,o){1&a&&(t.j41(0,"div",8)(1,"div",9)(2,"span",10),t.<PERSON>(3,"Loading..."),t.k0s()(),t.j41(4,"div",11),t.EFF(5,"Loading chats..."),t.k0s()())}function r(a,o){if(1&a&&(t.j41(0,"div",12),t.nrm(1,"i",13),t.j41(2,"p"),t.EFF(3,"No conversations yet"),t.k0s(),t.j41(4,"small"),t.<PERSON><PERSON>(5),t.k0s()()),2&a){const e=t.XpG();t.R7$(5),t.SpI("Start a conversation with a ","PATIENT"===(null==e.currentUser?null:e.currentUser.role)?"doctor":"patient","")}}function _(a,o){1&a&&t.nrm(0,"span",28)}function C(a,o){if(1&a&&(t.j41(0,"span",31),t.nrm(1,"i",32),t.k0s()),2&a){const e=t.XpG(2).$implicit;t.R7$(1),t.AVh("text-primary","READ"===e.lastMessage.status)("text-muted","READ"!==e.lastMessage.status)}}function f(a,o){if(1&a&&(t.j41(0,"p",29),t.DNE(1,C,2,4,"span",30),t.EFF(2),t.nI1(3,"slice"),t.k0s()),2&a){const e=t.XpG().$implicit,n=t.XpG(2);t.R7$(1),t.Y8G("ngIf",e.lastMessage.sender.id===(null==n.currentUser?null:n.currentUser.id)),t.R7$(1),t.Lme(" ",t.brH(3,3,e.lastMessage.content,0,50),"",e.lastMessage.content.length>50?"...":""," ")}}function v(a,o){1&a&&(t.j41(0,"p",29)(1,"em"),t.EFF(2,"No messages yet"),t.k0s()())}function c(a,o){if(1&a&&(t.j41(0,"span",33),t.EFF(1),t.k0s()),2&a){const e=t.XpG().$implicit;t.R7$(1),t.SpI(" ",e.unreadCount," ")}}function P(a,o){if(1&a){const e=t.RV6();t.j41(0,"div",16),t.bIt("click",function(){const p=t.eBV(e).$implicit,b=t.XpG(2);return t.Njj(b.selectChat(p))}),t.j41(1,"div",17),t.nrm(2,"img",18),t.DNE(3,_,1,0,"span",19),t.k0s(),t.j41(4,"div",20)(5,"div",21)(6,"h6",22),t.EFF(7),t.k0s(),t.j41(8,"small",23),t.EFF(9),t.k0s()(),t.j41(10,"div",24),t.DNE(11,f,4,7,"p",25),t.DNE(12,v,3,0,"p",25),t.k0s()(),t.j41(13,"div",26),t.DNE(14,c,2,1,"span",27),t.k0s()()}if(2&a){const e=o.$implicit,n=t.XpG(2);t.AVh("active",n.selectedChatId===e.id),t.R7$(2),t.Y8G("src",n.getOtherParticipant(e).avatar||"/assets/images/default-avatar.png",t.B4B)("alt",n.getOtherParticipant(e).fullName),t.R7$(1),t.Y8G("ngIf",!1),t.R7$(4),t.SpI(" ",n.getOtherParticipant(e).fullName," "),t.R7$(2),t.SpI(" ",n.formatLastMessageTime(e.lastMessage?e.lastMessage.createdAt:e.createdAt)," "),t.R7$(2),t.Y8G("ngIf",e.lastMessage),t.R7$(1),t.Y8G("ngIf",!e.lastMessage),t.R7$(2),t.Y8G("ngIf",e.unreadCount>0)}}function M(a,o){if(1&a&&(t.j41(0,"div",14),t.DNE(1,P,15,10,"div",15),t.k0s()),2&a){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.chats)}}let l=(()=>{class a{constructor(e,n){this.chatService=e,this.authService=n,this.chatSelected=new t.bkB,this.chats=[],this.selectedChatId=null,this.loading=!0,this.subscriptions=[]}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.loadChats(),this.subscribeToChats()}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe())}loadChats(){this.chatService.loadUserChats()}subscribeToChats(){const e=this.chatService.chats$.subscribe({next:i=>{this.chats=i,this.loading=!1},error:i=>{console.error("Failed to load chats:",i),this.loading=!1}});this.subscriptions.push(e);const n=this.chatService.messages$.subscribe({next:i=>{this.updateChatWithNewMessage(i)}});this.subscriptions.push(n)}updateChatWithNewMessage(e){const n=this.chats.findIndex(i=>i.id===e.chatId);if(-1!==n){this.chats[n].lastMessage=e,this.chats[n].updatedAt=e.createdAt,e.sender.id!==this.currentUser?.id&&this.chats[n].unreadCount++;const i=this.chats.splice(n,1)[0];this.chats.unshift(i)}}selectChat(e){this.selectedChatId=e.id,this.chatSelected.emit(e),e.unreadCount>0&&this.chatService.markMessagesAsRead(e.id).subscribe({next:()=>{e.unreadCount=0},error:n=>{console.error("Failed to mark messages as read:",n)}})}getOtherParticipant(e){return"PATIENT"===this.currentUser?.role?e.doctor:e.patient}formatLastMessageTime(e){const n=new Date(e),p=((new Date).getTime()-n.getTime())/36e5;return p<1?"Just now":p<24?n.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):n.toLocaleDateString()}static{this.\u0275fac=function(n){return new(n||a)(t.rXU(g.m),t.rXU(h.u))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-chat-list"]],outputs:{chatSelected:"chatSelected"},decls:9,vars:3,consts:[[1,"chat-list"],[1,"chat-list-header"],[1,"mb-0"],[1,"bi","bi-chat-dots","me-2"],[1,"chat-list-body"],["class","text-center p-3",4,"ngIf"],["class","text-center p-4 text-muted",4,"ngIf"],["class","chat-items",4,"ngIf"],[1,"text-center","p-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"mt-2"],[1,"text-center","p-4","text-muted"],[1,"bi","bi-chat-square-text","fs-1","mb-3","d-block"],[1,"chat-items"],["class","chat-item",3,"active","click",4,"ngFor","ngForOf"],[1,"chat-item",3,"click"],[1,"chat-avatar"],[1,"avatar-img",3,"src","alt"],["class","online-indicator",4,"ngIf"],[1,"chat-content"],[1,"chat-header"],[1,"chat-name","mb-0"],[1,"chat-time","text-muted"],[1,"chat-preview"],["class","mb-0 text-muted",4,"ngIf"],[1,"chat-meta"],["class","badge bg-primary rounded-pill",4,"ngIf"],[1,"online-indicator"],[1,"mb-0","text-muted"],["class","me-1",4,"ngIf"],[1,"me-1"],[1,"bi","bi-check2-all"],[1,"badge","bg-primary","rounded-pill"]],template:function(n,i){1&n&&(t.j41(0,"div",0)(1,"div",1)(2,"h5",2),t.nrm(3,"i",3),t.EFF(4," Messages "),t.k0s()(),t.j41(5,"div",4),t.DNE(6,u,6,0,"div",5),t.DNE(7,r,6,1,"div",6),t.DNE(8,M,2,1,"div",7),t.k0s()()),2&n&&(t.R7$(6),t.Y8G("ngIf",i.loading),t.R7$(1),t.Y8G("ngIf",!i.loading&&0===i.chats.length),t.R7$(1),t.Y8G("ngIf",!i.loading&&i.chats.length>0))},dependencies:[d.Sq,d.bT,d.P9],styles:[".chat-list[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;border-right:1px solid #e9ecef}.chat-list-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid #e9ecef;background-color:#f8f9fa}.chat-list-body[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem 1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:background-color .2s ease}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.chat-items[_ngcontent-%COMP%]   .chat-item.active[_ngcontent-%COMP%]{background-color:#e3f2fd;border-left:3px solid #2196f3}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]{position:relative;margin-right:.75rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%]{width:45px;height:45px;border-radius:50%;object-fit:cover;border:2px solid #e9ecef}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:12px;height:12px;background-color:#4caf50;border:2px solid white;border-radius:50%}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]{flex:1;min-width:0}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.25rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-name[_ngcontent-%COMP%]{font-weight:600;color:#333;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-time[_ngcontent-%COMP%]{font-size:.75rem;white-space:nowrap}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-content[_ngcontent-%COMP%]   .chat-preview[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.3;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]{margin-left:.5rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;min-width:20px;height:20px;display:flex;align-items:center;justify-content:center}@media (max-width: 768px){.chat-list[_ngcontent-%COMP%]{border-right:none}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{padding:1rem}.chat-items[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%]{width:50px;height:50px}}"]})}}return a})()},2693:(O,m,s)=>{s.r(m),s.d(m,{ChatModule:()=>v});var t=s(177),g=s(4341),h=s(2434),d=s(4978),u=s(9339),r=s(540);const _=[{path:"",canActivate:[d.q],component:u.q}];let C=(()=>{class c{static{this.\u0275fac=function(l){return new(l||c)}}static{this.\u0275mod=r.$C({type:c})}static{this.\u0275inj=r.G2t({imports:[h.iI.forChild(_),h.iI]})}}return c})();var f=s(3887);let v=(()=>{class c{static{this.\u0275fac=function(l){return new(l||c)}}static{this.\u0275mod=r.$C({type:c})}static{this.\u0275inj=r.G2t({imports:[t.MD,g.YN,g.X1,h.iI,C,f.G]})}}return c})()}}]);