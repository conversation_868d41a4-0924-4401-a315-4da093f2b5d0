{"ast": null, "code": "import { ConversationType } from '../../core/services/ai-health-bot.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/ai-health-bot.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction ConversationHistoryComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ConversationHistoryComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConversationHistoryComponent_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", type_r8);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getConversationTypeDisplayName(type_r8), \" \");\n  }\n}\nfunction ConversationHistoryComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"span\", 35);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 36);\n    i0.ɵɵtext(5, \"Loading conversation history...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ConversationHistoryComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction ConversationHistoryComponent_div_33_h5_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\", 44);\n    i0.ɵɵtext(1, \"No conversations yet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConversationHistoryComponent_div_33_h5_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\", 44);\n    i0.ɵɵtext(1, \"No conversations match your filters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConversationHistoryComponent_div_33_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 44);\n    i0.ɵɵtext(1, \" Start your first conversation with the AI Health Assistant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConversationHistoryComponent_div_33_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ConversationHistoryComponent_div_33_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.startNewConversation());\n    });\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \" Start First Conversation \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConversationHistoryComponent_div_33_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ConversationHistoryComponent_div_33_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      ctx_r16.clearSearch();\n      ctx_r16.selectedType = \"ALL\";\n      return i0.ɵɵresetView(ctx_r16.onTypeFilterChange());\n    });\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConversationHistoryComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtemplate(2, ConversationHistoryComponent_div_33_h5_2_Template, 2, 0, \"h5\", 41);\n    i0.ɵɵtemplate(3, ConversationHistoryComponent_div_33_h5_3_Template, 2, 0, \"h5\", 41);\n    i0.ɵɵtemplate(4, ConversationHistoryComponent_div_33_p_4_Template, 2, 0, \"p\", 41);\n    i0.ɵɵtemplate(5, ConversationHistoryComponent_div_33_button_5_Template, 3, 0, \"button\", 42);\n    i0.ɵɵtemplate(6, ConversationHistoryComponent_div_33_button_6_Template, 3, 0, \"button\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.conversations.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.conversations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.conversations.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.conversations.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.conversations.length > 0);\n  }\n}\nfunction ConversationHistoryComponent_div_34_div_1_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵelement(1, \"i\", 69);\n    i0.ɵɵtext(2, \" Shared \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConversationHistoryComponent_div_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵlistener(\"click\", function ConversationHistoryComponent_div_34_div_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r22);\n      const conversation_r19 = restoredCtx.$implicit;\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.openConversation(conversation_r19));\n    });\n    i0.ɵɵelementStart(1, \"div\", 52)(2, \"div\", 3)(3, \"div\", 53);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 54)(6, \"h6\", 5);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"small\", 44);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 55)(11, \"div\", 56)(12, \"small\", 44);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"small\", 57);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 58)(17, \"p\", 59);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 60)(20, \"div\", 61)(21, \"div\", 62)(22, \"span\", 63);\n    i0.ɵɵelement(23, \"i\", 64);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, ConversationHistoryComponent_div_34_div_1_span_25_Template, 3, 0, \"span\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 66);\n    i0.ɵɵelement(27, \"i\", 67);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const conversation_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate1(\"fas fa-\", ctx_r18.getConversationTypeIcon(conversation_r19.conversationType), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(conversation_r19.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.getConversationTypeDisplayName(conversation_r19.conversationType), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r18.formatDate(conversation_r19.updatedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r18.formatTime(conversation_r19.updatedAt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r18.getConversationPreview(conversation_r19));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", conversation_r19.messageCount, \" message\", conversation_r19.messageCount !== 1 ? \"s\" : \"\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", conversation_r19.isSharedWithDoctor);\n  }\n}\nfunction ConversationHistoryComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtemplate(1, ConversationHistoryComponent_div_34_div_1_Template, 28, 11, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.filteredConversations);\n  }\n}\nexport let ConversationHistoryComponent = /*#__PURE__*/(() => {\n  class ConversationHistoryComponent {\n    constructor(aiHealthBotService, router) {\n      this.aiHealthBotService = aiHealthBotService;\n      this.router = router;\n      this.conversations = [];\n      this.filteredConversations = [];\n      this.isLoading = true;\n      this.error = '';\n      this.searchTerm = '';\n      this.selectedType = 'ALL';\n      this.conversationTypes = Object.values(ConversationType);\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      this.loadConversations();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    loadConversations() {\n      this.isLoading = true;\n      this.error = '';\n      const sub = this.aiHealthBotService.getUserConversations().subscribe({\n        next: conversations => {\n          this.conversations = conversations;\n          this.applyFilters();\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Failed to load conversations:', error);\n          this.error = 'Failed to load conversation history';\n          this.isLoading = false;\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    onSearchChange() {\n      this.applyFilters();\n    }\n    onTypeFilterChange() {\n      this.applyFilters();\n    }\n    applyFilters() {\n      let filtered = [...this.conversations];\n      // Filter by search term\n      if (this.searchTerm.trim()) {\n        const searchLower = this.searchTerm.toLowerCase();\n        filtered = filtered.filter(conv => conv.title.toLowerCase().includes(searchLower) || conv.summary?.toLowerCase().includes(searchLower) || conv.lastMessage?.toLowerCase().includes(searchLower));\n      }\n      // Filter by conversation type\n      if (this.selectedType !== 'ALL') {\n        filtered = filtered.filter(conv => conv.conversationType === this.selectedType);\n      }\n      this.filteredConversations = filtered;\n    }\n    openConversation(conversation) {\n      this.aiHealthBotService.setCurrentConversation(conversation);\n      this.router.navigate(['/ai-health-bot/chat', conversation.id]);\n    }\n    startNewConversation() {\n      this.router.navigate(['/ai-health-bot/chat']);\n    }\n    getConversationTypeDisplayName(type) {\n      return this.aiHealthBotService.getConversationTypeDisplayName(type);\n    }\n    getConversationTypeIcon(type) {\n      return this.aiHealthBotService.getConversationTypeIcon(type);\n    }\n    formatDate(dateString) {\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffTime = Math.abs(now.getTime() - date.getTime());\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      if (diffDays === 1) {\n        return 'Today';\n      } else if (diffDays === 2) {\n        return 'Yesterday';\n      } else if (diffDays <= 7) {\n        return `${diffDays - 1} days ago`;\n      } else {\n        return date.toLocaleDateString();\n      }\n    }\n    formatTime(dateString) {\n      const date = new Date(dateString);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    getConversationPreview(conversation) {\n      if (conversation.lastMessage) {\n        return conversation.lastMessage.length > 100 ? conversation.lastMessage.substring(0, 97) + '...' : conversation.lastMessage;\n      }\n      return 'No messages yet';\n    }\n    refreshConversations() {\n      this.loadConversations();\n    }\n    clearSearch() {\n      this.searchTerm = '';\n      this.applyFilters();\n    }\n    static {\n      this.ɵfac = function ConversationHistoryComponent_Factory(t) {\n        return new (t || ConversationHistoryComponent)(i0.ɵɵdirectiveInject(i1.AiHealthBotService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ConversationHistoryComponent,\n        selectors: [[\"app-conversation-history\"]],\n        decls: 35,\n        vars: 13,\n        consts: [[1, \"conversation-history-container\"], [1, \"history-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-history\", \"text-primary\", \"me-2\"], [1, \"mb-0\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\", \"me-1\"], [1, \"filters-section\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [1, \"input-group\"], [1, \"input-group-text\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"Search conversations...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"class\", \"btn btn-outline-secondary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [1, \"col-md-4\"], [1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"ALL\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-2\"], [1, \"text-muted\", \"small\"], [1, \"history-content\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [\"class\", \"empty-state text-center py-5\", 4, \"ngIf\"], [\"class\", \"conversations-list\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [3, \"value\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"empty-state\", \"text-center\", \"py-5\"], [1, \"fas\", \"fa-comments\", \"fa-3x\", \"text-muted\", \"mb-3\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-secondary\", 3, \"click\", 4, \"ngIf\"], [1, \"text-muted\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-2\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-filter\", \"me-2\"], [1, \"conversations-list\"], [\"class\", \"conversation-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"conversation-card\", 3, \"click\"], [1, \"conversation-header\"], [1, \"conversation-type-icon\"], [1, \"conversation-title\"], [1, \"conversation-meta\"], [1, \"conversation-date\"], [1, \"text-muted\", \"d-block\"], [1, \"conversation-preview\"], [1, \"mb-2\"], [1, \"conversation-footer\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"conversation-stats\"], [1, \"badge\", \"bg-light\", \"text-dark\", \"me-2\"], [1, \"fas\", \"fa-comments\", \"me-1\"], [\"class\", \"badge bg-success\", 4, \"ngIf\"], [1, \"conversation-actions\"], [1, \"fas\", \"fa-chevron-right\", \"text-muted\"], [1, \"badge\", \"bg-success\"], [1, \"fas\", \"fa-share\", \"me-1\"]],\n        template: function ConversationHistoryComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"i\", 4);\n            i0.ɵɵelementStart(5, \"h4\", 5);\n            i0.ɵɵtext(6, \"Conversation History\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function ConversationHistoryComponent_Template_button_click_8_listener() {\n              return ctx.startNewConversation();\n            });\n            i0.ɵɵelement(9, \"i\", 8);\n            i0.ɵɵtext(10, \" New Chat \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function ConversationHistoryComponent_Template_button_click_11_listener() {\n              return ctx.refreshConversations();\n            });\n            i0.ɵɵelement(12, \"i\", 10);\n            i0.ɵɵtext(13, \" Refresh \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12)(16, \"div\", 13)(17, \"div\", 14)(18, \"span\", 15);\n            i0.ɵɵelement(19, \"i\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"input\", 17);\n            i0.ɵɵlistener(\"ngModelChange\", function ConversationHistoryComponent_Template_input_ngModelChange_20_listener($event) {\n              return ctx.searchTerm = $event;\n            })(\"input\", function ConversationHistoryComponent_Template_input_input_20_listener() {\n              return ctx.onSearchChange();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(21, ConversationHistoryComponent_button_21_Template, 2, 0, \"button\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 19)(23, \"select\", 20);\n            i0.ɵɵlistener(\"ngModelChange\", function ConversationHistoryComponent_Template_select_ngModelChange_23_listener($event) {\n              return ctx.selectedType = $event;\n            })(\"change\", function ConversationHistoryComponent_Template_select_change_23_listener() {\n              return ctx.onTypeFilterChange();\n            });\n            i0.ɵɵelementStart(24, \"option\", 21);\n            i0.ɵɵtext(25, \"All Types\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(26, ConversationHistoryComponent_option_26_Template, 2, 2, \"option\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(27, \"div\", 23)(28, \"div\", 24);\n            i0.ɵɵtext(29);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(30, \"div\", 25);\n            i0.ɵɵtemplate(31, ConversationHistoryComponent_div_31_Template, 6, 0, \"div\", 26);\n            i0.ɵɵtemplate(32, ConversationHistoryComponent_div_32_Template, 3, 1, \"div\", 27);\n            i0.ɵɵtemplate(33, ConversationHistoryComponent_div_33_Template, 7, 5, \"div\", 28);\n            i0.ɵɵtemplate(34, ConversationHistoryComponent_div_34_Template, 2, 1, \"div\", 29);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"fa-spin\", ctx.isLoading);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.searchTerm);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngModel\", ctx.selectedType);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.conversationTypes);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate2(\" \", ctx.filteredConversations.length, \" conversation\", ctx.filteredConversations.length !== 1 ? \"s\" : \"\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredConversations.length === 0 && !ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredConversations.length > 0);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgModel],\n        styles: [\".conversation-history-container[_ngcontent-%COMP%]{padding:1.5rem;background:#f8f9fa;min-height:calc(100vh - 120px)}.history-header[_ngcontent-%COMP%]{background:white;padding:1.5rem;border-radius:.5rem;box-shadow:0 2px 8px #0000001a;margin-bottom:1.5rem}.filters-section[_ngcontent-%COMP%]{border-top:1px solid #dee2e6;padding-top:1rem;margin-top:1rem}.history-content[_ngcontent-%COMP%]{background:white;border-radius:.5rem;box-shadow:0 2px 8px #0000001a;min-height:400px}.conversations-list[_ngcontent-%COMP%]{padding:1rem}.conversation-card[_ngcontent-%COMP%]{border:1px solid #dee2e6;border-radius:.5rem;padding:1rem;margin-bottom:1rem;cursor:pointer;transition:all .2s ease;background:white}.conversation-card[_ngcontent-%COMP%]:hover{border-color:#007bff;box-shadow:0 4px 12px #007bff26;transform:translateY(-2px)}.conversation-card[_ngcontent-%COMP%]:last-child{margin-bottom:0}.conversation-header[_ngcontent-%COMP%]{display:flex;justify-content:between;align-items:flex-start;margin-bottom:.75rem}.conversation-type-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:linear-gradient(135deg,#007bff,#0056b3);color:#fff;display:flex;align-items:center;justify-content:center;margin-right:.75rem;flex-shrink:0}.conversation-title[_ngcontent-%COMP%]{flex:1}.conversation-title[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#333;font-weight:600;line-height:1.2}.conversation-meta[_ngcontent-%COMP%]{text-align:right;flex-shrink:0}.conversation-date[_ngcontent-%COMP%]{line-height:1.2}.conversation-preview[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem;line-height:1.4;margin-bottom:.75rem}.conversation-preview[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0}.conversation-footer[_ngcontent-%COMP%]{border-top:1px solid #f8f9fa;padding-top:.75rem}.conversation-stats[_ngcontent-%COMP%]{display:flex;align-items:center;flex-wrap:wrap;gap:.5rem}.conversation-actions[_ngcontent-%COMP%]{display:flex;align-items:center}.empty-state[_ngcontent-%COMP%]{padding:3rem 1rem}@media (max-width: 768px){.conversation-history-container[_ngcontent-%COMP%], .history-header[_ngcontent-%COMP%]{padding:1rem}.filters-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{--bs-gutter-x: .75rem}.conversation-card[_ngcontent-%COMP%]{padding:.75rem}.conversation-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.5rem}.conversation-meta[_ngcontent-%COMP%]{text-align:left;width:100%}.conversation-type-icon[_ngcontent-%COMP%]{width:32px;height:32px;margin-right:.5rem}.conversation-stats[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.25rem}}.fa-spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fa-spin 1s infinite linear}@keyframes _ngcontent-%COMP%_fa-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.badge[_ngcontent-%COMP%]{font-size:.75rem}.badge.bg-light[_ngcontent-%COMP%]{color:#495057!important}.input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}@media (prefers-color-scheme: dark){.conversation-history-container[_ngcontent-%COMP%]{background:#1a1a1a}.history-header[_ngcontent-%COMP%], .history-content[_ngcontent-%COMP%], .conversation-card[_ngcontent-%COMP%]{background:#2d2d2d;border-color:#404040;color:#e9ecef}.conversation-card[_ngcontent-%COMP%]:hover{border-color:#0d6efd;background:#343a40}.conversation-title[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#e9ecef}.conversation-preview[_ngcontent-%COMP%]{color:#adb5bd}.conversation-footer[_ngcontent-%COMP%]{border-top-color:#404040}.badge.bg-light[_ngcontent-%COMP%]{background-color:#495057!important;color:#e9ecef!important}}\"]\n      });\n    }\n  }\n  return ConversationHistoryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}