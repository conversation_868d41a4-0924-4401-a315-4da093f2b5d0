{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let InsuranceService = /*#__PURE__*/(() => {\n  class InsuranceService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/api/insurance`;\n      this.currentCoverage$ = new BehaviorSubject(null);\n    }\n    // Check eligibility for a service type\n    checkEligibility(serviceType) {\n      return this.http.get(`${this.apiUrl}/eligibility/${serviceType}`).pipe(catchError(error => {\n        console.error('Error checking insurance eligibility:', error);\n        throw error;\n      }));\n    }\n    // Check patient eligibility (for doctors)\n    checkPatientEligibility(serviceType, patientId) {\n      return this.http.get(`${this.apiUrl}/eligibility/${serviceType}/patient/${patientId}`).pipe(catchError(error => {\n        console.error('Error checking patient insurance eligibility:', error);\n        throw error;\n      }));\n    }\n    // Get supported insurance providers\n    getSupportedProviders() {\n      return this.http.get(`${this.apiUrl}/providers`).pipe(catchError(error => {\n        console.error('Error getting supported providers:', error);\n        throw error;\n      }));\n    }\n    // Verify coverage for a service\n    verifyCoverage(serviceType, patientId) {\n      const body = {\n        serviceType\n      };\n      if (patientId) {\n        body.patientId = patientId;\n      }\n      return this.http.post(`${this.apiUrl}/verify-coverage`, body).pipe(catchError(error => {\n        console.error('Error verifying coverage:', error);\n        throw error;\n      }));\n    }\n    // Get coverage summary for patient\n    getCoverageSummary() {\n      return this.http.get(`${this.apiUrl}/coverage-summary`).pipe(map(summary => {\n        this.currentCoverage$.next(summary);\n        return summary;\n      }), catchError(error => {\n        console.error('Error getting coverage summary:', error);\n        throw error;\n      }));\n    }\n    // Get current coverage summary (cached)\n    getCurrentCoverage() {\n      return this.currentCoverage$.asObservable();\n    }\n    // Estimate cost for a service\n    estimateCost(serviceType, baseCost, patientId) {\n      const body = {\n        serviceType,\n        baseCost\n      };\n      if (patientId) {\n        body.patientId = patientId;\n      }\n      return this.http.post(`${this.apiUrl}/estimate-cost`, body).pipe(catchError(error => {\n        console.error('Error estimating cost:', error);\n        throw error;\n      }));\n    }\n    // Get provider information\n    getProviderInfo(providerName) {\n      return this.http.get(`${this.apiUrl}/provider-info/${providerName}`).pipe(catchError(error => {\n        console.error('Error getting provider info:', error);\n        throw error;\n      }));\n    }\n    // Health check\n    healthCheck() {\n      return this.http.get(`${this.apiUrl}/health`).pipe(catchError(error => {\n        console.error('Insurance service health check failed:', error);\n        throw error;\n      }));\n    }\n    // Utility methods\n    formatCoverage(coveragePercentage) {\n      return `${Math.round(coveragePercentage * 100)}%`;\n    }\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n      }).format(amount);\n    }\n    getCoverageLevel(coveragePercentage) {\n      if (coveragePercentage >= 0.8) return 'excellent';\n      if (coveragePercentage >= 0.6) return 'good';\n      if (coveragePercentage >= 0.4) return 'fair';\n      if (coveragePercentage >= 0.2) return 'poor';\n      return 'none';\n    }\n    getCoverageLevelColor(coveragePercentage) {\n      const level = this.getCoverageLevel(coveragePercentage);\n      switch (level) {\n        case 'excellent':\n          return 'success';\n        case 'good':\n          return 'info';\n        case 'fair':\n          return 'warning';\n        case 'poor':\n          return 'danger';\n        default:\n          return 'secondary';\n      }\n    }\n    isEligibleForService(eligibility) {\n      return eligibility.eligible && eligibility.coveragePercentage > 0;\n    }\n    calculateSavings(baseCost, insuranceCoverage) {\n      return baseCost - insuranceCoverage;\n    }\n    getRecommendation(eligibility) {\n      if (!eligibility.eligible) {\n        return 'This service is not covered by your insurance plan.';\n      }\n      const coverage = eligibility.coveragePercentage;\n      if (coverage >= 0.8) {\n        return 'Excellent coverage! Most costs will be covered.';\n      } else if (coverage >= 0.6) {\n        return 'Good coverage. You\\'ll have moderate out-of-pocket costs.';\n      } else if (coverage >= 0.4) {\n        return 'Fair coverage. Consider budgeting for significant out-of-pocket costs.';\n      } else if (coverage > 0) {\n        return 'Limited coverage. Most costs will be out-of-pocket.';\n      } else {\n        return 'No coverage available for this service.';\n      }\n    }\n    // Mock data for development/testing\n    getMockCoverageSummary() {\n      return {\n        patientId: 1,\n        patientName: 'John Doe',\n        prescriptionCoverage: {\n          eligible: true,\n          coveragePercentage: 0.75,\n          reason: 'Coverage verified',\n          effectiveDate: '2024-01-01T00:00:00',\n          expirationDate: '2024-12-31T23:59:59'\n        },\n        consultationCoverage: {\n          eligible: true,\n          coveragePercentage: 0.80,\n          reason: 'Coverage verified',\n          effectiveDate: '2024-01-01T00:00:00',\n          expirationDate: '2024-12-31T23:59:59'\n        },\n        appointmentCoverage: {\n          eligible: true,\n          coveragePercentage: 0.85,\n          reason: 'Coverage verified',\n          effectiveDate: '2024-01-01T00:00:00',\n          expirationDate: '2024-12-31T23:59:59'\n        },\n        lastUpdated: Date.now()\n      };\n    }\n    getMockProviders() {\n      return [{\n        name: 'Blue Cross Blue Shield',\n        consultationCoverage: 0.80,\n        prescriptionCoverage: 0.70,\n        appointmentCoverage: 0.90\n      }, {\n        name: 'Aetna',\n        consultationCoverage: 0.75,\n        prescriptionCoverage: 0.65,\n        appointmentCoverage: 0.85\n      }, {\n        name: 'Cigna',\n        consultationCoverage: 0.78,\n        prescriptionCoverage: 0.68,\n        appointmentCoverage: 0.88\n      }, {\n        name: 'United Healthcare',\n        consultationCoverage: 0.82,\n        prescriptionCoverage: 0.72,\n        appointmentCoverage: 0.92\n      }, {\n        name: 'Humana',\n        consultationCoverage: 0.76,\n        prescriptionCoverage: 0.66,\n        appointmentCoverage: 0.86\n      }];\n    }\n    static {\n      this.ɵfac = function InsuranceService_Factory(t) {\n        return new (t || InsuranceService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: InsuranceService,\n        factory: InsuranceService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return InsuranceService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}