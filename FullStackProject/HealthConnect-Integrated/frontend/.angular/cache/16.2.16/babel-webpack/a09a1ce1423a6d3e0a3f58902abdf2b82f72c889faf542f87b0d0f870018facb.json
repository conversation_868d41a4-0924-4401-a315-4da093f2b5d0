{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/internationalization.service\";\nimport * as i2 from \"@angular/common\";\nfunction LanguageSelectorComponent_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 8);\n    i0.ɵɵlistener(\"click\", function LanguageSelectorComponent_li_10_Template_a_click_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const language_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.onLanguageChange(language_r1.code);\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵelementStart(2, \"span\", 3);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 9);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const language_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", language_r1.code === ctx_r0.currentLanguage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r1.flag);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", language_r1.code.toUpperCase(), \")\");\n  }\n}\nexport let LanguageSelectorComponent = /*#__PURE__*/(() => {\n  class LanguageSelectorComponent {\n    constructor(i18nService) {\n      this.i18nService = i18nService;\n      this.destroy$ = new Subject();\n      this.currentLanguage = 'en';\n      this.supportedLanguages = [];\n      this.isDropdownOpen = false;\n    }\n    ngOnInit() {\n      this.supportedLanguages = this.i18nService.getSupportedLanguages();\n      this.i18nService.getCurrentLanguage().pipe(takeUntil(this.destroy$)).subscribe(language => {\n        this.currentLanguage = language;\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    onLanguageChange(languageCode) {\n      this.i18nService.setLanguage(languageCode);\n      this.isDropdownOpen = false;\n    }\n    toggleDropdown() {\n      this.isDropdownOpen = !this.isDropdownOpen;\n    }\n    getCurrentLanguageInfo() {\n      return this.supportedLanguages.find(lang => lang.code === this.currentLanguage);\n    }\n    onClickOutside() {\n      this.isDropdownOpen = false;\n    }\n    static {\n      this.ɵfac = function LanguageSelectorComponent_Factory(t) {\n        return new (t || LanguageSelectorComponent)(i0.ɵɵdirectiveInject(i1.InternationalizationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LanguageSelectorComponent,\n        selectors: [[\"app-language-selector\"]],\n        decls: 11,\n        vars: 7,\n        consts: [[1, \"language-selector\", 3, \"clickOutside\"], [1, \"dropdown\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"language-btn\", 3, \"click\"], [1, \"flag\"], [1, \"language-name\", \"d-none\", \"d-md-inline\"], [1, \"language-code\", \"d-md-none\"], [1, \"dropdown-menu\"], [4, \"ngFor\", \"ngForOf\"], [\"href\", \"#\", 1, \"dropdown-item\", 3, \"click\"], [1, \"language-name\"], [1, \"language-code\"]],\n        template: function LanguageSelectorComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵlistener(\"clickOutside\", function LanguageSelectorComponent_Template_div_clickOutside_0_listener() {\n              return ctx.onClickOutside();\n            });\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function LanguageSelectorComponent_Template_button_click_2_listener() {\n              return ctx.toggleDropdown();\n            });\n            i0.ɵɵelementStart(3, \"span\", 3);\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"span\", 4);\n            i0.ɵɵtext(6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"span\", 5);\n            i0.ɵɵtext(8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"ul\", 6);\n            i0.ɵɵtemplate(10, LanguageSelectorComponent_li_10_Template, 8, 5, \"li\", 7);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_2_0;\n            i0.ɵɵadvance(2);\n            i0.ɵɵattribute(\"aria-expanded\", ctx.isDropdownOpen);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(((tmp_1_0 = ctx.getCurrentLanguageInfo()) == null ? null : tmp_1_0.flag) || \"\\uD83C\\uDF10\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(((tmp_2_0 = ctx.getCurrentLanguageInfo()) == null ? null : tmp_2_0.name) || \"Language\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.currentLanguage.toUpperCase());\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"show\", ctx.isDropdownOpen);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.supportedLanguages);\n          }\n        },\n        dependencies: [i2.NgForOf],\n        styles: [\".language-selector[_ngcontent-%COMP%]{position:relative}.language-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;border:1px solid #dee2e6;background:white;color:#495057;padding:.375rem .75rem;border-radius:.375rem;font-size:.875rem;transition:all .15s ease-in-out}.language-btn[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;border-color:#adb5bd}.language-btn[_ngcontent-%COMP%]:focus{outline:0;box-shadow:0 0 0 .2rem #007bff40}.flag[_ngcontent-%COMP%]{font-size:1.1em;line-height:1}.language-name[_ngcontent-%COMP%]{font-weight:500}.language-code[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d}.dropdown-menu[_ngcontent-%COMP%]{min-width:200px;border:1px solid #dee2e6;border-radius:.375rem;box-shadow:0 .5rem 1rem #00000026;z-index:1050}.dropdown-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.5rem 1rem;color:#212529;text-decoration:none;transition:background-color .15s ease-in-out}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;color:#16181b}.dropdown-item.active[_ngcontent-%COMP%]{background-color:#007bff;color:#fff}.dropdown-item.active[_ngcontent-%COMP%]   .language-code[_ngcontent-%COMP%]{color:#fffc}.dropdown-item[_ngcontent-%COMP%]   .language-name[_ngcontent-%COMP%]{flex:1;font-weight:500}.dropdown-item[_ngcontent-%COMP%]   .language-code[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d}@media (max-width: 768px){.language-btn[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.8rem}.dropdown-menu[_ngcontent-%COMP%]{min-width:150px}.dropdown-item[_ngcontent-%COMP%]{padding:.4rem .8rem;font-size:.85rem}}.dropdown-menu[_ngcontent-%COMP%]{opacity:0;transform:translateY(-10px);transition:opacity .15s ease,transform .15s ease;pointer-events:none}.dropdown-menu.show[_ngcontent-%COMP%]{opacity:1;transform:translateY(0);pointer-events:auto}\"]\n      });\n    }\n  }\n  return LanguageSelectorComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}