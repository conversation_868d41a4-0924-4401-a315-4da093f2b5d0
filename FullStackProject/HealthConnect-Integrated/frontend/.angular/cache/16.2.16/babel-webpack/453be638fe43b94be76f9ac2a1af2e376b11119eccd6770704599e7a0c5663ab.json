{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport var ConversationType = /*#__PURE__*/function (ConversationType) {\n  ConversationType[\"GENERAL_HEALTH\"] = \"GENERAL_HEALTH\";\n  ConversationType[\"SYMPTOM_ANALYSIS\"] = \"SYMPTOM_ANALYSIS\";\n  ConversationType[\"MEDICATION_INQUIRY\"] = \"MEDICATION_INQUIRY\";\n  ConversationType[\"WELLNESS_TIPS\"] = \"WELLNESS_TIPS\";\n  ConversationType[\"EMERGENCY_GUIDANCE\"] = \"EMERGENCY_GUIDANCE\";\n  return ConversationType;\n}(ConversationType || {});\nexport let AiHealthBotService = /*#__PURE__*/(() => {\n  class AiHealthBotService {\n    constructor(http, authService) {\n      this.http = http;\n      this.authService = authService;\n      this.apiUrl = `${environment.apiUrl}/ai-health-bot`;\n      this.currentConversationSubject = new BehaviorSubject(null);\n      this.currentConversation$ = this.currentConversationSubject.asObservable();\n    }\n    getHttpOptions() {\n      const token = this.authService.getToken();\n      return {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        })\n      };\n    }\n    sendMessage(request) {\n      return this.http.post(`${this.apiUrl}/chat`, request, this.getHttpOptions());\n    }\n    getUserConversations() {\n      return this.http.get(`${this.apiUrl}/conversations`, this.getHttpOptions());\n    }\n    getUserConversationsPaginated(page = 0, size = 10) {\n      return this.http.get(`${this.apiUrl}/conversations/paginated?page=${page}&size=${size}`, this.getHttpOptions());\n    }\n    getConversationDetails(conversationId) {\n      return this.http.get(`${this.apiUrl}/conversations/${conversationId}`, this.getHttpOptions());\n    }\n    setCurrentConversation(conversation) {\n      this.currentConversationSubject.next(conversation);\n    }\n    getCurrentConversation() {\n      return this.currentConversationSubject.value;\n    }\n    getConversationTypeDisplayName(type) {\n      switch (type) {\n        case ConversationType.GENERAL_HEALTH:\n          return 'General Health';\n        case ConversationType.SYMPTOM_ANALYSIS:\n          return 'Symptom Analysis';\n        case ConversationType.MEDICATION_INQUIRY:\n          return 'Medication Inquiry';\n        case ConversationType.WELLNESS_TIPS:\n          return 'Wellness Tips';\n        case ConversationType.EMERGENCY_GUIDANCE:\n          return 'Emergency Guidance';\n        default:\n          return 'General Health';\n      }\n    }\n    getConversationTypeIcon(type) {\n      switch (type) {\n        case ConversationType.GENERAL_HEALTH:\n          return 'heart';\n        case ConversationType.SYMPTOM_ANALYSIS:\n          return 'activity';\n        case ConversationType.MEDICATION_INQUIRY:\n          return 'pill';\n        case ConversationType.WELLNESS_TIPS:\n          return 'sun';\n        case ConversationType.EMERGENCY_GUIDANCE:\n          return 'alert-triangle';\n        default:\n          return 'heart';\n      }\n    }\n    healthCheck() {\n      return this.http.get(`${this.apiUrl}/health`, this.getHttpOptions());\n    }\n    static {\n      this.ɵfac = function AiHealthBotService_Factory(t) {\n        return new (t || AiHealthBotService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AiHealthBotService,\n        factory: AiHealthBotService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AiHealthBotService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}