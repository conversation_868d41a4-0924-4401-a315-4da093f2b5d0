{"ast": null, "code": "import { BehaviorSubject, of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let InternationalizationService = /*#__PURE__*/(() => {\n  class InternationalizationService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/api/i18n`;\n      this.currentLanguage$ = new BehaviorSubject('en');\n      this.translations$ = new BehaviorSubject({});\n      this.supportedLanguages = [{\n        code: 'en',\n        name: 'English',\n        flag: '🇺🇸'\n      }, {\n        code: 'es',\n        name: 'Español',\n        flag: '🇪🇸'\n      }, {\n        code: 'fr',\n        name: 'Français',\n        flag: '🇫🇷'\n      }, {\n        code: 'de',\n        name: 'Deutsch',\n        flag: '🇩🇪'\n      }, {\n        code: 'pt',\n        name: 'Portug<PERSON><PERSON><PERSON>',\n        flag: '🇵🇹'\n      }];\n      this.initializeLanguage();\n    }\n    initializeLanguage() {\n      // Get language from localStorage or browser preference\n      const savedLanguage = localStorage.getItem('healthconnect_language');\n      const browserLanguage = navigator.language.split('-')[0];\n      const defaultLanguage = savedLanguage || this.supportedLanguages.find(lang => lang.code === browserLanguage)?.code || 'en';\n      this.setLanguage(defaultLanguage);\n    }\n    getCurrentLanguage() {\n      return this.currentLanguage$.asObservable();\n    }\n    getCurrentLanguageValue() {\n      return this.currentLanguage$.value;\n    }\n    getTranslations() {\n      return this.translations$.asObservable();\n    }\n    getSupportedLanguages() {\n      return this.supportedLanguages;\n    }\n    setLanguage(languageCode) {\n      if (this.supportedLanguages.find(lang => lang.code === languageCode)) {\n        this.currentLanguage$.next(languageCode);\n        localStorage.setItem('healthconnect_language', languageCode);\n        this.loadTranslations(languageCode);\n      }\n    }\n    loadTranslations(languageCode) {\n      this.getTranslationsFromServer(languageCode).subscribe({\n        next: response => {\n          this.translations$.next(response.translations);\n        },\n        error: error => {\n          console.error('Failed to load translations:', error);\n          // Fallback to default translations\n          this.loadDefaultTranslations();\n        }\n      });\n    }\n    getTranslationsFromServer(languageCode) {\n      return this.http.get(`${this.apiUrl}/translations/${languageCode}`).pipe(catchError(error => {\n        console.error('Error fetching translations from server:', error);\n        return of(this.getDefaultTranslations(languageCode));\n      }));\n    }\n    translate(key, params) {\n      return this.translations$.pipe(map(translations => {\n        let translation = translations[key] || key;\n        // Replace parameters if provided\n        if (params) {\n          Object.keys(params).forEach(paramKey => {\n            translation = translation.replace(`{${paramKey}}`, params[paramKey]);\n          });\n        }\n        return translation;\n      }));\n    }\n    translateSync(key, params) {\n      const translations = this.translations$.value;\n      let translation = translations[key] || key;\n      // Replace parameters if provided\n      if (params) {\n        Object.keys(params).forEach(paramKey => {\n          translation = translation.replace(`{${paramKey}}`, params[paramKey]);\n        });\n      }\n      return translation;\n    }\n    translateBatch(keys) {\n      return this.http.post(`${this.apiUrl}/translate/batch`, {\n        keys,\n        language: this.getCurrentLanguageValue()\n      }).pipe(map(response => response.translations), catchError(error => {\n        console.error('Error in batch translation:', error);\n        // Fallback to current translations\n        const currentTranslations = this.translations$.value;\n        const result = {};\n        keys.forEach(key => {\n          result[key] = currentTranslations[key] || key;\n        });\n        return of(result);\n      }));\n    }\n    detectLanguage(text) {\n      return this.http.post(`${this.apiUrl}/detect-language`, {\n        text\n      }).pipe(catchError(error => {\n        console.error('Error detecting language:', error);\n        return of({\n          detectedLanguage: 'en',\n          confidence: 0.5\n        });\n      }));\n    }\n    formatMessage(messageKey, parameters) {\n      return this.http.post(`${this.apiUrl}/format-message`, {\n        messageKey,\n        language: this.getCurrentLanguageValue(),\n        parameters\n      }).pipe(map(response => response.formattedMessage), catchError(error => {\n        console.error('Error formatting message:', error);\n        return this.translate(messageKey);\n      }));\n    }\n    loadDefaultTranslations() {\n      const defaultTranslations = this.getDefaultTranslations(this.getCurrentLanguageValue());\n      this.translations$.next(defaultTranslations.translations);\n    }\n    getDefaultTranslations(languageCode) {\n      const translations = {\n        'en': {\n          'welcome': 'Welcome to HealthConnect',\n          'login': 'Login',\n          'logout': 'Logout',\n          'register': 'Register',\n          'dashboard': 'Dashboard',\n          'appointments': 'Appointments',\n          'prescriptions': 'Prescriptions',\n          'chat': 'Chat',\n          'video_consultation': 'Video Consultation',\n          'profile': 'Profile',\n          'settings': 'Settings',\n          'doctor': 'Doctor',\n          'patient': 'Patient',\n          'success': 'Success',\n          'error': 'Error',\n          'cancel': 'Cancel',\n          'save': 'Save',\n          'delete': 'Delete',\n          'edit': 'Edit',\n          'view': 'View',\n          'search': 'Search',\n          'loading': 'Loading...',\n          'no_data': 'No data available',\n          'confirm': 'Confirm',\n          'yes': 'Yes',\n          'no': 'No'\n        },\n        'es': {\n          'welcome': 'Bienvenido a HealthConnect',\n          'login': 'Iniciar Sesión',\n          'logout': 'Cerrar Sesión',\n          'register': 'Registrarse',\n          'dashboard': 'Panel de Control',\n          'appointments': 'Citas',\n          'prescriptions': 'Recetas',\n          'chat': 'Chat',\n          'video_consultation': 'Consulta por Video',\n          'profile': 'Perfil',\n          'settings': 'Configuración',\n          'doctor': 'Doctor',\n          'patient': 'Paciente',\n          'success': 'Éxito',\n          'error': 'Error',\n          'cancel': 'Cancelar',\n          'save': 'Guardar',\n          'delete': 'Eliminar',\n          'edit': 'Editar',\n          'view': 'Ver',\n          'search': 'Buscar',\n          'loading': 'Cargando...',\n          'no_data': 'No hay datos disponibles',\n          'confirm': 'Confirmar',\n          'yes': 'Sí',\n          'no': 'No'\n        },\n        'fr': {\n          'welcome': 'Bienvenue à HealthConnect',\n          'login': 'Connexion',\n          'logout': 'Déconnexion',\n          'register': 'S\\'inscrire',\n          'dashboard': 'Tableau de Bord',\n          'appointments': 'Rendez-vous',\n          'prescriptions': 'Ordonnances',\n          'chat': 'Chat',\n          'video_consultation': 'Consultation Vidéo',\n          'profile': 'Profil',\n          'settings': 'Paramètres',\n          'doctor': 'Docteur',\n          'patient': 'Patient',\n          'success': 'Succès',\n          'error': 'Erreur',\n          'cancel': 'Annuler',\n          'save': 'Sauvegarder',\n          'delete': 'Supprimer',\n          'edit': 'Modifier',\n          'view': 'Voir',\n          'search': 'Rechercher',\n          'loading': 'Chargement...',\n          'no_data': 'Aucune donnée disponible',\n          'confirm': 'Confirmer',\n          'yes': 'Oui',\n          'no': 'Non'\n        }\n      };\n      return {\n        language: languageCode,\n        translations: translations[languageCode] || translations['en'],\n        count: Object.keys(translations[languageCode] || translations['en']).length\n      };\n    }\n    // Utility method to get language name by code\n    getLanguageName(code) {\n      const language = this.supportedLanguages.find(lang => lang.code === code);\n      return language ? language.name : code;\n    }\n    // Utility method to get language flag by code\n    getLanguageFlag(code) {\n      const language = this.supportedLanguages.find(lang => lang.code === code);\n      return language?.flag || '🌐';\n    }\n    static {\n      this.ɵfac = function InternationalizationService_Factory(t) {\n        return new (t || InternationalizationService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: InternationalizationService,\n        factory: InternationalizationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return InternationalizationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}