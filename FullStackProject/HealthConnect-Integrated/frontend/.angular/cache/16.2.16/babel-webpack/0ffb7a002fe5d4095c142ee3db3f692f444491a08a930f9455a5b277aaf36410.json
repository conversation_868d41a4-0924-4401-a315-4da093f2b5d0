{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AiHealthBotRoutingModule } from './ai-health-bot-routing.module';\nimport { SharedModule } from '../shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let AiHealthBotModule = /*#__PURE__*/(() => {\n  class AiHealthBotModule {\n    static {\n      this.ɵfac = function AiHealthBotModule_Factory(t) {\n        return new (t || AiHealthBotModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AiHealthBotModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, FormsModule, ReactiveFormsModule, AiHealthBotRoutingModule, SharedModule]\n      });\n    }\n  }\n  return AiHealthBotModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}