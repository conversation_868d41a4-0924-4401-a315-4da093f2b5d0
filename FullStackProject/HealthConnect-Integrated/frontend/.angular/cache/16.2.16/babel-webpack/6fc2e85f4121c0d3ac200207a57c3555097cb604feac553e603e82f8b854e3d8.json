{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from '../core/guards/auth.guard';\nimport { AiChatComponent } from './ai-chat/ai-chat.component';\nimport { ConversationHistoryComponent } from './conversation-history/conversation-history.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    redirectTo: 'chat',\n    pathMatch: 'full'\n  }, {\n    path: 'chat',\n    component: AiChatComponent,\n    data: {\n      title: 'AI Health Assistant'\n    }\n  }, {\n    path: 'chat/:conversationId',\n    component: AiChatComponent,\n    data: {\n      title: 'AI Health Assistant'\n    }\n  }, {\n    path: 'history',\n    component: ConversationHistoryComponent,\n    data: {\n      title: 'Conversation History'\n    }\n  }]\n}];\nexport let AiHealthBotRoutingModule = /*#__PURE__*/(() => {\n  class AiHealthBotRoutingModule {\n    static {\n      this.ɵfac = function AiHealthBotRoutingModule_Factory(t) {\n        return new (t || AiHealthBotRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AiHealthBotRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return AiHealthBotRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}