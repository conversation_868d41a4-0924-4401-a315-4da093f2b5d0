{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/insurance.service\";\nimport * as i2 from \"../../../core/services/internationalization.service\";\nimport * as i3 from \"@angular/common\";\nfunction InsuranceCoverageComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"h5\", 6);\n    i0.ɵɵelement(2, \"i\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function InsuranceCoverageComponent_div_1_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.refresh());\n    });\n    i0.ɵɵelement(5, \"i\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.i18nService.translateSync(\"insurance\"), \" \", ctx_r0.i18nService.translateSync(\"coverage\"), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"fa-spin\", ctx_r0.loading);\n  }\n}\nfunction InsuranceCoverageComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"span\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.i18nService.translateSync(\"loading\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.i18nService.translateSync(\"loading\"));\n  }\n}\nfunction InsuranceCoverageComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function InsuranceCoverageComponent_div_3_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.refresh());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.i18nService.translateSync(\"retry\"), \" \");\n  }\n}\nfunction InsuranceCoverageComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"small\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.coverageSummary.patientName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r8.i18nService.translateSync(\"patient\"), \" ID: \", ctx_r8.coverageSummary.patientId, \" \");\n  }\n}\nfunction InsuranceCoverageComponent_div_4_small_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getRecommendation(ctx_r9.coverageSummary.prescriptionCoverage), \" \");\n  }\n}\nfunction InsuranceCoverageComponent_div_4_small_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getRecommendation(ctx_r10.coverageSummary.consultationCoverage), \" \");\n  }\n}\nfunction InsuranceCoverageComponent_div_4_small_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getRecommendation(ctx_r11.coverageSummary.appointmentCoverage), \" \");\n  }\n}\nfunction InsuranceCoverageComponent_div_4_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"small\", 38);\n    i0.ɵɵelement(2, \"i\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r12.i18nService.translateSync(\"last_updated\"), \": \", i0.ɵɵpipeBind2(4, 2, ctx_r12.coverageSummary.lastUpdated, \"medium\"), \" \");\n  }\n}\nfunction InsuranceCoverageComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, InsuranceCoverageComponent_div_4_div_1_Template, 8, 3, \"div\", 18);\n    i0.ɵɵelementStart(2, \"div\", 19)(3, \"div\", 20)(4, \"div\", 21)(5, \"div\", 22)(6, \"div\", 23);\n    i0.ɵɵelement(7, \"i\", 24);\n    i0.ɵɵelementStart(8, \"h6\", 6);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 25)(11, \"div\", 26)(12, \"span\", 27);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 28);\n    i0.ɵɵelement(16, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, InsuranceCoverageComponent_div_4_small_17_Template, 2, 1, \"small\", 30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 20)(19, \"div\", 21)(20, \"div\", 22)(21, \"div\", 23);\n    i0.ɵɵelement(22, \"i\", 31);\n    i0.ɵɵelementStart(23, \"h6\", 6);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 25)(26, \"div\", 26)(27, \"span\", 27);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 28);\n    i0.ɵɵelement(31, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, InsuranceCoverageComponent_div_4_small_32_Template, 2, 1, \"small\", 30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(33, \"div\", 20)(34, \"div\", 21)(35, \"div\", 22)(36, \"div\", 23);\n    i0.ɵɵelement(37, \"i\", 32);\n    i0.ɵɵelementStart(38, \"h6\", 6);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 25)(41, \"div\", 26)(42, \"span\", 27);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 28);\n    i0.ɵɵelement(46, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, InsuranceCoverageComponent_div_4_small_47_Template, 2, 1, \"small\", 30);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(48, InsuranceCoverageComponent_div_4_div_48_Template, 5, 5, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.compact);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r3.getCoverageText(\"prescription\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(\"text-\" + ctx_r3.getCoverageLevelColor(ctx_r3.coverageSummary.prescriptionCoverage));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.formatCoverage(ctx_r3.coverageSummary.prescriptionCoverage), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"text-\" + ctx_r3.getCoverageLevelColor(ctx_r3.coverageSummary.prescriptionCoverage));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r3.getProgressBarClass(ctx_r3.coverageSummary.prescriptionCoverage));\n    i0.ɵɵstyleProp(\"width\", ctx_r3.getProgressBarWidth(ctx_r3.coverageSummary.prescriptionCoverage), \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.compact);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.getCoverageText(\"consultation\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(\"text-\" + ctx_r3.getCoverageLevelColor(ctx_r3.coverageSummary.consultationCoverage));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.formatCoverage(ctx_r3.coverageSummary.consultationCoverage), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"text-\" + ctx_r3.getCoverageLevelColor(ctx_r3.coverageSummary.consultationCoverage));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r3.getProgressBarClass(ctx_r3.coverageSummary.consultationCoverage));\n    i0.ɵɵstyleProp(\"width\", ctx_r3.getProgressBarWidth(ctx_r3.coverageSummary.consultationCoverage), \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.compact);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.getCoverageText(\"appointment\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(\"text-\" + ctx_r3.getCoverageLevelColor(ctx_r3.coverageSummary.appointmentCoverage));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.formatCoverage(ctx_r3.coverageSummary.appointmentCoverage), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"text-\" + ctx_r3.getCoverageLevelColor(ctx_r3.coverageSummary.appointmentCoverage));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r3.getProgressBarClass(ctx_r3.coverageSummary.appointmentCoverage));\n    i0.ɵɵstyleProp(\"width\", ctx_r3.getProgressBarWidth(ctx_r3.coverageSummary.appointmentCoverage), \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.compact);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.compact);\n  }\n}\nexport let InsuranceCoverageComponent = /*#__PURE__*/(() => {\n  class InsuranceCoverageComponent {\n    constructor(insuranceService, i18nService) {\n      this.insuranceService = insuranceService;\n      this.i18nService = i18nService;\n      this.destroy$ = new Subject();\n      this.showTitle = true;\n      this.compact = false;\n      this.coverageSummary = null;\n      this.loading = false;\n      this.error = null;\n    }\n    ngOnInit() {\n      this.loadCoverageSummary();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadCoverageSummary() {\n      this.loading = true;\n      this.error = null;\n      this.insuranceService.getCoverageSummary().pipe(takeUntil(this.destroy$), finalize(() => this.loading = false)).subscribe({\n        next: summary => {\n          this.coverageSummary = summary;\n        },\n        error: error => {\n          console.error('Error loading coverage summary:', error);\n          this.error = 'Failed to load insurance coverage information';\n          // Use mock data for development\n          this.coverageSummary = this.insuranceService.getMockCoverageSummary();\n        }\n      });\n    }\n    getCoverageLevel(eligibility) {\n      return this.insuranceService.getCoverageLevel(eligibility.coveragePercentage);\n    }\n    getCoverageLevelColor(eligibility) {\n      return this.insuranceService.getCoverageLevelColor(eligibility.coveragePercentage);\n    }\n    formatCoverage(eligibility) {\n      return this.insuranceService.formatCoverage(eligibility.coveragePercentage);\n    }\n    getRecommendation(eligibility) {\n      return this.insuranceService.getRecommendation(eligibility);\n    }\n    isEligible(eligibility) {\n      return this.insuranceService.isEligibleForService(eligibility);\n    }\n    refresh() {\n      this.loadCoverageSummary();\n    }\n    getCoverageIcon(eligibility) {\n      if (!eligibility.eligible) return 'fas fa-times-circle';\n      const level = this.getCoverageLevel(eligibility);\n      switch (level) {\n        case 'excellent':\n          return 'fas fa-check-circle';\n        case 'good':\n          return 'fas fa-check-circle';\n        case 'fair':\n          return 'fas fa-exclamation-triangle';\n        case 'poor':\n          return 'fas fa-exclamation-triangle';\n        default:\n          return 'fas fa-times-circle';\n      }\n    }\n    getCoverageText(serviceType) {\n      switch (serviceType) {\n        case 'prescription':\n          return this.i18nService.translateSync('prescriptions');\n        case 'consultation':\n          return this.i18nService.translateSync('video_consultation');\n        case 'appointment':\n          return this.i18nService.translateSync('appointments');\n        default:\n          return serviceType;\n      }\n    }\n    getProgressBarClass(eligibility) {\n      const level = this.getCoverageLevel(eligibility);\n      switch (level) {\n        case 'excellent':\n          return 'bg-success';\n        case 'good':\n          return 'bg-info';\n        case 'fair':\n          return 'bg-warning';\n        case 'poor':\n          return 'bg-danger';\n        default:\n          return 'bg-secondary';\n      }\n    }\n    getProgressBarWidth(eligibility) {\n      return Math.round(eligibility.coveragePercentage * 100);\n    }\n    static {\n      this.ɵfac = function InsuranceCoverageComponent_Factory(t) {\n        return new (t || InsuranceCoverageComponent)(i0.ɵɵdirectiveInject(i1.InsuranceService), i0.ɵɵdirectiveInject(i2.InternationalizationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: InsuranceCoverageComponent,\n        selectors: [[\"app-insurance-coverage\"]],\n        inputs: {\n          patientId: \"patientId\",\n          showTitle: \"showTitle\",\n          compact: \"compact\"\n        },\n        decls: 5,\n        vars: 6,\n        consts: [[1, \"insurance-coverage\"], [\"class\", \"d-flex justify-content-between align-items-center mb-3\", 4, \"ngIf\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"alert alert-warning\", 4, \"ngIf\"], [\"class\", \"coverage-summary\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"fas\", \"fa-shield-alt\", \"text-primary\", \"me-2\"], [\"title\", \"Refresh coverage information\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [1, \"alert\", \"alert-warning\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-warning\", \"ms-2\", 3, \"click\"], [1, \"coverage-summary\"], [\"class\", \"patient-info mb-3\", 4, \"ngIf\"], [1, \"row\", \"g-3\"], [1, \"col-md-4\"], [1, \"coverage-card\", \"card\", \"h-100\"], [1, \"card-body\"], [1, \"d-flex\", \"align-items-center\", \"mb-2\"], [1, \"fas\", \"fa-pills\", \"text-primary\", \"me-2\"], [1, \"coverage-details\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"coverage-percentage\", \"h5\", \"mb-0\"], [1, \"progress\", \"mb-2\", 2, \"height\", \"6px\"], [1, \"progress-bar\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"fas\", \"fa-video\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-calendar-check\", \"text-info\", \"me-2\"], [\"class\", \"text-center mt-3\", 4, \"ngIf\"], [1, \"patient-info\", \"mb-3\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-user-circle\", \"text-secondary\", \"me-2\"], [1, \"text-muted\", \"d-block\"], [1, \"text-muted\"], [1, \"text-center\", \"mt-3\"], [1, \"fas\", \"fa-clock\", \"me-1\"]],\n        template: function InsuranceCoverageComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, InsuranceCoverageComponent_div_1_Template, 6, 5, \"div\", 1);\n            i0.ɵɵtemplate(2, InsuranceCoverageComponent_div_2_Template, 6, 2, \"div\", 2);\n            i0.ɵɵtemplate(3, InsuranceCoverageComponent_div_3_Template, 5, 2, \"div\", 3);\n            i0.ɵɵtemplate(4, InsuranceCoverageComponent_div_4_Template, 49, 35, \"div\", 4);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"compact\", ctx.compact);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showTitle);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading && !ctx.coverageSummary);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.coverageSummary);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.coverageSummary);\n          }\n        },\n        dependencies: [i3.NgIf, i3.DatePipe],\n        styles: [\".insurance-coverage[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:.5rem;padding:1.5rem}.insurance-coverage.compact[_ngcontent-%COMP%]{padding:1rem;background:transparent}.coverage-card[_ngcontent-%COMP%]{border:none;box-shadow:0 .125rem .25rem #00000013;transition:transform .15s ease-in-out,box-shadow .15s ease-in-out}.coverage-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 .5rem 1rem #00000026}.coverage-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:1rem}.coverage-percentage[_ngcontent-%COMP%]{font-weight:600;font-size:1.25rem}.coverage-details[_ngcontent-%COMP%]{margin-top:.5rem}.progress[_ngcontent-%COMP%]{background-color:#e9ecef;border-radius:.25rem}.progress-bar[_ngcontent-%COMP%]{transition:width .6s ease}.patient-info[_ngcontent-%COMP%]{background:white;border-radius:.375rem;padding:1rem;border:1px solid #dee2e6}.text-excellent[_ngcontent-%COMP%]{color:#28a745!important}.text-good[_ngcontent-%COMP%]{color:#17a2b8!important}.text-fair[_ngcontent-%COMP%]{color:#ffc107!important}.text-poor[_ngcontent-%COMP%]{color:#dc3545!important}.text-none[_ngcontent-%COMP%]{color:#6c757d!important}@media (max-width: 768px){.insurance-coverage[_ngcontent-%COMP%]{padding:1rem}.coverage-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:.75rem}.coverage-percentage[_ngcontent-%COMP%]{font-size:1.1rem}.row.g-3[_ngcontent-%COMP%]{--bs-gutter-x: .75rem;--bs-gutter-y: .75rem}}.spinner-border[_ngcontent-%COMP%]{width:2rem;height:2rem}.insurance-coverage.compact[_ngcontent-%COMP%]   .coverage-card[_ngcontent-%COMP%]{margin-bottom:.5rem}.insurance-coverage.compact[_ngcontent-%COMP%]   .coverage-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:.75rem}.insurance-coverage.compact[_ngcontent-%COMP%]   .coverage-percentage[_ngcontent-%COMP%]{font-size:1rem}.insurance-coverage.compact[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-size:.875rem}.fas[_ngcontent-%COMP%]{width:1.2em;text-align:center}.alert[_ngcontent-%COMP%]{border:none;border-radius:.375rem}.btn-outline-secondary[_ngcontent-%COMP%]{border-color:#dee2e6}.btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;border-color:#adb5bd}@keyframes _ngcontent-%COMP%_progressAnimation{0%{width:0%}to{width:var(--progress-width)}}.progress-bar[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_progressAnimation 1s ease-in-out}.coverage-card[_ngcontent-%COMP%]{cursor:default;position:relative;overflow:hidden}.coverage-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.coverage-card[_ngcontent-%COMP%]:hover:before{left:100%}\"]\n      });\n    }\n  }\n  return InsuranceCoverageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}