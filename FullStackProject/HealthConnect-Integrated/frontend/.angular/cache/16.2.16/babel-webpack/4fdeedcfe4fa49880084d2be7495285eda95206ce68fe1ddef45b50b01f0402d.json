{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { ConversationType } from '../../core/services/ai-health-bot.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/ai-health-bot.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"messagesContainer\"];\nfunction AiChatComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementStart(3, \"span\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"small\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"fas fa-\", ctx_r0.getConversationTypeIcon(ctx_r0.currentConversation.conversationType), \" me-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getConversationTypeDisplayName(ctx_r0.currentConversation.conversationType));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentConversation.title);\n  }\n}\nfunction AiChatComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"span\", 33);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 34);\n    i0.ɵɵtext(5, \"Loading conversation...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AiChatComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementStart(3, \"h5\");\n    i0.ɵɵtext(4, \"Welcome to HealthConnect AI Assistant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 27);\n    i0.ɵɵtext(6, \"I'm here to help you with health-related questions and provide general medical guidance.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 38);\n    i0.ɵɵelement(8, \"i\", 39);\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10, \"Important:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" I provide general health information only. For medical emergencies or serious concerns, please consult healthcare professionals immediately. \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"ai-message\": a1\n  };\n};\nconst _c2 = function (a0, a1) {\n  return {\n    \"fa-user\": a0,\n    \"fa-robot\": a1\n  };\n};\nfunction AiChatComponent_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 45)(3, \"div\", 46);\n    i0.ɵɵelement(4, \"i\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 48)(6, \"span\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 50);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 51);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r11 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c1, message_r11.role === \"USER\", message_r11.role === \"ASSISTANT\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c2, message_r11.role === \"USER\", message_r11.role === \"ASSISTANT\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", message_r11.role === \"USER\" ? \"You\" : \"HealthConnect AI\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.formatTimestamp(message_r11.timestamp));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", message_r11.content, \" \");\n  }\n}\nfunction AiChatComponent_div_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 44)(2, \"div\", 45)(3, \"div\", 46);\n    i0.ɵɵelement(4, \"i\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 48)(6, \"span\", 49);\n    i0.ɵɵtext(7, \"HealthConnect AI\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 50);\n    i0.ɵɵtext(9, \"typing...\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 51)(11, \"div\", 54);\n    i0.ɵɵelement(12, \"span\")(13, \"span\")(14, \"span\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction AiChatComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, AiChatComponent_div_19_div_1_Template, 12, 11, \"div\", 41);\n    i0.ɵɵtemplate(2, AiChatComponent_div_19_div_2_Template, 15, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSending);\n  }\n}\nfunction AiChatComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.error, \" \");\n  }\n}\nfunction AiChatComponent_div_23_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r13 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", type_r13);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getConversationTypeDisplayName(type_r13), \" \");\n  }\n}\nfunction AiChatComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"label\", 58);\n    i0.ɵɵtext(2, \"Conversation Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 59);\n    i0.ɵɵlistener(\"change\", function AiChatComponent_div_23_Template_select_change_3_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onConversationTypeChange());\n    });\n    i0.ɵɵtemplate(4, AiChatComponent_div_23_option_4_Template, 2, 2, \"option\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.conversationTypes);\n  }\n}\nfunction AiChatComponent_i_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 62);\n  }\n}\nfunction AiChatComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"span\", 33);\n    i0.ɵɵtext(2, \"Sending...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let AiChatComponent = /*#__PURE__*/(() => {\n  class AiChatComponent {\n    constructor(fb, aiHealthBotService, route, router) {\n      this.fb = fb;\n      this.aiHealthBotService = aiHealthBotService;\n      this.route = route;\n      this.router = router;\n      this.currentConversation = null;\n      this.messages = [];\n      this.isLoading = false;\n      this.isSending = false;\n      this.error = '';\n      this.conversationTypes = Object.values(ConversationType);\n      this.selectedConversationType = ConversationType.GENERAL_HEALTH;\n      this.subscriptions = [];\n      this.shouldScrollToBottom = false;\n      this.chatForm = this.fb.group({\n        message: ['', [Validators.required, Validators.maxLength(2000)]],\n        conversationType: [ConversationType.GENERAL_HEALTH]\n      });\n    }\n    ngOnInit() {\n      this.loadConversationFromRoute();\n      this.subscribeToCurrentConversation();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    ngAfterViewChecked() {\n      if (this.shouldScrollToBottom) {\n        this.scrollToBottom();\n        this.shouldScrollToBottom = false;\n      }\n    }\n    loadConversationFromRoute() {\n      const conversationId = this.route.snapshot.paramMap.get('conversationId');\n      if (conversationId) {\n        this.loadConversation(+conversationId);\n      }\n    }\n    subscribeToCurrentConversation() {\n      const sub = this.aiHealthBotService.currentConversation$.subscribe(conversation => {\n        this.currentConversation = conversation;\n        if (conversation) {\n          this.messages = conversation.messages || [];\n          this.selectedConversationType = conversation.conversationType;\n          this.chatForm.patchValue({\n            conversationType: conversation.conversationType\n          });\n          this.shouldScrollToBottom = true;\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    loadConversation(conversationId) {\n      this.isLoading = true;\n      this.error = '';\n      const sub = this.aiHealthBotService.getConversationDetails(conversationId).subscribe({\n        next: conversation => {\n          this.aiHealthBotService.setCurrentConversation(conversation);\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Failed to load conversation:', error);\n          this.error = 'Failed to load conversation';\n          this.isLoading = false;\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    onSendMessage() {\n      if (this.chatForm.invalid || this.isSending) {\n        return;\n      }\n      const message = this.chatForm.get('message')?.value?.trim();\n      if (!message) {\n        return;\n      }\n      this.isSending = true;\n      this.error = '';\n      const request = {\n        message: message,\n        conversationId: this.currentConversation?.id,\n        conversationType: this.selectedConversationType,\n        isNewConversation: !this.currentConversation\n      };\n      // Add user message to UI immediately\n      const userMessage = {\n        id: Date.now(),\n        content: message,\n        role: 'USER',\n        timestamp: new Date().toISOString()\n      };\n      this.messages.push(userMessage);\n      this.shouldScrollToBottom = true;\n      // Clear the form\n      this.chatForm.patchValue({\n        message: ''\n      });\n      const sub = this.aiHealthBotService.sendMessage(request).subscribe({\n        next: response => {\n          // Add AI response to messages\n          const aiMessage = {\n            id: Date.now() + 1,\n            content: response.aiResponse,\n            role: 'ASSISTANT',\n            timestamp: response.timestamp\n          };\n          this.messages.push(aiMessage);\n          // Update current conversation\n          if (response.isNewConversation || !this.currentConversation) {\n            // Navigate to the new conversation\n            this.router.navigate(['/ai-health-bot/chat', response.conversationId]);\n          }\n          this.shouldScrollToBottom = true;\n          this.isSending = false;\n        },\n        error: error => {\n          console.error('Failed to send message:', error);\n          this.error = 'Failed to send message. Please try again.';\n          // Remove the user message that was added optimistically\n          this.messages.pop();\n          this.isSending = false;\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    onNewConversation() {\n      this.aiHealthBotService.setCurrentConversation(null);\n      this.messages = [];\n      this.selectedConversationType = ConversationType.GENERAL_HEALTH;\n      this.chatForm.patchValue({\n        message: '',\n        conversationType: ConversationType.GENERAL_HEALTH\n      });\n      this.router.navigate(['/ai-health-bot/chat']);\n    }\n    onConversationTypeChange() {\n      this.selectedConversationType = this.chatForm.get('conversationType')?.value;\n    }\n    getConversationTypeDisplayName(type) {\n      return this.aiHealthBotService.getConversationTypeDisplayName(type);\n    }\n    getConversationTypeIcon(type) {\n      return this.aiHealthBotService.getConversationTypeIcon(type);\n    }\n    scrollToBottom() {\n      try {\n        if (this.messagesContainer) {\n          this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\n        }\n      } catch (err) {\n        console.error('Error scrolling to bottom:', err);\n      }\n    }\n    formatTimestamp(timestamp) {\n      const date = new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    onKeyPress(event) {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.onSendMessage();\n      }\n    }\n    static {\n      this.ɵfac = function AiChatComponent_Factory(t) {\n        return new (t || AiChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AiHealthBotService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AiChatComponent,\n        selectors: [[\"app-ai-chat\"]],\n        viewQuery: function AiChatComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          }\n        },\n        decls: 32,\n        vars: 13,\n        consts: [[1, \"ai-chat-container\"], [1, \"chat-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-robot\", \"text-primary\", \"me-2\"], [1, \"mb-0\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [\"routerLink\", \"/ai-health-bot/history\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\"], [1, \"fas\", \"fa-history\", \"me-1\"], [\"class\", \"conversation-info mt-2\", 4, \"ngIf\"], [1, \"messages-container\"], [\"messagesContainer\", \"\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"welcome-message\", 4, \"ngIf\"], [\"class\", \"messages-list\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mx-3\", 4, \"ngIf\"], [1, \"chat-input\"], [3, \"formGroup\", \"ngSubmit\"], [\"class\", \"conversation-type-selector mb-2\", 4, \"ngIf\"], [1, \"input-group\"], [\"formControlName\", \"message\", \"placeholder\", \"Type your health question here...\", \"rows\", \"2\", 1, \"form-control\", 3, \"disabled\", \"keypress\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [1, \"text-end\", \"mt-1\"], [1, \"text-muted\"], [1, \"conversation-info\", \"mt-2\"], [1, \"d-flex\", \"align-items-center\", \"text-muted\"], [1, \"me-3\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\", \"text-muted\"], [1, \"welcome-message\"], [1, \"text-center\", \"py-5\"], [1, \"fas\", \"fa-robot\", \"fa-3x\", \"text-primary\", \"mb-3\"], [1, \"alert\", \"alert-info\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [1, \"messages-list\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"message ai-message\", 4, \"ngIf\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-header\"], [1, \"message-avatar\"], [1, \"fas\", 3, \"ngClass\"], [1, \"message-info\"], [1, \"message-sender\"], [1, \"message-time\"], [1, \"message-text\"], [1, \"message\", \"ai-message\"], [1, \"fas\", \"fa-robot\"], [1, \"typing-indicator\"], [1, \"alert\", \"alert-danger\", \"mx-3\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"conversation-type-selector\", \"mb-2\"], [1, \"form-label\", \"small\", \"text-muted\"], [\"formControlName\", \"conversationType\", 1, \"form-select\", \"form-select-sm\", 3, \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"fas\", \"fa-paper-plane\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"]],\n        template: function AiChatComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"i\", 4);\n            i0.ɵɵelementStart(5, \"h4\", 5);\n            i0.ɵɵtext(6, \"HealthConnect AI Assistant\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function AiChatComponent_Template_button_click_8_listener() {\n              return ctx.onNewConversation();\n            });\n            i0.ɵɵelement(9, \"i\", 8);\n            i0.ɵɵtext(10, \" New Chat \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"button\", 9);\n            i0.ɵɵelement(12, \"i\", 10);\n            i0.ɵɵtext(13, \" History \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(14, AiChatComponent_div_14_Template, 7, 5, \"div\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 12, 13);\n            i0.ɵɵtemplate(17, AiChatComponent_div_17_Template, 6, 0, \"div\", 14);\n            i0.ɵɵtemplate(18, AiChatComponent_div_18_Template, 12, 0, \"div\", 15);\n            i0.ɵɵtemplate(19, AiChatComponent_div_19_Template, 3, 2, \"div\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(20, AiChatComponent_div_20_Template, 3, 1, \"div\", 17);\n            i0.ɵɵelementStart(21, \"div\", 18)(22, \"form\", 19);\n            i0.ɵɵlistener(\"ngSubmit\", function AiChatComponent_Template_form_ngSubmit_22_listener() {\n              return ctx.onSendMessage();\n            });\n            i0.ɵɵtemplate(23, AiChatComponent_div_23_Template, 5, 1, \"div\", 20);\n            i0.ɵɵelementStart(24, \"div\", 21)(25, \"textarea\", 22);\n            i0.ɵɵlistener(\"keypress\", function AiChatComponent_Template_textarea_keypress_25_listener($event) {\n              return ctx.onKeyPress($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"button\", 23);\n            i0.ɵɵtemplate(27, AiChatComponent_i_27_Template, 1, 0, \"i\", 24);\n            i0.ɵɵtemplate(28, AiChatComponent_div_28_Template, 3, 0, \"div\", 25);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"div\", 26)(30, \"small\", 27);\n            i0.ɵɵtext(31);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            let tmp_12_0;\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"disabled\", ctx.isSending);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentConversation);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.messages.length === 0 && !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.messages.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.chatForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.currentConversation);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isSending);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.chatForm.invalid || ctx.isSending);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isSending);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isSending);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ((tmp_12_0 = ctx.chatForm.get(\"message\")) == null ? null : tmp_12_0.value == null ? null : tmp_12_0.value.length) || 0, \"/2000 \");\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n        styles: [\".ai-chat-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:calc(100vh - 120px);background:#f8f9fa}.chat-header[_ngcontent-%COMP%]{background:white;padding:1rem;border-bottom:1px solid #dee2e6;box-shadow:0 2px 4px #0000001a}.conversation-info[_ngcontent-%COMP%]{font-size:.9rem}.messages-container[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1rem;background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%)}.welcome-message[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.messages-list[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}.message[_ngcontent-%COMP%]{margin-bottom:1.5rem}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{margin-left:auto;margin-right:0;max-width:70%;background:#007bff;color:#fff}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-avatar[_ngcontent-%COMP%]{background:#0056b3}.message.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{margin-left:0;margin-right:auto;max-width:80%;background:white;color:#333;border:1px solid #dee2e6}.message.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-avatar[_ngcontent-%COMP%]{background:#28a745;color:#fff}.message-content[_ngcontent-%COMP%]{border-radius:1rem;padding:1rem;box-shadow:0 2px 8px #0000001a}.message-header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.5rem}.message-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:.75rem;font-size:.9rem}.message-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.message-sender[_ngcontent-%COMP%]{font-weight:600;font-size:.9rem}.message-time[_ngcontent-%COMP%]{font-size:.75rem;opacity:.7}.message-text[_ngcontent-%COMP%]{line-height:1.5;white-space:pre-wrap;word-wrap:break-word}.typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background:#6c757d;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3){animation-delay:0s}@keyframes _ngcontent-%COMP%_typing{0%,80%,to{transform:scale(.8);opacity:.5}40%{transform:scale(1);opacity:1}}.chat-input[_ngcontent-%COMP%]{background:white;padding:1rem;border-top:1px solid #dee2e6;box-shadow:0 -2px 8px #0000001a}.conversation-type-selector[_ngcontent-%COMP%]{max-width:300px}.input-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:none;border-radius:1rem 0 0 1rem}.input-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 .2rem #007bff40}.input-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-radius:0 1rem 1rem 0;min-width:60px}@media (max-width: 768px){.ai-chat-container[_ngcontent-%COMP%]{height:calc(100vh - 80px)}.chat-header[_ngcontent-%COMP%]{padding:.75rem}.chat-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.1rem}.chat-header[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%]{font-size:.8rem;padding:.25rem .5rem}.messages-container[_ngcontent-%COMP%]{padding:.75rem}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%], .message.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:90%}.message-content[_ngcontent-%COMP%], .chat-input[_ngcontent-%COMP%]{padding:.75rem}}@media (prefers-color-scheme: dark){.ai-chat-container[_ngcontent-%COMP%]{background:#1a1a1a}.chat-header[_ngcontent-%COMP%]{background:#2d2d2d;border-bottom-color:#404040;color:#e9ecef}.messages-container[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1a1a1a 0%,#2d2d2d 100%)}.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:#2d2d2d;border-color:#404040;color:#e9ecef}.chat-input[_ngcontent-%COMP%]{background:#2d2d2d;border-top-color:#404040}}\"]\n      });\n    }\n  }\n  return AiChatComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}