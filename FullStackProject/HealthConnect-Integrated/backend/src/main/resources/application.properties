# Application Configuration
spring.application.name=healthconnect-backend
server.port=8080

# H2 Database Configuration
spring.datasource.url=jdbc:h2:mem:healthconnect;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=password
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console (for debugging)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=true

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=never

# JWT Configuration
jwt.secret=mySecretKeyForHealthConnectPlatformThatIsLongEnoughForSecurity
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# Google Gemini AI Configuration
google.ai.api.key=AIzaSyCFWc5GegGFmlMpD7-gNyfXO_eiU24PklQ
google.ai.model=gemini-1.5-flash

# Logging Configuration
logging.level.com.healthconnect=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web.cors=DEBUG

# Error handling
server.error.include-message=always
server.error.include-binding-errors=always
