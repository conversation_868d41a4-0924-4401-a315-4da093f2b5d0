/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/HealthConnectApplication.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/config/JwtAuthenticationFilter.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/config/SecurityConfig.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/config/WebClientConfig.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/config/WebSocketConfig.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/AiHealthBotController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/AppointmentController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/AuthController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/ChatController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/DebugController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/DigitalPrescriptionController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/DoctorController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/InsuranceController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/InternationalizationController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/SymptomQuestionnaireController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/TestController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/UserController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/controller/WebSocketController.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/AiChatRequest.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/AiChatResponse.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/AiConversationResponse.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/AiMessageResponse.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/AppointmentChatRequest.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/AppointmentRequest.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/AppointmentResponse.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/AppointmentUpdateRequest.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/AuthResponse.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/ChatRequest.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/ChatResponse.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/LoginRequest.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/MessageRequest.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/MessageResponse.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/RegisterRequest.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/SymptomAnalysisRequest.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/SymptomAnalysisResponse.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/TimeSlotResponse.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/UpdateProfileRequest.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/dto/UserResponse.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/AiConversation.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/AiMessage.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/Appointment.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/AppointmentStatus.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/AppointmentType.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/AvailabilityStatus.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/Chat.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/ChatStatus.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/ChatType.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/DigitalPrescription.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/DoctorAvailability.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/Message.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/MessageStatus.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/MessageType.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/PrescriptionMedication.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/SymptomAnalysis.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/SymptomQuestionnaire.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/User.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/UserRole.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/entity/VideoConsultation.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/AiConversationRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/AiMessageRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/AppointmentRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/ChatRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/DigitalPrescriptionRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/DoctorAvailabilityRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/MessageRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/SymptomAnalysisRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/SymptomQuestionnaireRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/UserRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/repository/VideoConsultationRepository.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/AiHealthBotService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/AppointmentService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/AuthService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/ChatService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/DataInitializationService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/DigitalPrescriptionService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/DoctorAvailabilityService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/GeminiApiService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/InsuranceService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/InternationalizationService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/JwtService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/NotificationService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/SymptomAnalysisService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/SymptomQuestionnaireService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/UserService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/VideoConsultationService.java
/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/backend/src/main/java/com/healthconnect/service/WebRTCService.java
